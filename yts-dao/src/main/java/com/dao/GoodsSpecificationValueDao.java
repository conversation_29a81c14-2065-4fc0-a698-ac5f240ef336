package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.GoodsSpecificationValue;
import com.domain.complex.GoodsSpecificationValueQuery;
/**
 * 商品规格值表持久层
 *
 * @date 2025-07-27 21:26:17
 */
@Mapper
public interface GoodsSpecificationValueDao {
    List<GoodsSpecificationValue> select(GoodsSpecificationValueQuery goodsSpecificationValueQuery);

    Integer insert(GoodsSpecificationValue goodsSpecificationValue);

    Integer updateById(GoodsSpecificationValue goodsSpecificationValue);

    List<GoodsSpecificationValue> selectAll(GoodsSpecificationValueQuery goodsSpecificationValueQuery);

    Integer count(GoodsSpecificationValueQuery goodsSpecificationValueQuery);

    GoodsSpecificationValue selectById(Long id);

    List<GoodsSpecificationValue> selectByIds(List<Long> ids);

    Integer insertBatch(List<GoodsSpecificationValue> goodsSpecificationValues);

    Integer deleteById(Long id);

}
