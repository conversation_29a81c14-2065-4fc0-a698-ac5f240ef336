package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.Order;
import com.domain.complex.OrderQuery;
/**
 * 订单表持久层
 *
 * @date 2025-07-24 23:22:31
 */
@Mapper
public interface OrderDao {
    List<Order> select(OrderQuery orderQuery);

    Integer insert(Order order);

    Integer updateById(Order order);

    List<Order> selectAll(OrderQuery orderQuery);

    Integer count(OrderQuery orderQuery);

    Order selectById(Long id);

    List<Order> selectByIds(List<Long> ids);

    Integer insertBatch(List<Order> orders);

    Order selectByCode(String orderNo);
}
