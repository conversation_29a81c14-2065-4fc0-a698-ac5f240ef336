package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.File;
import com.domain.complex.FileQuery;
/**
 * 文件表持久层
 *
 * @date 2025-07-16 21:03:32
 */
@Mapper
public interface FileDao {
    List<File> select(FileQuery fileQuery);

    Integer insert(File file);

    Integer updateById(File file);

    List<File> selectAll(FileQuery fileQuery);

    Integer count(FileQuery fileQuery);

    File selectById(Long id);

    List<File> selectByIds(List<Long> ids);

    Integer insertBatch(List<File> files);

    File selectByUrl(String url);
}
