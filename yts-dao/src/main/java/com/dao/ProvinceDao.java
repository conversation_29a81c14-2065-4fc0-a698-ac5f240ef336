package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.Province;
import com.domain.complex.ProvinceQuery;
/**
 * 省份表持久层
 *
 * @date 2025-07-23 22:58:36
 */
@Mapper
public interface ProvinceDao {
    List<Province> select(ProvinceQuery provinceQuery);

    Integer insert(Province province);

    Integer updateById(Province province);

    List<Province> selectAll(ProvinceQuery provinceQuery);

    Integer count(ProvinceQuery provinceQuery);

    Province selectById(Long id);

    List<Province> selectByIds(List<Long> ids);

    Integer insertBatch(List<Province> provinces);

}
