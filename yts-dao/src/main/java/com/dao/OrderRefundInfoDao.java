package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.OrderRefundInfo;
import com.domain.complex.OrderRefundInfoQuery;
/**
 * 订单退款记录表持久层
 *
 * @date 2025-07-29 22:28:19
 */
@Mapper
public interface OrderRefundInfoDao {
    List<OrderRefundInfo> select(OrderRefundInfoQuery orderRefundInfoQuery);

    Integer insert(OrderRefundInfo orderRefundInfo);

    Integer updateById(OrderRefundInfo orderRefundInfo);

    List<OrderRefundInfo> selectAll(OrderRefundInfoQuery orderRefundInfoQuery);

    Integer count(OrderRefundInfoQuery orderRefundInfoQuery);

    OrderRefundInfo selectById(Long id);

    List<OrderRefundInfo> selectByIds(List<Long> ids);

    Integer insertBatch(List<OrderRefundInfo> orderRefundInfos);

}
