package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.OrderProduct;
import com.domain.complex.OrderProductQuery;
/**
 * 订单产品表持久层
 *
 * @date 2025-07-24 23:22:31
 */
@Mapper
public interface OrderProductDao {
    List<OrderProduct> select(OrderProductQuery orderProductQuery);

    Integer insert(OrderProduct orderProduct);

    Integer updateById(OrderProduct orderProduct);

    List<OrderProduct> selectAll(OrderProductQuery orderProductQuery);

    Integer count(OrderProductQuery orderProductQuery);

    OrderProduct selectById(Long id);

    List<OrderProduct> selectByIds(List<Long> ids);

    Integer insertBatch(List<OrderProduct> orderProducts);

}
