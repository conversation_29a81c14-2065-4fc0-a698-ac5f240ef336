package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.UserTeam;
import com.domain.complex.UserTeamQuery;
/**
 * 用户团队表持久层
 *
 * @date 2025-07-19 10:58:27
 */
@Mapper
public interface UserTeamDao {
    List<UserTeam> select(UserTeamQuery userTeamQuery);

    Integer insert(UserTeam userTeam);

    Integer updateById(UserTeam userTeam);

    List<UserTeam> selectAll(UserTeamQuery userTeamQuery);

    Integer count(UserTeamQuery userTeamQuery);

    UserTeam selectById(Long id);

    List<UserTeam> selectByIds(List<Long> ids);

    Integer insertBatch(List<UserTeam> userTeams);

}
