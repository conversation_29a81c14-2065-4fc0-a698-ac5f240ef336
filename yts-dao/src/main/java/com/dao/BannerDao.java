package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.Banner;
import com.domain.complex.BannerQuery;
/**
 * 轮播图表持久层
 *
 * @date 2025-07-19 10:58:28
 */
@Mapper
public interface BannerDao {
    List<Banner> select(BannerQuery bannerQuery);

    Integer insert(Banner banner);

    Integer updateById(Banner banner);

    List<Banner> selectAll(BannerQuery bannerQuery);

    Integer count(BannerQuery bannerQuery);

    Banner selectById(Long id);

    List<Banner> selectByIds(List<Long> ids);

    Integer insertBatch(List<Banner> banners);

}
