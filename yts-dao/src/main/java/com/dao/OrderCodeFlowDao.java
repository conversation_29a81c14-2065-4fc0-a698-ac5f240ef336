package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.OrderCodeFlow;
import com.domain.complex.OrderCodeFlowQuery;
/**
 * 订单号流水表持久层
 *
 * @date 2025-07-22 23:08:11
 */
@Mapper
public interface OrderCodeFlowDao {
    List<OrderCodeFlow> select(OrderCodeFlowQuery orderCodeFlowQuery);

    Integer insert(OrderCodeFlow orderCodeFlow);

    Integer updateById(OrderCodeFlow orderCodeFlow);

    List<OrderCodeFlow> selectAll(OrderCodeFlowQuery orderCodeFlowQuery);

    Integer count(OrderCodeFlowQuery orderCodeFlowQuery);

    OrderCodeFlow selectById(Long id);

    List<OrderCodeFlow> selectByIds(List<Long> ids);

    Integer insertBatch(List<OrderCodeFlow> orderCodeFlows);

}
