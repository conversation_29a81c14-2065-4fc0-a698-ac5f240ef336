package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.ReportGoods;
import com.domain.complex.ReportGoodsQuery;
/**
 * 统计商品表持久层
 *
 * @date 2025-07-20 16:36:02
 */
@Mapper
public interface ReportGoodsDao {
    List<ReportGoods> select(ReportGoodsQuery reportGoodsQuery);

    Integer insert(ReportGoods reportGoods);

    Integer updateById(ReportGoods reportGoods);

    List<ReportGoods> selectAll(ReportGoodsQuery reportGoodsQuery);

    Integer count(ReportGoodsQuery reportGoodsQuery);

    ReportGoods selectById(Long id);

    List<ReportGoods> selectByIds(List<Long> ids);

    Integer insertBatch(List<ReportGoods> reportGoodss);

}
