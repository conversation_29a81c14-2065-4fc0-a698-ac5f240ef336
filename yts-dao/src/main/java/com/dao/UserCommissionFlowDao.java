package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.UserCommissionFlow;
import com.domain.complex.UserCommissionFlowQuery;
/**
 * 用户佣金明细表持久层
 *
 * @date 2025-07-27 11:32:54
 */
@Mapper
public interface UserCommissionFlowDao {
    List<UserCommissionFlow> select(UserCommissionFlowQuery userCommissionFlowQuery);

    Integer insert(UserCommissionFlow userCommissionFlow);

    Integer updateById(UserCommissionFlow userCommissionFlow);

    List<UserCommissionFlow> selectAll(UserCommissionFlowQuery userCommissionFlowQuery);

    Integer count(UserCommissionFlowQuery userCommissionFlowQuery);

    UserCommissionFlow selectById(Long id);

    List<UserCommissionFlow> selectByIds(List<Long> ids);

    Integer insertBatch(List<UserCommissionFlow> userCommissionFlows);

}
