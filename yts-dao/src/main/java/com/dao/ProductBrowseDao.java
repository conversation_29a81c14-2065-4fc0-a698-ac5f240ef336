package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.ProductBrowse;
import com.domain.complex.ProductBrowseQuery;
/**
 * 产品浏览表持久层
 *
 * @date 2025-07-20 16:36:02
 */
@Mapper
public interface ProductBrowseDao {
    List<ProductBrowse> select(ProductBrowseQuery productBrowseQuery);

    Integer insert(ProductBrowse productBrowse);

    Integer updateById(ProductBrowse productBrowse);

    List<ProductBrowse> selectAll(ProductBrowseQuery productBrowseQuery);

    Integer count(ProductBrowseQuery productBrowseQuery);

    ProductBrowse selectById(Long id);

    List<ProductBrowse> selectByIds(List<Long> ids);

    Integer insertBatch(List<ProductBrowse> productBrowses);

}
