package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.UserCommission;
import com.domain.complex.UserCommissionQuery;
/**
 * 用户佣金表持久层
 *
 * @date 2025-07-27 11:32:54
 */
@Mapper
public interface UserCommissionDao {
    List<UserCommission> select(UserCommissionQuery userCommissionQuery);

    Integer insert(UserCommission userCommission);

    Integer updateById(UserCommission userCommission);

    List<UserCommission> selectAll(UserCommissionQuery userCommissionQuery);

    Integer count(UserCommissionQuery userCommissionQuery);

    UserCommission selectById(Long id);

    List<UserCommission> selectByIds(List<Long> ids);

    Integer insertBatch(List<UserCommission> userCommissions);

    UserCommission selectByUserId(Long userId);
}
