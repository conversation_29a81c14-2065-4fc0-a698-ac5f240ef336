package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.ServiceInfo;
import com.domain.complex.ServiceInfoQuery;
/**
 * 服务信息表持久层
 *
 * @date 2025-07-19 10:58:27
 */
@Mapper
public interface ServiceInfoDao {
    List<ServiceInfo> select(ServiceInfoQuery serviceInfoQuery);

    Integer insert(ServiceInfo serviceInfo);

    Integer updateById(ServiceInfo serviceInfo);

    List<ServiceInfo> selectAll(ServiceInfoQuery serviceInfoQuery);

    Integer count(ServiceInfoQuery serviceInfoQuery);

    ServiceInfo selectById(Long id);

    List<ServiceInfo> selectByIds(List<Long> ids);

    Integer insertBatch(List<ServiceInfo> serviceInfos);

}
