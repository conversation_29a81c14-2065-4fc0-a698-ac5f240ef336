package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Log;
import com.domain.complex.LogQuery;
/**
 * 系统用户日志表持久层
 *
 * @date 2025-07-20 18:59:08
 */
@Mapper
public interface LogDao {
    List<Log> select(LogQuery logQuery);

    Integer insert(Log log);

    Integer updateById(Log log);

    List<Log> selectAll(LogQuery logQuery);

    Integer count(LogQuery logQuery);

    Log selectById(Long id);

    List<Log> selectByIds(List<Long> ids);

    Integer insertBatch(List<Log> logs);

    Integer deleteById(Long id);
}
