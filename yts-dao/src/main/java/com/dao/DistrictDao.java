package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.District;
import com.domain.complex.DistrictQuery;
/**
 * 区(县)表持久层
 *
 * @date 2025-07-23 22:58:35
 */
@Mapper
public interface DistrictDao {
    List<District> select(DistrictQuery districtQuery);

    Integer insert(District district);

    Integer updateById(District district);

    List<District> selectAll(DistrictQuery districtQuery);

    Integer count(DistrictQuery districtQuery);

    District selectById(Long id);

    List<District> selectByIds(List<Long> ids);

    Integer insertBatch(List<District> districts);

}
