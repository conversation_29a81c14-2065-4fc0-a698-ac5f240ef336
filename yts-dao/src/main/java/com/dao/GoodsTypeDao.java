package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.GoodsType;
import com.domain.complex.GoodsTypeQuery;
/**
 * 商品分类表持久层
 *
 * @date 2025-07-20 10:32:17
 */
@Mapper
public interface GoodsTypeDao {
    List<GoodsType> select(GoodsTypeQuery goodsTypeQuery);

    Integer insert(GoodsType goodsType);

    Integer updateById(GoodsType goodsType);

    List<GoodsType> selectAll(GoodsTypeQuery goodsTypeQuery);

    Integer count(GoodsTypeQuery goodsTypeQuery);

    GoodsType selectById(Long id);

    List<GoodsType> selectByIds(List<Long> ids);

    Integer insertBatch(List<GoodsType> goodsTypes);

}
