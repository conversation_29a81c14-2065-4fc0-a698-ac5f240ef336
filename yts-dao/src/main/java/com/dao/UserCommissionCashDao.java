package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.UserCommissionCash;
import com.domain.complex.UserCommissionCashQuery;
/**
 * 用户佣金提现表持久层
 *
 * @date 2025-07-19 10:58:27
 */
@Mapper
public interface UserCommissionCashDao {
    List<UserCommissionCash> select(UserCommissionCashQuery userCommissionCashQuery);

    Integer insert(UserCommissionCash userCommissionCash);

    Integer updateById(UserCommissionCash userCommissionCash);

    List<UserCommissionCash> selectAll(UserCommissionCashQuery userCommissionCashQuery);

    Integer count(UserCommissionCashQuery userCommissionCashQuery);

    UserCommissionCash selectById(Long id);

    List<UserCommissionCash> selectByIds(List<Long> ids);

    Integer insertBatch(List<UserCommissionCash> userCommissionCashs);

}
