package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.UserAddress;
import com.domain.complex.UserAddressQuery;
/**
 * 用户地址表持久层
 *
 * @date 2025-07-23 22:58:35
 */
@Mapper
public interface UserAddressDao {
    List<UserAddress> select(UserAddressQuery userAddressQuery);

    Integer insert(UserAddress userAddress);

    Integer updateById(UserAddress userAddress);

    List<UserAddress> selectAll(UserAddressQuery userAddressQuery);

    Integer count(UserAddressQuery userAddressQuery);

    UserAddress selectById(Long id);

    List<UserAddress> selectByIds(List<Long> ids);

    Integer insertBatch(List<UserAddress> userAddresss);

}
