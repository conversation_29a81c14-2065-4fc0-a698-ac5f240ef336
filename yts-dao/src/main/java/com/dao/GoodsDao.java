package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.Goods;
import com.domain.complex.GoodsQuery;
/**
 * 商品表持久层
 *
 * @date 2025-07-24 23:22:31
 */
@Mapper
public interface GoodsDao {
    List<Goods> select(GoodsQuery goodsQuery);

    Integer insert(Goods goods);

    Integer updateById(Goods goods);

    List<Goods> selectAll(GoodsQuery goodsQuery);

    Integer count(GoodsQuery goodsQuery);

    Goods selectById(Long id);

    List<Goods> selectByIds(List<Long> ids);

    Integer insertBatch(List<Goods> goodss);

}
