package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.SysUser;
import com.domain.complex.SysUserQuery;
/**
 * 系统用户表持久层
 *
 * @date 2025-07-16 21:03:31
 */
@Mapper
public interface SysUserDao {
    List<SysUser> select(SysUserQuery sysUserQuery);

    Integer insert(SysUser sysUser);

    Integer updateById(SysUser sysUser);

    List<SysUser> selectAll(SysUserQuery sysUserQuery);

    Integer count(SysUserQuery sysUserQuery);

    SysUser selectById(Long id);

    List<SysUser> selectByIds(List<Long> ids);

    Integer insertBatch(List<SysUser> sysUsers);

}
