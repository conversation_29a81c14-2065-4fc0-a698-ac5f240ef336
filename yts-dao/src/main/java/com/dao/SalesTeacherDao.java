package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.SalesTeacher;
import com.domain.complex.SalesTeacherQuery;
/**
 * 销售老师表持久层
 *
 * @date 2025-07-24 23:22:31
 */
@Mapper
public interface SalesTeacherDao {
    List<SalesTeacher> select(SalesTeacherQuery salesTeacherQuery);

    Integer insert(SalesTeacher salesTeacher);

    Integer updateById(SalesTeacher salesTeacher);

    List<SalesTeacher> selectAll(SalesTeacherQuery salesTeacherQuery);

    Integer count(SalesTeacherQuery salesTeacherQuery);

    SalesTeacher selectById(Long id);

    List<SalesTeacher> selectByIds(List<Long> ids);

    Integer insertBatch(List<SalesTeacher> salesTeachers);

}
