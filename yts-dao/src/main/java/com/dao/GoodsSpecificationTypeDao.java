package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.GoodsSpecificationType;
import com.domain.complex.GoodsSpecificationTypeQuery;
/**
 * 商品规格类型表持久层
 *
 * @date 2025-07-27 21:26:17
 */
@Mapper
public interface GoodsSpecificationTypeDao {
    List<GoodsSpecificationType> select(GoodsSpecificationTypeQuery goodsSpecificationTypeQuery);

    Integer insert(GoodsSpecificationType goodsSpecificationType);

    Integer updateById(GoodsSpecificationType goodsSpecificationType);

    List<GoodsSpecificationType> selectAll(GoodsSpecificationTypeQuery goodsSpecificationTypeQuery);

    Integer count(GoodsSpecificationTypeQuery goodsSpecificationTypeQuery);

    GoodsSpecificationType selectById(Long id);

    List<GoodsSpecificationType> selectByIds(List<Long> ids);

    Integer insertBatch(List<GoodsSpecificationType> goodsSpecificationTypes);

    Integer deleteById(Long id);
}
