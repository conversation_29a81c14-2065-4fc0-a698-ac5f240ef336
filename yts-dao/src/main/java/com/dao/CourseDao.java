package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.Course;
import com.domain.complex.CourseQuery;
/**
 * 课程表持久层
 *
 * @date 2025-07-19 10:58:28
 */
@Mapper
public interface CourseDao {
    List<Course> select(CourseQuery courseQuery);

    Integer insert(Course course);

    Integer updateById(Course course);

    List<Course> selectAll(CourseQuery courseQuery);

    Integer count(CourseQuery courseQuery);

    Course selectById(Long id);

    List<Course> selectByIds(List<Long> ids);

    Integer insertBatch(List<Course> courses);

}
