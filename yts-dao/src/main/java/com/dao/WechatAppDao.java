package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.WechatApp;
import com.domain.complex.WechatAppQuery;
/**
 * 微信应用表持久层
 *
 * @date 2025-07-16 21:29:32
 */
@Mapper
public interface WechatAppDao {
    List<WechatApp> select(WechatAppQuery wechatAppQuery);

    Integer insert(WechatApp wechatApp);

    Integer updateById(WechatApp wechatApp);

    List<WechatApp> selectAll(WechatAppQuery wechatAppQuery);

    Integer count(WechatAppQuery wechatAppQuery);

    WechatApp selectById(Long id);

    List<WechatApp> selectByIds(List<Long> ids);

    Integer insertBatch(List<WechatApp> wechatApps);

}
