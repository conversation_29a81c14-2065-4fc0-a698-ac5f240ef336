package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.User;
import com.domain.complex.UserQuery;
/**
 * 用户表持久层
 *
 * @date 2025-07-24 23:22:31
 */
@Mapper
public interface UserDao {
    List<User> select(UserQuery userQuery);

    Integer insert(User user);

    Integer updateById(User user);

    List<User> selectAll(UserQuery userQuery);

    Integer count(UserQuery userQuery);

    User selectById(Long id);

    List<User> selectByIds(List<Long> ids);

    Integer insertBatch(List<User> users);

    User selectByUnionId(String unionId);

    User selectByOpenId(String openid);
}
