package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.domain.GoodsSpecificationSku;
import com.domain.complex.GoodsSpecificationSkuQuery;
/**
 * 商品规格sku表持久层
 *
 * @date 2025-07-27 21:26:17
 */
@Mapper
public interface GoodsSpecificationSkuDao {
    List<GoodsSpecificationSku> select(GoodsSpecificationSkuQuery goodsSpecificationSkuQuery);

    Integer insert(GoodsSpecificationSku goodsSpecificationSku);

    Integer updateById(GoodsSpecificationSku goodsSpecificationSku);

    List<GoodsSpecificationSku> selectAll(GoodsSpecificationSkuQuery goodsSpecificationSkuQuery);

    Integer count(GoodsSpecificationSkuQuery goodsSpecificationSkuQuery);

    GoodsSpecificationSku selectById(Long id);

    List<GoodsSpecificationSku> selectByIds(List<Long> ids);

    Integer insertBatch(List<GoodsSpecificationSku> goodsSpecificationSkus);

    Integer deleteById(Long id);

    GoodsSpecificationSku findBySpecValuesAndGoodsId(@Param("specValues") String specValues , @Param("goodsId") Long goodsId);

    Integer updateStockById(GoodsSpecificationSku goodsSpecificationSku);
}
