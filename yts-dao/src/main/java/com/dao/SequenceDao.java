package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.Sequence;
import com.domain.complex.SequenceQuery;
/**
 * 序列表持久层
 *
 * @date 2025-07-16 21:29:32
 */
@Mapper
public interface SequenceDao {
    List<Sequence> select(SequenceQuery sequenceQuery);

    Integer insert(Sequence sequence);

    Integer updateById(Sequence sequence);

    List<Sequence> selectAll(SequenceQuery sequenceQuery);

    Integer count(SequenceQuery sequenceQuery);

    Sequence selectById(Long id);

    List<Sequence> selectByIds(List<Long> ids);

    Integer insertBatch(List<Sequence> sequences);

}
