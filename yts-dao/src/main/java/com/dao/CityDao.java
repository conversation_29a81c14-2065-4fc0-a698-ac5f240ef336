package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.City;
import com.domain.complex.CityQuery;
/**
 * 城市表持久层
 *
 * @date 2025-07-23 22:58:35
 */
@Mapper
public interface CityDao {
    List<City> select(CityQuery cityQuery);

    Integer insert(City city);

    Integer updateById(City city);

    List<City> selectAll(CityQuery cityQuery);

    Integer count(CityQuery cityQuery);

    City selectById(Long id);

    List<City> selectByIds(List<Long> ids);

    Integer insertBatch(List<City> citys);

}
