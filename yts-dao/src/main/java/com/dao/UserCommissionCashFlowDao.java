package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.UserCommissionCashFlow;
import com.domain.complex.UserCommissionCashFlowQuery;
/**
 * 用户佣金提现明细表持久层
 *
 * @date 2025-07-19 10:58:27
 */
@Mapper
public interface UserCommissionCashFlowDao {
    List<UserCommissionCashFlow> select(UserCommissionCashFlowQuery userCommissionCashFlowQuery);

    Integer insert(UserCommissionCashFlow userCommissionCashFlow);

    Integer updateById(UserCommissionCashFlow userCommissionCashFlow);

    List<UserCommissionCashFlow> selectAll(UserCommissionCashFlowQuery userCommissionCashFlowQuery);

    Integer count(UserCommissionCashFlowQuery userCommissionCashFlowQuery);

    UserCommissionCashFlow selectById(Long id);

    List<UserCommissionCashFlow> selectByIds(List<Long> ids);

    Integer insertBatch(List<UserCommissionCashFlow> userCommissionCashFlows);

}
