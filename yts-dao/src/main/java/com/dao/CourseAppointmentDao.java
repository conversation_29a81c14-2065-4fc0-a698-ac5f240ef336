package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.CourseAppointment;
import com.domain.complex.CourseAppointmentQuery;
/**
 * 课程预约表持久层
 *
 * @date 2025-07-19 10:58:28
 */
@Mapper
public interface CourseAppointmentDao {
    List<CourseAppointment> select(CourseAppointmentQuery courseAppointmentQuery);

    Integer insert(CourseAppointment courseAppointment);

    Integer updateById(CourseAppointment courseAppointment);

    List<CourseAppointment> selectAll(CourseAppointmentQuery courseAppointmentQuery);

    Integer count(CourseAppointmentQuery courseAppointmentQuery);

    CourseAppointment selectById(Long id);

    List<CourseAppointment> selectByIds(List<Long> ids);

    Integer insertBatch(List<CourseAppointment> courseAppointments);

}
