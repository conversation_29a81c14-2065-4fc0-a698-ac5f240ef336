package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.ServiceAppointment;
import com.domain.complex.ServiceAppointmentQuery;
/**
 * 服务预约表持久层
 *
 * @date 2025-07-19 10:58:27
 */
@Mapper
public interface ServiceAppointmentDao {
    List<ServiceAppointment> select(ServiceAppointmentQuery serviceAppointmentQuery);

    Integer insert(ServiceAppointment serviceAppointment);

    Integer updateById(ServiceAppointment serviceAppointment);

    List<ServiceAppointment> selectAll(ServiceAppointmentQuery serviceAppointmentQuery);

    Integer count(ServiceAppointmentQuery serviceAppointmentQuery);

    ServiceAppointment selectById(Long id);

    List<ServiceAppointment> selectByIds(List<Long> ids);

    Integer insertBatch(List<ServiceAppointment> serviceAppointments);

}
