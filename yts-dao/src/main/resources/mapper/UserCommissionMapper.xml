<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.UserCommissionDao">

    <resultMap id="userCommissionMap" type="com.domain.UserCommission">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="amount" property="amount"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="totalAmount != null">
                and total_amount = #{totalAmount}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="userCommissionMap" parameterType="com.domain.complex.UserCommissionQuery">
        select * from tb_user_commission
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="userCommissionMap" parameterType="com.domain.complex.UserCommissionQuery">
        select * from tb_user_commission
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.UserCommission">
        update tb_user_commission
        <trim prefix="set" suffixOverrides=",">
            <if test="userId != null">
               user_id = #{userId},
            </if>
            <if test="amount != null">
               amount = #{amount},
            </if>
            <if test="totalAmount != null">
               total_amount = #{totalAmount},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.UserCommission" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_user_commission
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="totalAmount != null">
                total_amount,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="totalAmount != null">
                #{totalAmount},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.UserCommissionQuery">
        select count(*) from tb_user_commission
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="userCommissionMap">
        select * from tb_user_commission where id = #{id}
    </select>

    <select id="selectByUserId" resultMap="userCommissionMap">
        select * from tb_user_commission where user_id = #{userId}
    </select>

    <select id="selectByIds" resultMap="userCommissionMap">
        select * from tb_user_commission where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_user_commission
        (
        user_id,
        amount,
        total_amount,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.userId},
        #{item.amount},
        #{item.totalAmount},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>