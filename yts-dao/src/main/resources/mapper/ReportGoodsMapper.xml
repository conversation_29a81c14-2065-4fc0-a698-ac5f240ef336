<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.ReportGoodsDao">

    <resultMap id="reportGoodsMap" type="com.domain.ReportGoods">
        <id column="id" property="id"/>
        <result column="date" property="date"/>
        <result column="goods_id" property="goodsId"/>
        <result column="browse_num" property="browseNum"/>
        <result column="pay_num" property="payNum"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="refund_num" property="refundNum"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="date != null">
                and date = #{date}
            </if>
            <if test="minDate != null">
                and date <![CDATA[ >= ]]> #{minDate}
            </if>
            <if test="maxDate != null">
                and date <![CDATA[ < ]]> #{maxDate}
            </if>
            <if test="goodsId != null">
                and goods_id = #{goodsId}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="browseNum != null">
                and browse_num = #{browseNum}
            </if>
            <if test="payNum != null">
                and pay_num = #{payNum}
            </if>
            <if test="payAmount != null">
                and pay_amount = #{payAmount}
            </if>
            <if test="refundNum != null">
                and refund_num = #{refundNum}
            </if>
            <if test="refundAmount != null">
                and refund_amount = #{refundAmount}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="reportGoodsMap" parameterType="com.domain.complex.ReportGoodsQuery">
        select * from tb_report_goods
        <include refid="common_where_if"/>
        <if test="sortType != null">
            order by ${sortType}
        </if>
        <if test="sortType == null">
            order by id desc
        </if>
        limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="reportGoodsMap" parameterType="com.domain.complex.ReportGoodsQuery">
        select * from tb_report_goods
        <include refid="common_where_if"/>
        <if test="sortType != null">
            order by ${sortType}
        </if>
        <if test="sortType == null">
            order by id desc
        </if>
    </select>

    <update id="updateById" parameterType="com.domain.ReportGoods">
        update tb_report_goods
        <trim prefix="set" suffixOverrides=",">
            <if test="date != null">
               date = #{date},
            </if>
            <if test="goodsId != null">
               goods_id = #{goodsId},
            </if>
            <if test="browseNum != null">
               browse_num = #{browseNum},
            </if>
            <if test="payNum != null">
               pay_num = #{payNum},
            </if>
            <if test="payAmount != null">
               pay_amount = #{payAmount},
            </if>
            <if test="refundNum != null">
               refund_num = #{refundNum},
            </if>
            <if test="refundAmount != null">
               refund_amount = #{refundAmount},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.ReportGoods" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_report_goods
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="date != null">
                date,
            </if>
            <if test="goodsId != null">
                goods_id,
            </if>
            <if test="browseNum != null">
                browse_num,
            </if>
            <if test="payNum != null">
                pay_num,
            </if>
            <if test="payAmount != null">
                pay_amount,
            </if>
            <if test="refundNum != null">
                refund_num,
            </if>
            <if test="refundAmount != null">
                refund_amount,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="date != null">
                #{date},
            </if>
            <if test="goodsId != null">
                #{goodsId},
            </if>
            <if test="browseNum != null">
                #{browseNum},
            </if>
            <if test="payNum != null">
                #{payNum},
            </if>
            <if test="payAmount != null">
                #{payAmount},
            </if>
            <if test="refundNum != null">
                #{refundNum},
            </if>
            <if test="refundAmount != null">
                #{refundAmount},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.ReportGoodsQuery">
        select count(*) from tb_report_goods
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="reportGoodsMap">
        select * from tb_report_goods where id = #{id}
    </select>

    <select id="selectByIds" resultMap="reportGoodsMap">
        select * from tb_report_goods where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_report_goods
        (
        date,
        goods_id,
        browse_num,
        pay_num,
        pay_amount,
        refund_num,
        refund_amount,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.date},
        #{item.goodsId},
        #{item.browseNum},
        #{item.payNum},
        #{item.payAmount},
        #{item.refundNum},
        #{item.refundAmount},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>