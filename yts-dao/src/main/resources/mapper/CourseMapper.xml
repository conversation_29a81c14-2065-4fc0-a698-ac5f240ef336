<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.CourseDao">

    <resultMap id="courseMap" type="com.domain.Course">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="img_url" property="imgUrl"/>
        <result column="length" property="length"/>
        <result column="detail_img_url" property="detailImgUrl"/>
        <result column="original_price" property="originalPrice"/>
        <result column="price" property="price"/>
        <result column="comment" property="comment"/>
        <result column="sales_volume" property="salesVolume"/>
        <result column="home" property="home"/>
        <result column="sort" property="sort"/>
        <result column="stage" property="stage"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="imgUrl != null and imgUrl != ''">
                and img_url = #{imgUrl}
            </if>
            <if test="length != null">
                and length = #{length}
            </if>
            <if test="detailImgUrl != null and detailImgUrl != ''">
                and detail_img_url = #{detailImgUrl}
            </if>
            <if test="originalPrice != null">
                and original_price = #{originalPrice}
            </if>
            <if test="price != null">
                and price = #{price}
            </if>
            <if test="comment != null and comment != ''">
                and comment = #{comment}
            </if>
            <if test="salesVolume != null and salesVolume != ''">
                and sales_volume = #{salesVolume}
            </if>
            <if test="home != null and home != ''">
                and home = #{home}
            </if>
            <if test="sort != null">
                and sort = #{sort}
            </if>
            <if test="stage != null and stage != ''">
                and stage = #{stage}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="courseMap" parameterType="com.domain.complex.CourseQuery">
        select * from tb_course
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="courseMap" parameterType="com.domain.complex.CourseQuery">
        select * from tb_course
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.Course">
        update tb_course
        <trim prefix="set" suffixOverrides=",">
            <if test="title != null">
               title = #{title},
            </if>
            <if test="imgUrl != null">
               img_url = #{imgUrl},
            </if>
            <if test="length != null">
               length = #{length},
            </if>
            <if test="detailImgUrl != null">
               detail_img_url = #{detailImgUrl},
            </if>
            <if test="originalPrice != null">
               original_price = #{originalPrice},
            </if>
            <if test="price != null">
               price = #{price},
            </if>
            <if test="comment != null">
               comment = #{comment},
            </if>
            <if test="salesVolume != null">
               sales_volume = #{salesVolume},
            </if>
            <if test="home != null">
               home = #{home},
            </if>
            <if test="sort != null">
               sort = #{sort},
            </if>
            <if test="stage != null">
               stage = #{stage},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.Course" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_course
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="imgUrl != null">
                img_url,
            </if>
            <if test="length != null">
                length,
            </if>
            <if test="detailImgUrl != null">
                detail_img_url,
            </if>
            <if test="originalPrice != null">
                original_price,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="comment != null">
                comment,
            </if>
            <if test="salesVolume != null">
                sales_volume,
            </if>
            <if test="home != null">
                home,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="stage != null">
                stage,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="title != null">
                #{title},
            </if>
            <if test="imgUrl != null">
                #{imgUrl},
            </if>
            <if test="length != null">
                #{length},
            </if>
            <if test="detailImgUrl != null">
                #{detailImgUrl},
            </if>
            <if test="originalPrice != null">
                #{originalPrice},
            </if>
            <if test="price != null">
                #{price},
            </if>
            <if test="comment != null">
                #{comment},
            </if>
            <if test="salesVolume != null">
                #{salesVolume},
            </if>
            <if test="home != null">
                #{home},
            </if>
            <if test="sort != null">
                #{sort},
            </if>
            <if test="stage != null">
                #{stage},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.CourseQuery">
        select count(*) from tb_course
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="courseMap">
        select * from tb_course where id = #{id}
    </select>

    <select id="selectByIds" resultMap="courseMap">
        select * from tb_course where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_course
        (
        title,
        img_url,
        length,
        detail_img_url,
        original_price,
        price,
        comment,
        sales_volume,
        home,
        sort,
        stage,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.title},
        #{item.imgUrl},
        #{item.length},
        #{item.detailImgUrl},
        #{item.originalPrice},
        #{item.price},
        #{item.comment},
        #{item.salesVolume},
        #{item.home},
        #{item.sort},
        #{item.stage},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>