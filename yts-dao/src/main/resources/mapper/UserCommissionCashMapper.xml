<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.UserCommissionCashDao">

    <resultMap id="userCommissionCashMap" type="com.domain.UserCommissionCash">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="sum_commission" property="sumCommission"/>
        <result column="order_size" property="orderSize"/>
        <result column="opt_sys_user_id" property="optSysUserId"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sumCommission != null">
                and sum_commission = #{sumCommission}
            </if>
            <if test="orderSize != null">
                and order_size = #{orderSize}
            </if>
            <if test="optSysUserId != null">
                and opt_sys_user_id = #{optSysUserId}
            </if>
            <if test="optSysUserIds != null and optSysUserIds.size > 0">
                and opt_sys_user_id in
                <foreach collection="optSysUserIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="userCommissionCashMap" parameterType="com.domain.complex.UserCommissionCashQuery">
        select * from tb_user_commission_cash
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="userCommissionCashMap" parameterType="com.domain.complex.UserCommissionCashQuery">
        select * from tb_user_commission_cash
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.UserCommissionCash">
        update tb_user_commission_cash
        <trim prefix="set" suffixOverrides=",">
            <if test="userId != null">
               user_id = #{userId},
            </if>
            <if test="sumCommission != null">
               sum_commission = #{sumCommission},
            </if>
            <if test="orderSize != null">
               order_size = #{orderSize},
            </if>
            <if test="optSysUserId != null">
               opt_sys_user_id = #{optSysUserId},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.UserCommissionCash" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_user_commission_cash
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="sumCommission != null">
                sum_commission,
            </if>
            <if test="orderSize != null">
                order_size,
            </if>
            <if test="optSysUserId != null">
                opt_sys_user_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="sumCommission != null">
                #{sumCommission},
            </if>
            <if test="orderSize != null">
                #{orderSize},
            </if>
            <if test="optSysUserId != null">
                #{optSysUserId},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.UserCommissionCashQuery">
        select count(*) from tb_user_commission_cash
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="userCommissionCashMap">
        select * from tb_user_commission_cash where id = #{id}
    </select>

    <select id="selectByIds" resultMap="userCommissionCashMap">
        select * from tb_user_commission_cash where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_user_commission_cash
        (
        user_id,
        sum_commission,
        order_size,
        opt_sys_user_id,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.userId},
        #{item.sumCommission},
        #{item.orderSize},
        #{item.optSysUserId},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>