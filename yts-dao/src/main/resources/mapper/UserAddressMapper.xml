<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.UserAddressDao">

    <resultMap id="userAddressMap" type="com.domain.UserAddress">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="province_id" property="provinceId"/>
        <result column="city_id" property="cityId"/>
        <result column="district_id" property="districtId"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="name" property="name"/>
        <result column="mobile" property="mobile"/>
        <result column="default_address" property="defaultAddress"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="provinceId != null">
                and province_id = #{provinceId}
            </if>
            <if test="provinceIds != null and provinceIds.size > 0">
                and province_id in
                <foreach collection="provinceIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cityId != null">
                and city_id = #{cityId}
            </if>
            <if test="cityIds != null and cityIds.size > 0">
                and city_id in
                <foreach collection="cityIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="districtId != null">
                and district_id = #{districtId}
            </if>
            <if test="districtIds != null and districtIds.size > 0">
                and district_id in
                <foreach collection="districtIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="detailAddress != null and detailAddress != ''">
                and detail_address = #{detailAddress}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="mobile != null and mobile != ''">
                and mobile = #{mobile}
            </if>
            <if test="defaultAddress != null and defaultAddress != ''">
                and default_address = #{defaultAddress}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="userAddressMap" parameterType="com.domain.complex.UserAddressQuery">
        select * from tb_user_address
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="userAddressMap" parameterType="com.domain.complex.UserAddressQuery">
        select * from tb_user_address
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.UserAddress">
        update tb_user_address
        <trim prefix="set" suffixOverrides=",">
            <if test="userId != null">
               user_id = #{userId},
            </if>
            <if test="provinceId != null">
               province_id = #{provinceId},
            </if>
            <if test="cityId != null">
               city_id = #{cityId},
            </if>
            <if test="districtId != null">
               district_id = #{districtId},
            </if>
            <if test="detailAddress != null">
               detail_address = #{detailAddress},
            </if>
            <if test="name != null">
               name = #{name},
            </if>
            <if test="mobile != null">
               mobile = #{mobile},
            </if>
            <if test="defaultAddress != null">
               default_address = #{defaultAddress},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.UserAddress" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_user_address
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="provinceId != null">
                province_id,
            </if>
            <if test="cityId != null">
                city_id,
            </if>
            <if test="districtId != null">
                district_id,
            </if>
            <if test="detailAddress != null">
                detail_address,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="defaultAddress != null">
                default_address,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="provinceId != null">
                #{provinceId},
            </if>
            <if test="cityId != null">
                #{cityId},
            </if>
            <if test="districtId != null">
                #{districtId},
            </if>
            <if test="detailAddress != null">
                #{detailAddress},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="mobile != null">
                #{mobile},
            </if>
            <if test="defaultAddress != null">
                #{defaultAddress},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.UserAddressQuery">
        select count(*) from tb_user_address
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="userAddressMap">
        select * from tb_user_address where id = #{id}
    </select>

    <select id="selectByIds" resultMap="userAddressMap">
        select * from tb_user_address where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_user_address
        (
        user_id,
        province_id,
        city_id,
        district_id,
        detail_address,
        name,
        mobile,
        default_address,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.userId},
        #{item.provinceId},
        #{item.cityId},
        #{item.districtId},
        #{item.detailAddress},
        #{item.name},
        #{item.mobile},
        #{item.defaultAddress},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>