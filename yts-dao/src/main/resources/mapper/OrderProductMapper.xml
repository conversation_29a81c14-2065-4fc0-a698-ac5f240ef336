<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.OrderProductDao">

    <resultMap id="orderProductMap" type="com.domain.OrderProduct">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="product_catalog" property="productCatalog"/>
        <result column="product_id" property="productId"/>
        <result column="number" property="number"/>
        <result column="amount" property="amount"/>
        <result column="product_name" property="productName"/>
        <result column="sku_id" property="skuId"/>
        <result column="spec_values" property="specValues"/>
        <result column="img_url" property="imgUrl"/>
        <result column="buying_price" property="buyingPrice"/>
        <result column="original_price" property="originalPrice"/>
        <result column="price" property="price"/>
        <result column="gross_profit" property="grossProfit"/>
        <result column="commission_ratio" property="commissionRatio"/>
        <result column="commission" property="commission"/>
        <result column="sales_teacher_commission_ratio" property="salesTeacherCommissionRatio"/>
        <result column="sales_teacher_commission" property="salesTeacherCommission"/>
        <result column="unit" property="unit"/>
        <result column="goods_catalog" property="goodsCatalog"/>
        <result column="goods_type_id" property="goodsTypeId"/>
        <result column="goods_type_name" property="goodsTypeName"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderId != null">
                and order_id = #{orderId}
            </if>
            <if test="orderIds != null and orderIds.size > 0">
                and order_id in
                <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productCatalog != null and productCatalog != ''">
                and product_catalog = #{productCatalog}
            </if>
            <if test="productId != null">
                and product_id = #{productId}
            </if>
            <if test="productIds != null and productIds.size > 0">
                and product_id in
                <foreach collection="productIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="number != null">
                and number = #{number}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="productName != null and productName != ''">
                and product_name = #{productName}
            </if>
            <if test="skuId != null">
                and sku_id = #{skuId}
            </if>
            <if test="specValues != null and specValues != ''">
                and spec_values = #{specValues}
            </if>
            <if test="imgUrl != null and imgUrl != ''">
                and img_url = #{imgUrl}
            </if>
            <if test="buyingPrice != null">
                and buying_price = #{buyingPrice}
            </if>
            <if test="originalPrice != null">
                and original_price = #{originalPrice}
            </if>
            <if test="price != null">
                and price = #{price}
            </if>
            <if test="grossProfit != null">
                and gross_profit = #{grossProfit}
            </if>
            <if test="commissionRatio != null">
                and commission_ratio = #{commissionRatio}
            </if>
            <if test="commission != null">
                and commission = #{commission}
            </if>
            <if test="salesTeacherCommissionRatio != null">
                and sales_teacher_commission_ratio = #{salesTeacherCommissionRatio}
            </if>
            <if test="salesTeacherCommission != null">
                and sales_teacher_commission = #{salesTeacherCommission}
            </if>
            <if test="unit != null and unit != ''">
                and unit = #{unit}
            </if>
            <if test="goodsCatalog != null and goodsCatalog != ''">
                and goods_catalog = #{goodsCatalog}
            </if>
            <if test="goodsTypeId != null">
                and goods_type_id = #{goodsTypeId}
            </if>
            <if test="goodsTypeName != null and goodsTypeName != ''">
                and goods_type_name = #{goodsTypeName}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="orderProductMap" parameterType="com.domain.complex.OrderProductQuery">
        select * from tb_order_product
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="orderProductMap" parameterType="com.domain.complex.OrderProductQuery">
        select * from tb_order_product
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.OrderProduct">
        update tb_order_product
        <trim prefix="set" suffixOverrides=",">
            <if test="orderId != null">
               order_id = #{orderId},
            </if>
            <if test="productCatalog != null">
               product_catalog = #{productCatalog},
            </if>
            <if test="productId != null">
               product_id = #{productId},
            </if>
            <if test="number != null">
               number = #{number},
            </if>
            <if test="amount != null">
               amount = #{amount},
            </if>
            <if test="productName != null">
               product_name = #{productName},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId},
            </if>
            <if test="specValues != null">
                spec_values = #{specValues},
            </if>
            <if test="imgUrl != null">
               img_url = #{imgUrl},
            </if>
            <if test="buyingPrice != null">
                buying_price = #{buyingPrice},
            </if>
            <if test="originalPrice != null">
               original_price = #{originalPrice},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="grossProfit != null">
               gross_profit = #{grossProfit},
            </if>
            <if test="commissionRatio != null">
               commission_ratio = #{commissionRatio},
            </if>
            <if test="commission != null">
               commission = #{commission},
            </if>
            <if test="salesTeacherCommissionRatio != null">
               sales_teacher_commission_ratio = #{salesTeacherCommissionRatio},
            </if>
            <if test="salesTeacherCommission != null">
               sales_teacher_commission = #{salesTeacherCommission},
            </if>
            <if test="unit != null">
               unit = #{unit},
            </if>
            <if test="goodsCatalog != null">
               goods_catalog = #{goodsCatalog},
            </if>
            <if test="goodsTypeId != null">
               goods_type_id = #{goodsTypeId},
            </if>
            <if test="goodsTypeName != null">
               goods_type_name = #{goodsTypeName},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.OrderProduct" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_order_product
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="productCatalog != null">
                product_catalog,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="number != null">
                number,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="productName != null">
                product_name,
            </if>
            <if test="skuId != null">
                sku_id,
            </if>
            <if test="specValues != null">
                spec_values,
            </if>
            <if test="imgUrl != null">
                img_url,
            </if>
            <if test="buyingPrice != null">
                buying_price,
            </if>
            <if test="originalPrice != null">
                original_price,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="grossProfit != null">
                gross_profit,
            </if>
            <if test="commissionRatio != null">
                commission_ratio,
            </if>
            <if test="commission != null">
                commission,
            </if>
            <if test="salesTeacherCommissionRatio != null">
                sales_teacher_commission_ratio,
            </if>
            <if test="salesTeacherCommission != null">
                sales_teacher_commission,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="goodsCatalog != null">
                goods_catalog,
            </if>
            <if test="goodsTypeId != null">
                goods_type_id,
            </if>
            <if test="goodsTypeName != null">
                goods_type_name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="orderId != null">
                #{orderId},
            </if>
            <if test="productCatalog != null">
                #{productCatalog},
            </if>
            <if test="productId != null">
                #{productId},
            </if>
            <if test="number != null">
                #{number},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="productName != null">
                #{productName},
            </if>
            <if test="skuId != null">
                #{skuId},
            </if>
            <if test="specValues != null">
                #{specValues},
            </if>
            <if test="imgUrl != null">
                #{imgUrl},
            </if>
            <if test="buyingPrice != null">
                #{buyingPrice},
            </if>
            <if test="originalPrice != null">
                #{originalPrice},
            </if>
            <if test="price != null">
                #{price},
            </if>
            <if test="grossProfit != null">
                #{grossProfit},
            </if>
            <if test="commissionRatio != null">
                #{commissionRatio},
            </if>
            <if test="commission != null">
                #{commission},
            </if>
            <if test="salesTeacherCommissionRatio != null">
                #{salesTeacherCommissionRatio},
            </if>
            <if test="salesTeacherCommission != null">
                #{salesTeacherCommission},
            </if>
            <if test="unit != null">
                #{unit},
            </if>
            <if test="goodsCatalog != null">
                #{goodsCatalog},
            </if>
            <if test="goodsTypeId != null">
                #{goodsTypeId},
            </if>
            <if test="goodsTypeName != null">
                #{goodsTypeName},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.OrderProductQuery">
        select count(*) from tb_order_product
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="orderProductMap">
        select * from tb_order_product where id = #{id}
    </select>

    <select id="selectByIds" resultMap="orderProductMap">
        select * from tb_order_product where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_order_product
        (
        order_id,
        product_catalog,
        product_id,
        number,
        amount,
        product_name,
        sku_id,
        spec_values,
        img_url,
        buying_price,
        original_price,
        price,
        gross_profit,
        commission_ratio,
        commission,
        sales_teacher_commission_ratio,
        sales_teacher_commission,
        unit,
        goods_catalog,
        goods_type_id,
        goods_type_name,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.orderId},
        #{item.productCatalog},
        #{item.productId},
        #{item.number},
        #{item.amount},
        #{item.productName},
        #{item.skuId},
        #{item.specValues},
        #{item.imgUrl},
        #{item.buyingPrice},
        #{item.originalPrice},
        #{item.price},
        #{item.grossProfit},
        #{item.commissionRatio},
        #{item.commission},
        #{item.salesTeacherCommissionRatio},
        #{item.salesTeacherCommission},
        #{item.unit},
        #{item.goodsCatalog},
        #{item.goodsTypeId},
        #{item.goodsTypeName},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>