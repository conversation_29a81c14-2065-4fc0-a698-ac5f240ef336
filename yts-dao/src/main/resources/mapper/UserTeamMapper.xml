<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.UserTeamDao">

    <resultMap id="userTeamMap" type="com.domain.UserTeam">
        <id column="id" property="id"/>
        <result column="captain_user_id" property="captainUserId"/>
        <result column="team_member_user_id" property="teamMemberUserId"/>
        <result column="binding_catalog" property="bindingCatalog"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="captainUserId != null">
                and captain_user_id = #{captainUserId}
            </if>
            <if test="captainUserIds != null and captainUserIds.size > 0">
                and captain_user_id in
                <foreach collection="captainUserIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="teamMemberUserId != null">
                and team_member_user_id = #{teamMemberUserId}
            </if>
            <if test="teamMemberUserIds != null and teamMemberUserIds.size > 0">
                and team_member_user_id in
                <foreach collection="teamMemberUserIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bindingCatalog != null and bindingCatalog != ''">
                and binding_catalog = #{bindingCatalog}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="userTeamMap" parameterType="com.domain.complex.UserTeamQuery">
        select * from tb_user_team
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="userTeamMap" parameterType="com.domain.complex.UserTeamQuery">
        select * from tb_user_team
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.UserTeam">
        update tb_user_team
        <trim prefix="set" suffixOverrides=",">
            <if test="captainUserId != null">
               captain_user_id = #{captainUserId},
            </if>
            <if test="teamMemberUserId != null">
               team_member_user_id = #{teamMemberUserId},
            </if>
            <if test="bindingCatalog != null">
               binding_catalog = #{bindingCatalog},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.UserTeam" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_user_team
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="captainUserId != null">
                captain_user_id,
            </if>
            <if test="teamMemberUserId != null">
                team_member_user_id,
            </if>
            <if test="bindingCatalog != null">
                binding_catalog,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="captainUserId != null">
                #{captainUserId},
            </if>
            <if test="teamMemberUserId != null">
                #{teamMemberUserId},
            </if>
            <if test="bindingCatalog != null">
                #{bindingCatalog},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.UserTeamQuery">
        select count(*) from tb_user_team
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="userTeamMap">
        select * from tb_user_team where id = #{id}
    </select>

    <select id="selectByIds" resultMap="userTeamMap">
        select * from tb_user_team where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_user_team
        (
        captain_user_id,
        team_member_user_id,
        binding_catalog,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.captainUserId},
        #{item.teamMemberUserId},
        #{item.bindingCatalog},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>