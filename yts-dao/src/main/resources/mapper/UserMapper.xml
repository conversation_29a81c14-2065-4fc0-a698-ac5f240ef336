<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.UserDao">

    <resultMap id="userMap" type="com.domain.User">
        <id column="id" property="id"/>
        <result column="union_id" property="unionId"/>
        <result column="open_id" property="openId"/>
        <result column="invitation_code" property="invitationCode"/>
        <result column="qr_code" property="qrCode"/>
        <result column="catalog" property="catalog"/>
        <result column="name" property="name"/>
        <result column="mobile" property="mobile"/>
        <result column="avatar" property="avatar"/>
        <result column="gender" property="gender"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="unionId != null and unionId != ''">
                and union_id = #{unionId}
            </if>
            <if test="unionIds != null and unionIds.size > 0">
                and union_id in
                <foreach collection="unionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="openId != null and openId != ''">
                and open_id = #{openId}
            </if>
            <if test="invitationCode != null">
                and invitation_code = #{invitationCode}
            </if>
            <if test="qrCode != null and qrCode != ''">
                and qr_code = #{qrCode}
            </if>
            <if test="catalog != null and catalog != ''">
                and catalog = #{catalog}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="mobile != null and mobile != ''">
                and mobile = #{mobile}
            </if>
            <if test="avatar != null and avatar != ''">
                and avatar = #{avatar}
            </if>
            <if test="gender != null and gender != ''">
                and gender = #{gender}
            </if>
            <if test="lastLoginTime != null">
                and last_login_time = #{lastLoginTime}
            </if>
            <if test="minLastLoginTime != null">
                and last_login_time <![CDATA[ >= ]]> #{minLastLoginTime}
            </if>
            <if test="maxLastLoginTime != null">
                and last_login_time <![CDATA[ < ]]> #{maxLastLoginTime}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="userMap" parameterType="com.domain.complex.UserQuery">
        select * from tb_user
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="userMap" parameterType="com.domain.complex.UserQuery">
        select * from tb_user
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.User">
        update tb_user
        <trim prefix="set" suffixOverrides=",">
            <if test="unionId != null">
               union_id = #{unionId},
            </if>
            <if test="openId != null">
               open_id = #{openId},
            </if>
            <if test="invitationCode != null">
               invitation_code = #{invitationCode},
            </if>
            <if test="qrCode != null">
               qr_code = #{qrCode},
            </if>
            <if test="catalog != null">
               catalog = #{catalog},
            </if>
            <if test="name != null">
               name = #{name},
            </if>
            <if test="mobile != null">
               mobile = #{mobile},
            </if>
            <if test="avatar != null">
               avatar = #{avatar},
            </if>
            <if test="gender != null">
               gender = #{gender},
            </if>
            <if test="lastLoginTime != null">
               last_login_time = #{lastLoginTime},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.User" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_user
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="unionId != null">
                union_id,
            </if>
            <if test="openId != null">
                open_id,
            </if>
            <if test="invitationCode != null">
                invitation_code,
            </if>
            <if test="qrCode != null">
                qr_code,
            </if>
            <if test="catalog != null">
                catalog,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="avatar != null">
                avatar,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="lastLoginTime != null">
                last_login_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="unionId != null">
                #{unionId},
            </if>
            <if test="openId != null">
                #{openId},
            </if>
            <if test="invitationCode != null">
                #{invitationCode},
            </if>
            <if test="qrCode != null">
                #{qrCode},
            </if>
            <if test="catalog != null">
                #{catalog},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="mobile != null">
                #{mobile},
            </if>
            <if test="avatar != null">
                #{avatar},
            </if>
            <if test="gender != null">
                #{gender},
            </if>
            <if test="lastLoginTime != null">
                #{lastLoginTime},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.UserQuery">
        select count(*) from tb_user
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="userMap">
        select * from tb_user where id = #{id}
    </select>

    <select id="selectByIds" resultMap="userMap">
        select * from tb_user where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_user
        (
        union_id,
        open_id,
        invitation_code,
        qr_code,
        catalog,
        name,
        mobile,
        avatar,
        gender,
        last_login_time,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.unionId},
        #{item.openId},
        #{item.invitationCode},
        #{item.qrCode},
        #{item.catalog},
        #{item.name},
        #{item.mobile},
        #{item.avatar},
        #{item.gender},
        #{item.lastLoginTime},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>

    <select id="selectByUnionId" resultMap="userMap">
        select * from tb_user where union_id = #{unionId}
    </select>

    <select id="selectByOpenId" resultMap="userMap">
        select * from tb_user where open_id = #{openId}
    </select>
</mapper>