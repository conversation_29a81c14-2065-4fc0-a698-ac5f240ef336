<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.WechatAppDao">

    <resultMap id="wechatAppMap" type="com.domain.WechatApp">
        <id column="id" property="id"/>
        <result column="appId" property="appid"/>
        <result column="secret" property="secret"/>
        <result column="name" property="name"/>
        <result column="access_token" property="accessToken"/>
        <result column="access_token_datetime" property="accessTokenDatetime"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="appid != null and appid != ''">
                and appId = #{appid}
            </if>
            <if test="secret != null and secret != ''">
                and secret = #{secret}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="accessToken != null and accessToken != ''">
                and access_token = #{accessToken}
            </if>
            <if test="accessTokenDatetime != null">
                and access_token_datetime = #{accessTokenDatetime}
            </if>
            <if test="minAccessTokenDatetime != null">
                and access_token_datetime <![CDATA[ >= ]]> #{minAccessTokenDatetime}
            </if>
            <if test="maxAccessTokenDatetime != null">
                and access_token_datetime <![CDATA[ < ]]> #{maxAccessTokenDatetime}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="wechatAppMap" parameterType="com.domain.complex.WechatAppQuery">
        select * from tb_wechat_app
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="wechatAppMap" parameterType="com.domain.complex.WechatAppQuery">
        select * from tb_wechat_app
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.WechatApp">
        update tb_wechat_app
        <trim prefix="set" suffixOverrides=",">
            <if test="appid != null">
               appId = #{appid},
            </if>
            <if test="secret != null">
               secret = #{secret},
            </if>
            <if test="name != null">
               name = #{name},
            </if>
            <if test="accessToken != null">
               access_token = #{accessToken},
            </if>
            <if test="accessTokenDatetime != null">
               access_token_datetime = #{accessTokenDatetime},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.WechatApp" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_wechat_app
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="appid != null">
                appId,
            </if>
            <if test="secret != null">
                secret,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="accessToken != null">
                access_token,
            </if>
            <if test="accessTokenDatetime != null">
                access_token_datetime,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="appid != null">
                #{appid},
            </if>
            <if test="secret != null">
                #{secret},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="accessToken != null">
                #{accessToken},
            </if>
            <if test="accessTokenDatetime != null">
                #{accessTokenDatetime},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.WechatAppQuery">
        select count(*) from tb_wechat_app
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="wechatAppMap">
        select * from tb_wechat_app where id = #{id}
    </select>

    <select id="selectByIds" resultMap="wechatAppMap">
        select * from tb_wechat_app where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_wechat_app
        (
        appId,
        secret,
        name,
        access_token,
        access_token_datetime,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.appid},
        #{item.secret},
        #{item.name},
        #{item.accessToken},
        #{item.accessTokenDatetime},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>