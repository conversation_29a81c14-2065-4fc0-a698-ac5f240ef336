<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.CourseAppointmentDao">

    <resultMap id="courseAppointmentMap" type="com.domain.CourseAppointment">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="course_id" property="courseId"/>
        <result column="mobile" property="mobile"/>
        <result column="remark" property="remark"/>
        <result column="contact" property="contact"/>
        <result column="comment" property="comment"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="courseId != null">
                and course_id = #{courseId}
            </if>
            <if test="courseIds != null and courseIds.size > 0">
                and course_id in
                <foreach collection="courseIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="mobile != null and mobile != ''">
                and mobile like concat('%', #{mobile}, '%')
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="contact != null and contact != ''">
                and contact = #{contact}
            </if>
            <if test="comment != null and comment != ''">
                and comment = #{comment}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="courseAppointmentMap" parameterType="com.domain.complex.CourseAppointmentQuery">
        select * from tb_course_appointment
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="courseAppointmentMap" parameterType="com.domain.complex.CourseAppointmentQuery">
        select * from tb_course_appointment
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.CourseAppointment">
        update tb_course_appointment
        <trim prefix="set" suffixOverrides=",">
            <if test="userId != null">
               user_id = #{userId},
            </if>
            <if test="courseId != null">
               course_id = #{courseId},
            </if>
            <if test="mobile != null">
               mobile = #{mobile},
            </if>
            <if test="remark != null">
               remark = #{remark},
            </if>
            <if test="contact != null">
               contact = #{contact},
            </if>
            <if test="comment != null">
               comment = #{comment},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.CourseAppointment" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_course_appointment
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="courseId != null">
                course_id,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="contact != null">
                contact,
            </if>
            <if test="comment != null">
                comment,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="courseId != null">
                #{courseId},
            </if>
            <if test="mobile != null">
                #{mobile},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="contact != null">
                #{contact},
            </if>
            <if test="comment != null">
                #{comment},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.CourseAppointmentQuery">
        select count(*) from tb_course_appointment
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="courseAppointmentMap">
        select * from tb_course_appointment where id = #{id}
    </select>

    <select id="selectByIds" resultMap="courseAppointmentMap">
        select * from tb_course_appointment where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_course_appointment
        (
        user_id,
        course_id,
        mobile,
        remark,
        contact,
        comment,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.userId},
        #{item.courseId},
        #{item.mobile},
        #{item.remark},
        #{item.contact},
        #{item.comment},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>