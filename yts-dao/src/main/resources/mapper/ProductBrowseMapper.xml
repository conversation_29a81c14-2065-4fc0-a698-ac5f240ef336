<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.ProductBrowseDao">

    <resultMap id="productBrowseMap" type="com.domain.ProductBrowse">
        <id column="id" property="id"/>
        <result column="product_catalog" property="productCatalog"/>
        <result column="product_id" property="productId"/>
        <result column="user_id" property="userId"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="productCatalog != null and productCatalog != ''">
                and product_catalog = #{productCatalog}
            </if>
            <if test="productId != null">
                and product_id = #{productId}
            </if>
            <if test="productIds != null and productIds.size > 0">
                and product_id in
                <foreach collection="productIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="productBrowseMap" parameterType="com.domain.complex.ProductBrowseQuery">
        select * from tb_product_browse
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="productBrowseMap" parameterType="com.domain.complex.ProductBrowseQuery">
        select * from tb_product_browse
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.ProductBrowse">
        update tb_product_browse
        <trim prefix="set" suffixOverrides=",">
            <if test="productCatalog != null">
               product_catalog = #{productCatalog},
            </if>
            <if test="productId != null">
               product_id = #{productId},
            </if>
            <if test="userId != null">
               user_id = #{userId},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.ProductBrowse" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_product_browse
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="productCatalog != null">
                product_catalog,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="productCatalog != null">
                #{productCatalog},
            </if>
            <if test="productId != null">
                #{productId},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.ProductBrowseQuery">
        select count(*) from tb_product_browse
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="productBrowseMap">
        select * from tb_product_browse where id = #{id}
    </select>

    <select id="selectByIds" resultMap="productBrowseMap">
        select * from tb_product_browse where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_product_browse
        (
        product_catalog,
        product_id,
        user_id,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.productCatalog},
        #{item.productId},
        #{item.userId},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>