<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.FileDao">

    <resultMap id="fileMap" type="com.domain.File">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="size" property="size"/>
        <result column="url" property="url"/>
        <result column="oss_url" property="ossUrl"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="size != null">
                and size = #{size}
            </if>
            <if test="url != null and url != ''">
                and url = #{url}
            </if>
            <if test="ossUrl != null and ossUrl != ''">
                and oss_url = #{ossUrl}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="fileMap" parameterType="com.domain.complex.FileQuery">
        select * from tb_file
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="fileMap" parameterType="com.domain.complex.FileQuery">
        select * from tb_file
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.File">
        update tb_file
        <trim prefix="set" suffixOverrides=",">
            <if test="name != null">
               name = #{name},
            </if>
            <if test="type != null">
               type = #{type},
            </if>
            <if test="size != null">
               size = #{size},
            </if>
            <if test="url != null">
               url = #{url},
            </if>
            <if test="ossUrl != null">
               oss_url = #{ossUrl},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.File" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_file
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="size != null">
                size,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="ossUrl != null">
                oss_url,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="size != null">
                #{size},
            </if>
            <if test="url != null">
                #{url},
            </if>
            <if test="ossUrl != null">
                #{ossUrl},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.FileQuery">
        select count(*) from tb_file
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="fileMap">
        select * from tb_file where id = #{id}
    </select>

    <select id="selectByIds" resultMap="fileMap">
        select * from tb_file where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_file
        (
        name,
        type,
        size,
        url,
        oss_url,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.name},
        #{item.type},
        #{item.size},
        #{item.url},
        #{item.ossUrl},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>

    <select id="selectByUrl" resultMap="fileMap">
        select * from tb_file where url = #{url}
    </select>
</mapper>