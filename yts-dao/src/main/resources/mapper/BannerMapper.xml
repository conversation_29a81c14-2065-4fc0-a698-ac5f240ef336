<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.BannerDao">

    <resultMap id="bannerMap" type="com.domain.Banner">
        <id column="id" property="id"/>
        <result column="img_url" property="imgUrl"/>
        <result column="jump" property="jump"/>
        <result column="jump_catalog" property="jumpCatalog"/>
        <result column="jump_id" property="jumpId"/>
        <result column="stage" property="stage"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="imgUrl != null and imgUrl != ''">
                and img_url = #{imgUrl}
            </if>
            <if test="jump != null and jump != ''">
                and jump = #{jump}
            </if>
            <if test="jumpCatalog != null and jumpCatalog != ''">
                and jump_catalog = #{jumpCatalog}
            </if>
            <if test="jumpId != null">
                and jump_id = #{jumpId}
            </if>
            <if test="jumpIds != null and jumpIds.size > 0">
                and jump_id in
                <foreach collection="jumpIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stage != null and stage != ''">
                and stage = #{stage}
            </if>
            <if test="sort != null">
                and sort = #{sort}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="bannerMap" parameterType="com.domain.complex.BannerQuery">
        select * from tb_banner
        <include refid="common_where_if"/>
        order by sort asc, id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="bannerMap" parameterType="com.domain.complex.BannerQuery">
        select * from tb_banner
        <include refid="common_where_if"/>
        order by sort asc, id desc
    </select>

    <update id="updateById" parameterType="com.domain.Banner">
        update tb_banner
        <trim prefix="set" suffixOverrides=",">
            <if test="imgUrl != null">
               img_url = #{imgUrl},
            </if>
            <if test="jump != null">
               jump = #{jump},
            </if>
            <if test="jumpCatalog != null">
               jump_catalog = #{jumpCatalog},
            </if>
            <if test="jumpId != null">
               jump_id = #{jumpId},
            </if>
            <if test="stage != null">
               stage = #{stage},
            </if>
            <if test="sort != null">
               sort = #{sort},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.Banner" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_banner
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="imgUrl != null">
                img_url,
            </if>
            <if test="jump != null">
                jump,
            </if>
            <if test="jumpCatalog != null">
                jump_catalog,
            </if>
            <if test="jumpId != null">
                jump_id,
            </if>
            <if test="stage != null">
                stage,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="imgUrl != null">
                #{imgUrl},
            </if>
            <if test="jump != null">
                #{jump},
            </if>
            <if test="jumpCatalog != null">
                #{jumpCatalog},
            </if>
            <if test="jumpId != null">
                #{jumpId},
            </if>
            <if test="stage != null">
                #{stage},
            </if>
            <if test="sort != null">
                #{sort},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.BannerQuery">
        select count(*) from tb_banner
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="bannerMap">
        select * from tb_banner where id = #{id}
    </select>

    <select id="selectByIds" resultMap="bannerMap">
        select * from tb_banner where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_banner
        (
        img_url,
        jump,
        jump_catalog,
        jump_id,
        stage,
        sort,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.imgUrl},
        #{item.jump},
        #{item.jumpCatalog},
        #{item.jumpId},
        #{item.stage},
        #{item.sort},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>