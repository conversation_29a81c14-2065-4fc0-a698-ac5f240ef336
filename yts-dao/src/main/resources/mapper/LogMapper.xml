<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.LogDao">

    <resultMap id="logMap" type="com.domain.Log">
        <id column="id" property="id"/>
        <result column="platform" property="platform"/>
        <result column="version" property="version"/>
        <result column="type" property="type"/>
        <result column="user_id" property="userId"/>
        <result column="ip" property="ip"/>
        <result column="url" property="url"/>
        <result column="request_id" property="requestId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="duration" property="duration"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="platform != null and platform != ''">
                and platform = #{platform}
            </if>
            <if test="version != null and version != ''">
                and version = #{version}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ip != null and ip != ''">
                and ip = #{ip}
            </if>
            <if test="url != null and url != ''">
                and url = #{url}
            </if>
            <if test="requestId != null and requestId != ''">
                and request_id = #{requestId}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="minStartTime != null">
                and start_time <![CDATA[ >= ]]> #{minStartTime}
            </if>
            <if test="maxStartTime != null">
                and start_time <![CDATA[ < ]]> #{maxStartTime}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime}
            </if>
            <if test="minEndTime != null">
                and end_time <![CDATA[ >= ]]> #{minEndTime}
            </if>
            <if test="maxEndTime != null">
                and end_time <![CDATA[ < ]]> #{maxEndTime}
            </if>
            <if test="duration != null">
                and duration = #{duration}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="logMap" parameterType="com.domain.complex.LogQuery">
        select * from tb_log
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="logMap" parameterType="com.domain.complex.LogQuery">
        select * from tb_log
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.Log">
        update tb_log
        <trim prefix="set" suffixOverrides=",">
            <if test="platform != null">
               platform = #{platform},
            </if>
            <if test="version != null">
               version = #{version},
            </if>
            <if test="type != null">
               type = #{type},
            </if>
            <if test="userId != null">
               user_id = #{userId},
            </if>
            <if test="ip != null">
               ip = #{ip},
            </if>
            <if test="url != null">
               url = #{url},
            </if>
            <if test="requestId != null">
               request_id = #{requestId},
            </if>
            <if test="startTime != null">
               start_time = #{startTime},
            </if>
            <if test="endTime != null">
               end_time = #{endTime},
            </if>
            <if test="duration != null">
               duration = #{duration},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.Log" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_log
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="requestId != null">
                request_id,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="duration != null">
                duration,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="platform != null">
                #{platform},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="ip != null">
                #{ip},
            </if>
            <if test="url != null">
                #{url},
            </if>
            <if test="requestId != null">
                #{requestId},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="duration != null">
                #{duration},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.LogQuery">
        select count(*) from tb_log
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="logMap">
        select * from tb_log where id = #{id}
    </select>

    <select id="selectByIds" resultMap="logMap">
        select * from tb_log where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_log
        (
        platform,
        version,
        type,
        user_id,
        ip,
        url,
        request_id,
        start_time,
        end_time,
        duration,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.platform},
        #{item.version},
        #{item.type},
        #{item.userId},
        #{item.ip},
        #{item.url},
        #{item.requestId},
        #{item.startTime},
        #{item.endTime},
        #{item.duration},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>

    <delete id="deleteById" parameterType="Long">
        delete from tb_log where id = #{id}
    </delete>
</mapper>