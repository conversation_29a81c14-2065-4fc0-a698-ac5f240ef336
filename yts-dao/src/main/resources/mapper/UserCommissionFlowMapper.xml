<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.UserCommissionFlowDao">

    <resultMap id="userCommissionFlowMap" type="com.domain.UserCommissionFlow">
        <id column="id" property="id"/>
        <result column="flow_code" property="flowCode"/>
        <result column="user_id" property="userId"/>
        <result column="order_id" property="orderId"/>
        <result column="order_code" property="orderCode"/>
        <result column="prepay_id" property="prepayId"/>
        <result column="transaction_time" property="transactionTime"/>
        <result column="flow_amount" property="flowAmount"/>
        <result column="amount" property="amount"/>
        <result column="catalog" property="catalog"/>
        <result column="pay_channel" property="payChannel"/>
        <result column="income" property="income"/>
        <result column="label" property="label"/>
        <result column="comment" property="comment"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="flowCode != null and flowCode != ''">
                and flow_code = #{flowCode}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orderId != null">
                and order_id = #{orderId}
            </if>
            <if test="orderIds != null and orderIds.size > 0">
                and order_id in
                <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orderCode != null and orderCode != ''">
                and order_code = #{orderCode}
            </if>
            <if test="prepayId != null and prepayId != ''">
                and prepay_id = #{prepayId}
            </if>
            <if test="transactionTime != null">
                and transaction_time = #{transactionTime}
            </if>
            <if test="minTransactionTime != null">
                and transaction_time <![CDATA[ >= ]]> #{minTransactionTime}
            </if>
            <if test="maxTransactionTime != null">
                and transaction_time <![CDATA[ < ]]> #{maxTransactionTime}
            </if>
            <if test="flowAmount != null">
                and flow_amount = #{flowAmount}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="catalog != null and catalog != ''">
                and catalog = #{catalog}
            </if>
            <if test="catalogs != null and catalogs.size > 0">
                and catalog in
                <foreach collection="catalogs" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="payChannel != null and payChannel != ''">
                and pay_channel = #{payChannel}
            </if>
            <if test="income != null and income != ''">
                and income = #{income}
            </if>
            <if test="label != null and label != ''">
                and label = #{label}
            </if>
            <if test="comment != null and comment != ''">
                and comment = #{comment}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="userCommissionFlowMap" parameterType="com.domain.complex.UserCommissionFlowQuery">
        select * from tb_user_commission_flow
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="userCommissionFlowMap" parameterType="com.domain.complex.UserCommissionFlowQuery">
        select * from tb_user_commission_flow
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.UserCommissionFlow">
        update tb_user_commission_flow
        <trim prefix="set" suffixOverrides=",">
            <if test="flowCode != null">
               flow_code = #{flowCode},
            </if>
            <if test="userId != null">
               user_id = #{userId},
            </if>
            <if test="orderId != null">
               order_id = #{orderId},
            </if>
            <if test="orderCode != null">
               order_code = #{orderCode},
            </if>
            <if test="prepayId != null">
               prepay_id = #{prepayId},
            </if>
            <if test="transactionTime != null">
               transaction_time = #{transactionTime},
            </if>
            <if test="flowAmount != null">
               flow_amount = #{flowAmount},
            </if>
            <if test="amount != null">
               amount = #{amount},
            </if>
            <if test="catalog != null">
               catalog = #{catalog},
            </if>
            <if test="payChannel != null">
               pay_channel = #{payChannel},
            </if>
            <if test="income != null">
               income = #{income},
            </if>
            <if test="label != null">
               label = #{label},
            </if>
            <if test="comment != null">
               comment = #{comment},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.UserCommissionFlow" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_user_commission_flow
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="flowCode != null">
                flow_code,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="orderCode != null">
                order_code,
            </if>
            <if test="prepayId != null">
                prepay_id,
            </if>
            <if test="transactionTime != null">
                transaction_time,
            </if>
            <if test="flowAmount != null">
                flow_amount,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="catalog != null">
                catalog,
            </if>
            <if test="payChannel != null">
                pay_channel,
            </if>
            <if test="income != null">
                income,
            </if>
            <if test="label != null">
                label,
            </if>
            <if test="comment != null">
                comment,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="flowCode != null">
                #{flowCode},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="orderId != null">
                #{orderId},
            </if>
            <if test="orderCode != null">
                #{orderCode},
            </if>
            <if test="prepayId != null">
                #{prepayId},
            </if>
            <if test="transactionTime != null">
                #{transactionTime},
            </if>
            <if test="flowAmount != null">
                #{flowAmount},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="catalog != null">
                #{catalog},
            </if>
            <if test="payChannel != null">
                #{payChannel},
            </if>
            <if test="income != null">
                #{income},
            </if>
            <if test="label != null">
                #{label},
            </if>
            <if test="comment != null">
                #{comment},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.UserCommissionFlowQuery">
        select count(*) from tb_user_commission_flow
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="userCommissionFlowMap">
        select * from tb_user_commission_flow where id = #{id}
    </select>

    <select id="selectByIds" resultMap="userCommissionFlowMap">
        select * from tb_user_commission_flow where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_user_commission_flow
        (
        flow_code,
        user_id,
        order_id,
        order_code,
        prepay_id,
        transaction_time,
        flow_amount,
        amount,
        catalog,
        pay_channel,
        income,
        label,
        comment,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.flowCode},
        #{item.userId},
        #{item.orderId},
        #{item.orderCode},
        #{item.prepayId},
        #{item.transactionTime},
        #{item.flowAmount},
        #{item.amount},
        #{item.catalog},
        #{item.payChannel},
        #{item.income},
        #{item.label},
        #{item.comment},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>