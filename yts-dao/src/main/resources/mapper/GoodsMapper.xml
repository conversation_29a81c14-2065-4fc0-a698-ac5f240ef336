<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.GoodsDao">

    <resultMap id="goodsMap" type="com.domain.Goods">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="unit" property="unit"/>
        <result column="img_url" property="imgUrl"/>
        <result column="catalog" property="catalog"/>
        <result column="goods_type_id" property="goodsTypeId"/>
        <result column="buying_price" property="buyingPrice"/>
        <result column="original_price" property="originalPrice"/>
        <result column="price" property="price"/>
        <result column="gross_profit" property="grossProfit"/>
        <result column="open_commission" property="openCommission"/>
        <result column="commission_ratio" property="commissionRatio"/>
        <result column="sales_teacher_commission_ratio" property="salesTeacherCommissionRatio"/>
        <result column="detail_img_url" property="detailImgUrl"/>
        <result column="comment" property="comment"/>
        <result column="sales_volume" property="salesVolume"/>
        <result column="real_sales_volume" property="realSalesVolume"/>
        <result column="home" property="home"/>
        <result column="stage" property="stage"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="unit != null and unit != ''">
                and unit = #{unit}
            </if>
            <if test="imgUrl != null and imgUrl != ''">
                and img_url = #{imgUrl}
            </if>
            <if test="catalog != null and catalog != ''">
                and catalog = #{catalog}
            </if>
            <if test="goodsTypeId != null">
                and goods_type_id = #{goodsTypeId}
            </if>
            <if test="goodsTypeIds != null and goodsTypeIds.size > 0">
                and goods_type_id in
                <foreach collection="goodsTypeIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="buyingPrice != null">
                and buying_price = #{buyingPrice}
            </if>
            <if test="originalPrice != null">
                and original_price = #{originalPrice}
            </if>
            <if test="price != null">
                and price = #{price}
            </if>
            <if test="minPrice != null">
                and price <![CDATA[ >= ]]> #{minPrice}
            </if>
            <if test="maxPrice != null">
                and price <![CDATA[ < ]]> #{maxPrice}
            </if>
            <if test="grossProfit != null">
                and gross_profit = #{grossProfit}
            </if>
            <if test="openCommission != null and openCommission != ''">
                and open_commission = #{openCommission}
            </if>
            <if test="commissionRatio != null">
                and commission_ratio = #{commissionRatio}
            </if>
            <if test="salesTeacherCommissionRatio != null">
                and sales_teacher_commission_ratio = #{salesTeacherCommissionRatio}
            </if>
            <if test="detailImgUrl != null and detailImgUrl != ''">
                and detail_img_url = #{detailImgUrl}
            </if>
            <if test="comment != null and comment != ''">
                and comment = #{comment}
            </if>
            <if test="salesVolume != null">
                and sales_volume = #{salesVolume}
            </if>
            <if test="realSalesVolume != null">
                and real_sales_volume = #{realSalesVolume}
            </if>
            <if test="home != null and home != ''">
                and home = #{home}
            </if>
            <if test="stage != null and stage != ''">
                and stage = #{stage}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="goodsMap" parameterType="com.domain.complex.GoodsQuery">
        select * from tb_goods
        <include refid="common_where_if"/>
        <if test="sortType != null">
            order by ${sortType}
        </if>
        <if test="sortType == null">
            order by id desc
        </if>
        limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="goodsMap" parameterType="com.domain.complex.GoodsQuery">
        select * from tb_goods
        <include refid="common_where_if"/>
        <if test="sortType != null">
            order by ${sortType}
        </if>
        <if test="sortType == null">
            order by id desc
        </if>
    </select>

    <update id="updateById" parameterType="com.domain.Goods">
        update tb_goods
        <trim prefix="set" suffixOverrides=",">
            <if test="name != null">
               name = #{name},
            </if>
            <if test="unit != null">
               unit = #{unit},
            </if>
            <if test="imgUrl != null">
               img_url = #{imgUrl},
            </if>
            <if test="catalog != null">
               catalog = #{catalog},
            </if>
            <if test="goodsTypeId != null">
               goods_type_id = #{goodsTypeId},
            </if>
            <if test="buyingPrice != null">
               buying_price = #{buyingPrice},
            </if>
            <if test="originalPrice != null">
               original_price = #{originalPrice},
            </if>
            <if test="price != null">
               price = #{price},
            </if>
            <if test="grossProfit != null">
               gross_profit = #{grossProfit},
            </if>
            <if test="openCommission != null">
               open_commission = #{openCommission},
            </if>
            <if test="commissionRatio != null">
               commission_ratio = #{commissionRatio},
            </if>
            <if test="salesTeacherCommissionRatio != null">
               sales_teacher_commission_ratio = #{salesTeacherCommissionRatio},
            </if>
            <if test="detailImgUrl != null">
               detail_img_url = #{detailImgUrl},
            </if>
            <if test="comment != null">
               comment = #{comment},
            </if>
            <if test="salesVolume != null">
               sales_volume = #{salesVolume},
            </if>
            <if test="realSalesVolume != null">
               real_sales_volume = #{realSalesVolume},
            </if>
            <if test="home != null">
               home = #{home},
            </if>
            <if test="stage != null">
               stage = #{stage},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.Goods" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_goods
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="imgUrl != null">
                img_url,
            </if>
            <if test="catalog != null">
                catalog,
            </if>
            <if test="goodsTypeId != null">
                goods_type_id,
            </if>
            <if test="buyingPrice != null">
                buying_price,
            </if>
            <if test="originalPrice != null">
                original_price,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="grossProfit != null">
                gross_profit,
            </if>
            <if test="openCommission != null">
                open_commission,
            </if>
            <if test="commissionRatio != null">
                commission_ratio,
            </if>
            <if test="salesTeacherCommissionRatio != null">
                sales_teacher_commission_ratio,
            </if>
            <if test="detailImgUrl != null">
                detail_img_url,
            </if>
            <if test="comment != null">
                comment,
            </if>
            <if test="salesVolume != null">
                sales_volume,
            </if>
            <if test="realSalesVolume != null">
                real_sales_volume,
            </if>
            <if test="home != null">
                home,
            </if>
            <if test="stage != null">
                stage,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="unit != null">
                #{unit},
            </if>
            <if test="imgUrl != null">
                #{imgUrl},
            </if>
            <if test="catalog != null">
                #{catalog},
            </if>
            <if test="goodsTypeId != null">
                #{goodsTypeId},
            </if>
            <if test="buyingPrice != null">
                #{buyingPrice},
            </if>
            <if test="originalPrice != null">
                #{originalPrice},
            </if>
            <if test="price != null">
                #{price},
            </if>
            <if test="grossProfit != null">
                #{grossProfit},
            </if>
            <if test="openCommission != null">
                #{openCommission},
            </if>
            <if test="commissionRatio != null">
                #{commissionRatio},
            </if>
            <if test="salesTeacherCommissionRatio != null">
                #{salesTeacherCommissionRatio},
            </if>
            <if test="detailImgUrl != null">
                #{detailImgUrl},
            </if>
            <if test="comment != null">
                #{comment},
            </if>
            <if test="salesVolume != null">
                #{salesVolume},
            </if>
            <if test="realSalesVolume != null">
                #{realSalesVolume},
            </if>
            <if test="home != null">
                #{home},
            </if>
            <if test="stage != null">
                #{stage},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.GoodsQuery">
        select count(*) from tb_goods
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="goodsMap">
        select * from tb_goods where id = #{id}
    </select>

    <select id="selectByIds" resultMap="goodsMap">
        select * from tb_goods where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_goods
        (
        name,
        unit,
        img_url,
        catalog,
        goods_type_id,
        buying_price,
        original_price,
        price,
        gross_profit,
        open_commission,
        commission_ratio,
        sales_teacher_commission_ratio,
        detail_img_url,
        comment,
        sales_volume,
        real_sales_volume,
        home,
        stage,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.name},
        #{item.unit},
        #{item.imgUrl},
        #{item.catalog},
        #{item.goodsTypeId},
        #{item.buyingPrice},
        #{item.originalPrice},
        #{item.price},
        #{item.grossProfit},
        #{item.openCommission},
        #{item.commissionRatio},
        #{item.salesTeacherCommissionRatio},
        #{item.detailImgUrl},
        #{item.comment},
        #{item.salesVolume},
        #{item.realSalesVolume},
        #{item.home},
        #{item.stage},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>