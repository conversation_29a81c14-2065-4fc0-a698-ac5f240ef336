<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.OrderRefundInfoDao">

    <resultMap id="orderRefundInfoMap" type="com.domain.OrderRefundInfo">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="refund_no" property="refundNo"/>
        <result column="refund_id" property="refundId"/>
        <result column="total_fee" property="totalFee"/>
        <result column="refund" property="refund"/>
        <result column="reason" property="reason"/>
        <result column="refund_status" property="refundStatus"/>
        <result column="content_return" property="contentReturn"/>
        <result column="content_notify" property="contentNotify"/>
        <result column="payment_type" property="paymentType"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="refundNo != null and refundNo != ''">
                and refund_no = #{refundNo}
            </if>
            <if test="refundId != null and refundId != ''">
                and refund_id = #{refundId}
            </if>
            <if test="totalFee != null">
                and total_fee = #{totalFee}
            </if>
            <if test="refund != null">
                and refund = #{refund}
            </if>
            <if test="reason != null and reason != ''">
                and reason = #{reason}
            </if>
            <if test="refundStatus != null and refundStatus != ''">
                and refund_status = #{refundStatus}
            </if>
            <if test="contentReturn != null and contentReturn != ''">
                and content_return = #{contentReturn}
            </if>
            <if test="contentNotify != null and contentNotify != ''">
                and content_notify = #{contentNotify}
            </if>
            <if test="paymentType != null and paymentType != ''">
                and payment_type = #{paymentType}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="orderRefundInfoMap" parameterType="com.domain.complex.OrderRefundInfoQuery">
        select * from tb_order_refund_info
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="orderRefundInfoMap" parameterType="com.domain.complex.OrderRefundInfoQuery">
        select * from tb_order_refund_info
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.OrderRefundInfo">
        update tb_order_refund_info
        <trim prefix="set" suffixOverrides=",">
            <if test="orderNo != null">
               order_no = #{orderNo},
            </if>
            <if test="refundNo != null">
               refund_no = #{refundNo},
            </if>
            <if test="refundId != null">
               refund_id = #{refundId},
            </if>
            <if test="totalFee != null">
               total_fee = #{totalFee},
            </if>
            <if test="refund != null">
               refund = #{refund},
            </if>
            <if test="reason != null">
               reason = #{reason},
            </if>
            <if test="refundStatus != null">
               refund_status = #{refundStatus},
            </if>
            <if test="contentReturn != null">
               content_return = #{contentReturn},
            </if>
            <if test="contentNotify != null">
               content_notify = #{contentNotify},
            </if>
            <if test="paymentType != null">
               payment_type = #{paymentType},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.OrderRefundInfo" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_order_refund_info
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="refundNo != null">
                refund_no,
            </if>
            <if test="refundId != null">
                refund_id,
            </if>
            <if test="totalFee != null">
                total_fee,
            </if>
            <if test="refund != null">
                refund,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="refundStatus != null">
                refund_status,
            </if>
            <if test="contentReturn != null">
                content_return,
            </if>
            <if test="contentNotify != null">
                content_notify,
            </if>
            <if test="paymentType != null">
                payment_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="orderNo != null">
                #{orderNo},
            </if>
            <if test="refundNo != null">
                #{refundNo},
            </if>
            <if test="refundId != null">
                #{refundId},
            </if>
            <if test="totalFee != null">
                #{totalFee},
            </if>
            <if test="refund != null">
                #{refund},
            </if>
            <if test="reason != null">
                #{reason},
            </if>
            <if test="refundStatus != null">
                #{refundStatus},
            </if>
            <if test="contentReturn != null">
                #{contentReturn},
            </if>
            <if test="contentNotify != null">
                #{contentNotify},
            </if>
            <if test="paymentType != null">
                #{paymentType},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.OrderRefundInfoQuery">
        select count(*) from tb_order_refund_info
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="orderRefundInfoMap">
        select * from tb_order_refund_info where id = #{id}
    </select>

    <select id="selectByIds" resultMap="orderRefundInfoMap">
        select * from tb_order_refund_info where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_order_refund_info
        (
        order_no,
        refund_no,
        refund_id,
        total_fee,
        refund,
        reason,
        refund_status,
        content_return,
        content_notify,
        payment_type,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.orderNo},
        #{item.refundNo},
        #{item.refundId},
        #{item.totalFee},
        #{item.refund},
        #{item.reason},
        #{item.refundStatus},
        #{item.contentReturn},
        #{item.contentNotify},
        #{item.paymentType},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>