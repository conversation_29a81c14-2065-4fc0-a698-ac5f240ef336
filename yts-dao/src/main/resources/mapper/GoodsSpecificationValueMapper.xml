<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.GoodsSpecificationValueDao">

    <resultMap id="goodsSpecificationValueMap" type="com.domain.GoodsSpecificationValue">
        <id column="id" property="id"/>
        <result column="goods_id" property="goodsId"/>
        <result column="goods_specification_type_id" property="goodsSpecificationTypeId"/>
        <result column="name" property="name"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="goodsId != null">
                and goods_id = #{goodsId}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="goodsSpecificationTypeId != null">
                and goods_specification_type_id = #{goodsSpecificationTypeId}
            </if>
            <if test="goodsSpecificationTypeIds != null and goodsSpecificationTypeIds.size > 0">
                and goods_specification_type_id in
                <foreach collection="goodsSpecificationTypeIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="goodsSpecificationValueMap" parameterType="com.domain.complex.GoodsSpecificationValueQuery">
        select * from tb_goods_specification_value
        <include refid="common_where_if"/>
        order by id asc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="goodsSpecificationValueMap" parameterType="com.domain.complex.GoodsSpecificationValueQuery">
        select * from tb_goods_specification_value
        <include refid="common_where_if"/>
        order by id asc
    </select>

    <update id="updateById" parameterType="com.domain.GoodsSpecificationValue">
        update tb_goods_specification_value
        <trim prefix="set" suffixOverrides=",">
            <if test="goodsId != null">
               goods_id = #{goodsId},
            </if>
            <if test="goodsSpecificationTypeId != null">
               goods_specification_type_id = #{goodsSpecificationTypeId},
            </if>
            <if test="name != null">
               name = #{name},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.GoodsSpecificationValue" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_goods_specification_value
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="goodsId != null">
                goods_id,
            </if>
            <if test="goodsSpecificationTypeId != null">
                goods_specification_type_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="goodsId != null">
                #{goodsId},
            </if>
            <if test="goodsSpecificationTypeId != null">
                #{goodsSpecificationTypeId},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.GoodsSpecificationValueQuery">
        select count(*) from tb_goods_specification_value
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="goodsSpecificationValueMap">
        select * from tb_goods_specification_value where id = #{id}
    </select>

    <select id="selectByIds" resultMap="goodsSpecificationValueMap">
        select * from tb_goods_specification_value where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_goods_specification_value
        (
        goods_id,
        goods_specification_type_id,
        name,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.goodsId},
        #{item.goodsSpecificationTypeId},
        #{item.name},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>

    <delete id="deleteById" parameterType="Long">
        delete from tb_goods_specification_value where id = #{id}
    </delete>
</mapper>