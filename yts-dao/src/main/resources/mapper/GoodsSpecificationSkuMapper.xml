<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.GoodsSpecificationSkuDao">

    <resultMap id="goodsSpecificationSkuMap" type="com.domain.GoodsSpecificationSku">
        <id column="id" property="id"/>
        <result column="goods_id" property="goodsId"/>
        <result column="spec_values" property="specValues"/>
        <result column="img_url" property="imgUrl"/>
        <result column="buying_price" property="buyingPrice"/>
        <result column="original_price" property="originalPrice"/>
        <result column="price" property="price"/>
        <result column="gross_profit" property="grossProfit"/>
        <result column="stock" property="stock"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="goodsId != null">
                and goods_id = #{goodsId}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="specValues != null and specValues != ''">
                and spec_values = #{specValues}
            </if>
            <if test="imgUrl != null and imgUrl != ''">
                and img_url = #{imgUrl}
            </if>
            <if test="buyingPrice != null">
                and buying_price = #{buyingPrice}
            </if>
            <if test="originalPrice != null">
                and original_price = #{originalPrice}
            </if>
            <if test="price != null">
                and price = #{price}
            </if>
            <if test="grossProfit != null">
                and gross_profit = #{grossProfit}
            </if>
            <if test="stock != null">
                and stock = #{stock}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="goodsSpecificationSkuMap" parameterType="com.domain.complex.GoodsSpecificationSkuQuery">
        select * from tb_goods_specification_sku
        <include refid="common_where_if"/>
        order by id asc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="goodsSpecificationSkuMap" parameterType="com.domain.complex.GoodsSpecificationSkuQuery">
        select * from tb_goods_specification_sku
        <include refid="common_where_if"/>
        order by id asc
    </select>

    <update id="updateById" parameterType="com.domain.GoodsSpecificationSku">
        update tb_goods_specification_sku
        <trim prefix="set" suffixOverrides=",">
            <if test="goodsId != null">
               goods_id = #{goodsId},
            </if>
            <if test="specValues != null">
               spec_values = #{specValues},
            </if>
            <if test="imgUrl != null">
               img_url = #{imgUrl},
            </if>
            <if test="buyingPrice != null">
               buying_price = #{buyingPrice},
            </if>
            <if test="originalPrice != null">
               original_price = #{originalPrice},
            </if>
            <if test="price != null">
               price = #{price},
            </if>
            <if test="grossProfit != null">
               gross_profit = #{grossProfit},
            </if>
            <if test="stock != null">
               stock = #{stock},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.GoodsSpecificationSku" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_goods_specification_sku
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="goodsId != null">
                goods_id,
            </if>
            <if test="specValues != null">
                spec_values,
            </if>
            <if test="imgUrl != null">
                img_url,
            </if>
            <if test="buyingPrice != null">
                buying_price,
            </if>
            <if test="originalPrice != null">
                original_price,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="grossProfit != null">
                gross_profit,
            </if>
            <if test="stock != null">
                stock,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="goodsId != null">
                #{goodsId},
            </if>
            <if test="specValues != null">
                #{specValues},
            </if>
            <if test="imgUrl != null">
                #{imgUrl},
            </if>
            <if test="buyingPrice != null">
                #{buyingPrice},
            </if>
            <if test="originalPrice != null">
                #{originalPrice},
            </if>
            <if test="price != null">
                #{price},
            </if>
            <if test="grossProfit != null">
                #{grossProfit},
            </if>
            <if test="stock != null">
                #{stock},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.GoodsSpecificationSkuQuery">
        select count(*) from tb_goods_specification_sku
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="goodsSpecificationSkuMap">
        select * from tb_goods_specification_sku where id = #{id}
    </select>

    <select id="selectByIds" resultMap="goodsSpecificationSkuMap">
        select * from tb_goods_specification_sku where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_goods_specification_sku
        (
        goods_id,
        spec_values,
        img_url,
        buying_price,
        original_price,
        price,
        gross_profit,
        stock,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.goodsId},
        #{item.specValues},
        #{item.imgUrl},
        #{item.buyingPrice},
        #{item.originalPrice},
        #{item.price},
        #{item.grossProfit},
        #{item.stock},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>

    <delete id="deleteById" parameterType="Long">
        delete from tb_goods_specification_sku where id = #{id}
    </delete>

    <select id="findBySpecValuesAndGoodsId" resultMap="goodsSpecificationSkuMap">
        select * from tb_goods_specification_sku where spec_values = #{specValues} and goods_id = #{goodsId}
    </select>

    <update id="updateStockById" parameterType="com.domain.GoodsSpecificationSku">
        update tb_goods_specification_sku
        <trim prefix="set" suffixOverrides=",">
            stock = stock - #{stock},
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
        </trim>
        where id = #{id} and #{stock} <![CDATA[ <= ]]> stock
    </update>
</mapper>