<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.OrderCodeFlowDao">

    <resultMap id="orderCodeFlowMap" type="com.domain.OrderCodeFlow">
        <id column="id" property="id"/>
        <result column="order_code" property="orderCode"/>
        <result column="prepay_id" property="prepayId"/>
        <result column="payment_type" property="paymentType"/>
        <result column="trade_type" property="tradeType"/>
        <result column="trade_state" property="tradeState"/>
        <result column="payer_total" property="payerTotal"/>
        <result column="content" property="content"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and order_code = #{orderCode}
            </if>
            <if test="prepayId != null and prepayId != ''">
                and prepay_id = #{prepayId}
            </if>
            <if test="paymentType != null and paymentType != ''">
                and payment_type = #{paymentType}
            </if>
            <if test="tradeType != null and tradeType != ''">
                and trade_type = #{tradeType}
            </if>
            <if test="tradeState != null and tradeState != ''">
                and trade_state = #{tradeState}
            </if>
            <if test="payerTotal != null">
                and payer_total = #{payerTotal}
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="orderCodeFlowMap" parameterType="com.domain.complex.OrderCodeFlowQuery">
        select * from tb_order_code_flow
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="orderCodeFlowMap" parameterType="com.domain.complex.OrderCodeFlowQuery">
        select * from tb_order_code_flow
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.OrderCodeFlow">
        update tb_order_code_flow
        <trim prefix="set" suffixOverrides=",">
            <if test="orderCode != null">
               order_code = #{orderCode},
            </if>
            <if test="prepayId != null">
               prepay_id = #{prepayId},
            </if>
            <if test="paymentType != null">
               payment_type = #{paymentType},
            </if>
            <if test="tradeType != null">
               trade_type = #{tradeType},
            </if>
            <if test="tradeState != null">
               trade_state = #{tradeState},
            </if>
            <if test="payerTotal != null">
               payer_total = #{payerTotal},
            </if>
            <if test="content != null">
               content = #{content},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.OrderCodeFlow" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_order_code_flow
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="orderCode != null">
                order_code,
            </if>
            <if test="prepayId != null">
                prepay_id,
            </if>
            <if test="paymentType != null">
                payment_type,
            </if>
            <if test="tradeType != null">
                trade_type,
            </if>
            <if test="tradeState != null">
                trade_state,
            </if>
            <if test="payerTotal != null">
                payer_total,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="orderCode != null">
                #{orderCode},
            </if>
            <if test="prepayId != null">
                #{prepayId},
            </if>
            <if test="paymentType != null">
                #{paymentType},
            </if>
            <if test="tradeType != null">
                #{tradeType},
            </if>
            <if test="tradeState != null">
                #{tradeState},
            </if>
            <if test="payerTotal != null">
                #{payerTotal},
            </if>
            <if test="content != null">
                #{content},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.OrderCodeFlowQuery">
        select count(*) from tb_order_code_flow
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="orderCodeFlowMap">
        select * from tb_order_code_flow where id = #{id}
    </select>

    <select id="selectByIds" resultMap="orderCodeFlowMap">
        select * from tb_order_code_flow where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_order_code_flow
        (
        order_code,
        prepay_id,
        payment_type,
        trade_type,
        trade_state,
        payer_total,
        content,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.orderCode},
        #{item.prepayId},
        #{item.paymentType},
        #{item.tradeType},
        #{item.tradeState},
        #{item.payerTotal},
        #{item.content},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>