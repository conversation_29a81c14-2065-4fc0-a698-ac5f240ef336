<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.OrderDao">

    <resultMap id="orderMap" type="com.domain.Order">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="prepay_id" property="prepayId"/>
        <result column="user_id" property="userId"/>
        <result column="user_catalog" property="userCatalog"/>
        <result column="order_catalog" property="orderCatalog"/>
        <result column="select_catalog" property="selectCatalog"/>
        <result column="amount" property="amount"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="sum_commission" property="sumCommission"/>
        <result column="commission_user_id" property="commissionUserId"/>
        <result column="sum_sales_teacher_commission" property="sumSalesTeacherCommission"/>
        <result column="sales_teacher_id" property="salesTeacherId"/>
        <result column="stage" property="stage"/>
        <result column="operate_stage" property="operateStage"/>
        <result column="delivery_catalog" property="deliveryCatalog"/>
        <result column="reservation_time" property="reservationTime"/>
        <result column="user_address_id" property="userAddressId"/>
        <result column="address_province_id" property="addressProvinceId"/>
        <result column="address_city_id" property="addressCityId"/>
        <result column="address_district_id" property="addressDistrictId"/>
        <result column="address_detail_address" property="addressDetailAddress"/>
        <result column="address_name" property="addressName"/>
        <result column="address_mobile" property="addressMobile"/>
        <result column="pay_time" property="payTime"/>
        <result column="comment" property="comment"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="prepayId != null and prepayId != ''">
                and prepay_id = #{prepayId}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userCatalog != null">
                and user_catalog = #{userCatalog}
            </if>
            <if test="orderCatalog != null and orderCatalog != ''">
                and order_catalog = #{orderCatalog}
            </if>
            <if test="selectCatalog != null and selectCatalog != ''">
                and select_catalog = #{selectCatalog}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="refundAmount != null">
                and refund_amount = #{refundAmount}
            </if>
            <if test="sumCommission != null">
                and sum_commission = #{sumCommission}
            </if>
            <if test="commissionUserId != null">
                and commission_user_id = #{commissionUserId}
            </if>
            <if test="commissionUserIds != null and commissionUserIds.size > 0">
                and commission_user_id in
                <foreach collection="commissionUserIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sumSalesTeacherCommission != null">
                and sum_sales_teacher_commission = #{sumSalesTeacherCommission}
            </if>
            <if test="salesTeacherId != null">
                and sales_teacher_id = #{salesTeacherId}
            </if>
            <if test="salesTeacherIds != null and salesTeacherIds.size > 0">
                and sales_teacher_id in
                <foreach collection="salesTeacherIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="stage != null and stage != ''">
                and stage = #{stage}
            </if>
            <if test="stages != null and stages.size > 0">
                and stage in
                <foreach collection="stages" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="operateStage != null and operateStage != ''">
                and operate_stage = #{operateStage}
            </if>
            <if test="deliveryCatalog != null and deliveryCatalog != ''">
                and delivery_catalog = #{deliveryCatalog}
            </if>
            <if test="reservationTime != null">
                and reservation_time = #{reservationTime}
            </if>
            <if test="minReservationTime != null">
                and reservation_time <![CDATA[ >= ]]> #{minReservationTime}
            </if>
            <if test="maxReservationTime != null">
                and reservation_time <![CDATA[ < ]]> #{maxReservationTime}
            </if>
            <if test="userAddressId != null">
                and user_address_id = #{userAddressId}
            </if>
            <if test="userAddressIds != null and userAddressIds.size > 0">
                and user_address_id in
                <foreach collection="userAddressIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="addressProvinceId != null">
                and address_province_id = #{addressProvinceId}
            </if>
            <if test="addressCityId != null">
                and address_city_id = #{addressCityId}
            </if>
            <if test="addressDistrictId != null">
                and address_district_id = #{addressDistrictId}
            </if>
            <if test="addressDetailAddress != null and addressDetailAddress != ''">
                and address_detail_address = #{addressDetailAddress}
            </if>
            <if test="addressName != null and addressName != ''">
                and address_name = #{addressName}
            </if>
            <if test="addressMobile != null and addressMobile != ''">
                and address_mobile = #{addressMobile}
            </if>
            <if test="payTime != null">
                and pay_time = #{payTime}
            </if>
            <if test="minPayTime != null">
                and pay_time <![CDATA[ >= ]]> #{minPayTime}
            </if>
            <if test="maxPayTime != null">
                and pay_time <![CDATA[ < ]]> #{maxPayTime}
            </if>
            <if test="comment != null and comment != ''">
                and comment = #{comment}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
            <if test="userName != null and userName != ''">
                and user_id in (select id from tb_user where and name like concat('%', #{userName}, '%'))
            </if>
            <if test="mobile != null and mobile != ''">
                and user_id in (select id from tb_user where and mobile like concat('%', #{mobile}, '%'))
            </if>
            <if test="goodsCatalog != null and goodsCatalog != ''">
                and id in (select order_id from tb_order_product where product_catalog = #{goodsCatalog})
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="orderMap" parameterType="com.domain.complex.OrderQuery">
        select * from tb_order
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="orderMap" parameterType="com.domain.complex.OrderQuery">
        select * from tb_order
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.Order">
        update tb_order
        <trim prefix="set" suffixOverrides=",">
            <if test="code != null">
               code = #{code},
            </if>
            <if test="prepayId != null">
               prepay_id = #{prepayId},
            </if>
            <if test="userId != null">
               user_id = #{userId},
            </if>
            <if test="userCatalog != null">
               user_catalog = #{userCatalog},
            </if>
            <if test="orderCatalog != null">
               order_catalog = #{orderCatalog},
            </if>
            <if test="selectCatalog != null">
               select_catalog = #{selectCatalog},
            </if>
            <if test="amount != null">
               amount = #{amount},
            </if>
            <if test="refundAmount != null">
               refund_amount = #{refundAmount},
            </if>
            <if test="sumCommission != null">
               sum_commission = #{sumCommission},
            </if>
            <if test="commissionUserId != null">
               commission_user_id = #{commissionUserId},
            </if>
            <if test="sumSalesTeacherCommission != null">
               sum_sales_teacher_commission = #{sumSalesTeacherCommission},
            </if>
            <if test="salesTeacherId != null">
               sales_teacher_id = #{salesTeacherId},
            </if>
            <if test="stage != null">
               stage = #{stage},
            </if>
            <if test="operateStage != null">
               operate_stage = #{operateStage},
            </if>
            <if test="deliveryCatalog != null">
               delivery_catalog = #{deliveryCatalog},
            </if>
            <if test="reservationTime != null">
               reservation_time = #{reservationTime},
            </if>
            <if test="userAddressId != null">
               user_address_id = #{userAddressId},
            </if>
            <if test="addressProvinceId != null">
               address_province_id = #{addressProvinceId},
            </if>
            <if test="addressCityId != null">
               address_city_id = #{addressCityId},
            </if>
            <if test="addressDistrictId != null">
               address_district_id = #{addressDistrictId},
            </if>
            <if test="addressDetailAddress != null">
               address_detail_address = #{addressDetailAddress},
            </if>
            <if test="addressName != null">
               address_name = #{addressName},
            </if>
            <if test="addressMobile != null">
               address_mobile = #{addressMobile},
            </if>
            <if test="payTime != null">
               pay_time = #{payTime},
            </if>
            <if test="comment != null">
               comment = #{comment},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.Order" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_order
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="prepayId != null">
                prepay_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userCatalog != null">
                user_catalog,
            </if>
            <if test="orderCatalog != null">
                order_catalog,
            </if>
            <if test="selectCatalog != null">
                select_catalog,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="refundAmount != null">
                refund_amount,
            </if>
            <if test="sumCommission != null">
                sum_commission,
            </if>
            <if test="commissionUserId != null">
                commission_user_id,
            </if>
            <if test="sumSalesTeacherCommission != null">
                sum_sales_teacher_commission,
            </if>
            <if test="salesTeacherId != null">
                sales_teacher_id,
            </if>
            <if test="stage != null">
                stage,
            </if>
            <if test="operateStage != null">
                operate_stage,
            </if>
            <if test="deliveryCatalog != null">
                delivery_catalog,
            </if>
            <if test="reservationTime != null">
                reservation_time,
            </if>
            <if test="userAddressId != null">
                user_address_id,
            </if>
            <if test="addressProvinceId != null">
                address_province_id,
            </if>
            <if test="addressCityId != null">
                address_city_id,
            </if>
            <if test="addressDistrictId != null">
                address_district_id,
            </if>
            <if test="addressDetailAddress != null">
                address_detail_address,
            </if>
            <if test="addressName != null">
                address_name,
            </if>
            <if test="addressMobile != null">
                address_mobile,
            </if>
            <if test="payTime != null">
                pay_time,
            </if>
            <if test="comment != null">
                comment,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="code != null">
                #{code},
            </if>
            <if test="prepayId != null">
                #{prepayId},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="userCatalog != null">
                #{userCatalog},
            </if>
            <if test="orderCatalog != null">
                #{orderCatalog},
            </if>
            <if test="selectCatalog != null">
                #{selectCatalog},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="refundAmount != null">
                #{refundAmount},
            </if>
            <if test="sumCommission != null">
                #{sumCommission},
            </if>
            <if test="commissionUserId != null">
                #{commissionUserId},
            </if>
            <if test="sumSalesTeacherCommission != null">
                #{sumSalesTeacherCommission},
            </if>
            <if test="salesTeacherId != null">
                #{salesTeacherId},
            </if>
            <if test="stage != null">
                #{stage},
            </if>
            <if test="operateStage != null">
                #{operateStage},
            </if>
            <if test="deliveryCatalog != null">
                #{deliveryCatalog},
            </if>
            <if test="reservationTime != null">
                #{reservationTime},
            </if>
            <if test="userAddressId != null">
                #{userAddressId},
            </if>
            <if test="addressProvinceId != null">
                #{addressProvinceId},
            </if>
            <if test="addressCityId != null">
                #{addressCityId},
            </if>
            <if test="addressDistrictId != null">
                #{addressDistrictId},
            </if>
            <if test="addressDetailAddress != null">
                #{addressDetailAddress},
            </if>
            <if test="addressName != null">
                #{addressName},
            </if>
            <if test="addressMobile != null">
                #{addressMobile},
            </if>
            <if test="payTime != null">
                #{payTime},
            </if>
            <if test="comment != null">
                #{comment},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.OrderQuery">
        select count(*) from tb_order
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="orderMap">
        select * from tb_order where id = #{id}
    </select>

    <select id="selectByIds" resultMap="orderMap">
        select * from tb_order where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_order
        (
        code,
        prepay_id,
        user_id,
        user_catalog,
        order_catalog,
        select_catalog,
        amount,
        refund_amount,
        sum_commission,
        commission_user_id,
        sum_sales_teacher_commission,
        sales_teacher_id,
        stage,
        operate_stage,
        delivery_catalog,
        reservation_time,
        user_address_id,
        address_province_id,
        address_city_id,
        address_district_id,
        address_detail_address,
        address_name,
        address_mobile,
        pay_time,
        comment,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.code},
        #{item.prepayId},
        #{item.userId},
        #{item.userCatalog},
        #{item.orderCatalog},
        #{item.selectCatalog},
        #{item.amount},
        #{item.refundAmount},
        #{item.sumCommission},
        #{item.commissionUserId},
        #{item.sumSalesTeacherCommission},
        #{item.salesTeacherId},
        #{item.stage},
        #{item.operateStage},
        #{item.deliveryCatalog},
        #{item.reservationTime},
        #{item.userAddressId},
        #{item.addressProvinceId},
        #{item.addressCityId},
        #{item.addressDistrictId},
        #{item.addressDetailAddress},
        #{item.addressName},
        #{item.addressMobile},
        #{item.payTime},
        #{item.comment},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
    <select id="selectByCode" resultMap="orderMap">
        select * from tb_order where `code` = #{code}
    </select>
</mapper>