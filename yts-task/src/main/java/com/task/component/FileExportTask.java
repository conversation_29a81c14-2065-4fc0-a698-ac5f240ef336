package com.task.component;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.common.bean.ExportMessage;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.bean.UserExport;
import com.common.client.FileSystemClient;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.ExportStage;
import com.common.constant.ExportType;
import com.common.constant.Gender;
import com.common.constant.OrderStage;
import com.common.constant.UserCatalog;
import com.common.constant.UserCommissionFlowCatalog;
import com.common.constant.UserCommissionFlowIncome;
import com.common.util.DateUtil;
import com.common.util.POIUtil;
import com.domain.Order;
import com.domain.User;
import com.domain.UserCommissionFlow;
import com.domain.UserTeam;
import com.domain.complex.OrderQuery;
import com.domain.complex.UserCommissionFlowQuery;
import com.domain.complex.UserQuery;
import com.domain.complex.UserTeamQuery;
import com.service.OrderProductService;
import com.service.OrderService;
import com.service.UserCommissionFlowService;
import com.service.UserCommissionService;
import com.service.UserService;
import com.service.UserTeamService;
import com.task.constant.App;

@Component
public class FileExportTask extends BaseTask{

    @Autowired
    private RedisTemplate<String,String > redisTemplate;
    @Autowired
    private UserService userService;
    @Autowired
    private UserTeamService userTeamService;
    @Autowired
    private UserCommissionFlowService userCommissionFlowService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderProductService orderProductService;
    @Autowired
    private UserCommissionService userCommissionService;


    @Scheduled(initialDelay = 30,fixedRate = 1 * 1000)
    public void start(){
        try {
            String exportMessageJson = redisTemplate.opsForList().rightPop(CacheKey.FILE_EXPORT);
            if (exportMessageJson == null){
                return;
            }
            // 先强转为父类，判断是那个导出类型
            ExportMessage exportMessage = (ExportMessage) this.getObject(exportMessageJson, ExportMessage.class);
            logger.info("file export task start ,code:{}",exportMessage.getCode());
            // 文件下载地址的缓存键
            String key = CacheKey.FILE_DOWNLOAD_URL + exportMessage.getCode();
            // 设置该状态为处理中
            redisTemplate.opsForValue().set(key, ExportStage.ING.getCode(), 1, TimeUnit.HOURS);
            String fileDownloadUrl = null;
            switch (ExportType.get(exportMessage.getType())){
                case C0:
                    // 用户分佣导出
                    fileDownloadUrl = exportCommission((UserExport) this.getObject(exportMessageJson, UserExport.class));
                    break;
                default:
                    break;
            }
            // 处理失败
            if (isEmpty(fileDownloadUrl)) {
                redisTemplate.opsForValue().set(key, ExportStage.FAIL.getCode(), 1, TimeUnit.MINUTES);
            }
            // 处理成功
            if (!isEmpty(fileDownloadUrl)) {
                redisTemplate.opsForValue().set(key, fileDownloadUrl, 1, TimeUnit.MINUTES);
            }
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }

    }


    public String exportCommission(UserExport request){
        //标题
        List<String> titles = new ArrayList<>();
        titles.add("姓名");
        titles.add("手机号");
        titles.add("性别");
        titles.add("用户类型");
        titles.add("累计分佣(元)");
        titles.add("累计订单");
        titles.add("未结算佣金(元)");
        titles.add("未结算订单");
        titles.add("已提现佣金(元)");
        titles.add("上次提现时间");
        titles.add("所属团长");
        titles.add("状态");
        titles.add("最后登录时间");
        titles.add("创建时间");
        //数据
        List<List<String>> datas = new ArrayList<>();

        UserQuery userQuery = new UserQuery();
        userQuery.setName(request.getName());
        userQuery.setMobile(request.getMobile());
        userQuery.setCatalog(request.getCatalog());
        if (!this.isEmpty(request.getCaptainUserId())){
            UserTeamQuery userTeamQuery = new UserTeamQuery();
            userTeamQuery.setCaptainUserId(request.getCaptainUserId());
            userTeamQuery.setStatus(DataStatus.Y.getCode());
            List<UserTeam> userTeamList = userTeamService.findAll(userTeamQuery);
            if (this.isEmpty(userTeamList) || userTeamList.isEmpty()){
                return getDownloadUrl("用户分佣导出.xlsx", titles, datas);
            }
            List<Long> userIdList = userTeamList.stream().map(UserTeam::getTeamMemberUserId).collect(Collectors.toList());
            userQuery.setIds(userIdList);
        }
        userQuery.setStatus(DataStatus.Y.getCode());
        List<User> users = userService.findAll(userQuery);

        List<Long> userIdList = users.stream().map(User::getId).collect(Collectors.toList());
        Map<Long,List<Order>> userIdTimeGroupOrderMap = new HashMap<>();
        Map<Long,List<Order>> userIdGroupOrderMap = new HashMap<>();
        Map<Long,List<UserTeam>> userIdGroupUserTeamMap = new HashMap<>();
        Map<Long,UserTeam> userIdCaptainUserTeamMap = new HashMap<>();
        Map<Long,User> userMap = new HashMap<>();
        Map<Long,List<UserCommissionFlow>> userIdGroupUserCommissionFlowMap = new HashMap<>();
        if (!userIdList.isEmpty()){
            OrderQuery orderQuery = new OrderQuery();
            //查询非未支付订单
            List<String> stages = new ArrayList<>();
            stages.add(OrderStage.SUCCESS.getCode());
            stages.add(OrderStage.AUTO_REFUND_PROCESSING.getCode());
            stages.add(OrderStage.AUTO_REFUND_SUCCESS.getCode());
            stages.add(OrderStage.AUTO_REFUND_ABNORMAL.getCode());
            stages.add(OrderStage.REFUND_PROCESSING.getCode());
            stages.add(OrderStage.REFUND_SUCCESS.getCode());
            stages.add(OrderStage.REFUND_ABNORMAL.getCode());
            stages.add(OrderStage.PENDING_REFUND.getCode());
            orderQuery.setStages(stages);
            orderQuery.setCommissionUserIds(userIdList);
            if (!this.isEmpty(request.getMinCreateTime())){
                orderQuery.setMinPayTime(DateUtil.parse(request.getMinCreateTime(),DATETIME_FORMAT));
            }
            if (!this.isEmpty(request.getMaxCreateTime())){
                orderQuery.setMaxPayTime(DateUtil.parse(request.getMaxCreateTime(),DATETIME_FORMAT));
            }
            orderQuery.setStatus(DataStatus.Y.getCode());
            List<Order> orderList = orderService.findAll(orderQuery);
            for (Order order : orderList) {
                if (!userIdTimeGroupOrderMap.containsKey(order.getCommissionUserId())){
                    userIdTimeGroupOrderMap.put(order.getCommissionUserId(),new ArrayList<>());
                }
                userIdTimeGroupOrderMap.get(order.getCommissionUserId()).add(order);
            }

            orderQuery = new OrderQuery();
            orderQuery.setStages(stages);
            orderQuery.setStatus(DataStatus.Y.getCode());
            List<Order> allOrderList = orderService.findAll(orderQuery);
            for (Order order : allOrderList) {
                if (!userIdGroupOrderMap.containsKey(order.getCommissionUserId())){
                    userIdGroupOrderMap.put(order.getCommissionUserId(),new ArrayList<>());
                }
                userIdGroupOrderMap.get(order.getCommissionUserId()).add(order);
            }

            List<Long> teamUserIds = new ArrayList<>();

            UserTeamQuery userTeamQuery = new UserTeamQuery();
            userTeamQuery.setCaptainUserIds(userIdList);
            if (!this.isEmpty(request.getMinCreateTime())){
                userTeamQuery.setMinCreateTime(DateUtil.parse(request.getMinCreateTime(),DATETIME_FORMAT));
            }
            if (!this.isEmpty(request.getMaxCreateTime())){
                userTeamQuery.setMaxCreateTime(DateUtil.parse(request.getMaxCreateTime(),DATETIME_FORMAT));
            }
            userTeamQuery.setStatus(DataStatus.Y.getCode());
            List<UserTeam> userTeamList = userTeamService.findAll(userTeamQuery);
            for (UserTeam userTeam : userTeamList) {
                if (!userIdGroupUserTeamMap.containsKey(userTeam.getCaptainUserId())){
                    userIdGroupUserTeamMap.put(userTeam.getCaptainUserId(),new ArrayList<>());
                }
                userIdGroupUserTeamMap.get(userTeam.getCaptainUserId()).add(userTeam);
                teamUserIds.add(userTeam.getCaptainUserId());
            }

            userTeamQuery = new UserTeamQuery();
            userTeamQuery.setTeamMemberUserIds(userIdList);
            if (!this.isEmpty(request.getMinCreateTime())){
                userTeamQuery.setMinCreateTime(DateUtil.parse(request.getMinCreateTime(),DATETIME_FORMAT));
            }
            if (!this.isEmpty(request.getMaxCreateTime())){
                userTeamQuery.setMaxCreateTime(DateUtil.parse(request.getMaxCreateTime(),DATETIME_FORMAT));
            }
            userTeamQuery.setStatus(DataStatus.Y.getCode());
            List<UserTeam> captainUserTeamList = userTeamService.findAll(userTeamQuery);
            for (UserTeam userTeam : captainUserTeamList) {
                userIdCaptainUserTeamMap.put(userTeam.getTeamMemberUserId(),userTeam);
                teamUserIds.add(userTeam.getTeamMemberUserId());
            }

            if (!teamUserIds.isEmpty()) {
                userMap = userService.findMapByIds(userIdList);
            }

            UserCommissionFlowQuery userCommissionFlowQuery = new UserCommissionFlowQuery();
            userCommissionFlowQuery.setUserIds(userIdList);
            if (!this.isEmpty(request.getMinCreateTime())){
                userCommissionFlowQuery.setMinCreateTime(DateUtil.parse(request.getMinCreateTime(),DATETIME_FORMAT));
            }
            if (!this.isEmpty(request.getMaxCreateTime())){
                userCommissionFlowQuery.setMaxCreateTime(DateUtil.parse(request.getMaxCreateTime(),DATETIME_FORMAT));
            }
            userCommissionFlowQuery.setStatus(DataStatus.Y.getCode());
            List<UserCommissionFlow> userCommissionFlowList = userCommissionFlowService.findAll(userCommissionFlowQuery);
            userIdGroupUserCommissionFlowMap = userCommissionFlowList.stream().collect(Collectors.groupingBy(UserCommissionFlow::getUserId));
        }

        for (User user : users) {
            List<String> data = new ArrayList<>();
            data.add(user.getName());
            data.add(user.getMobile());
            data.add(Gender.getName(user.getGender()));
            data.add(UserCatalog.getName(user.getCatalog()));
            if (userIdTimeGroupOrderMap.containsKey(user.getId())) {
                BigDecimal commission = userIdTimeGroupOrderMap.get(user.getId()).stream().map(Order::getSumCommission).reduce(BigDecimal.ZERO, BigDecimal::add);
                int commissionOrderCount = userIdTimeGroupOrderMap.get(user.getId()).size();
                data.add(commission.toString());
                data.add(commissionOrderCount + "");
            }else {
                data.add("0");
                data.add("0");
            }
            if (userIdGroupUserCommissionFlowMap.containsKey(user.getId())) {
                List<UserCommissionFlow> userCommissionFlows = userIdGroupUserCommissionFlowMap.get(user.getId());
                //按id正序排序
                userCommissionFlows.sort(Comparator.comparing(UserCommissionFlow::getId));

                //未结算佣金:最后一条总金额
                if (!userCommissionFlows.isEmpty()) {
                    data.add(userCommissionFlows.get(userCommissionFlows.size() - 1).getAmount().toString());
                }else {
                    data.add("0");
                }

                Integer unsettledOrderCount = 0;
                for (int i = userCommissionFlows.size() - 1; i >= 0; i--) {
                    //判断当前流水是否为提现记录
                    UserCommissionFlow userCommissionFlow = userCommissionFlows.get(i);
                    if (UserCommissionFlowCatalog.C1001.getCode().equals(userCommissionFlow.getCatalog())
                            && UserCommissionFlowIncome.C1.getCode().equals(userCommissionFlow.getIncome())) {
                        break;
                    }
                    unsettledOrderCount++;
                }
                data.add(unsettledOrderCount.toString());

                //已提现佣金：用户流水所有提现记录累加
                BigDecimal transferCommission = userCommissionFlows.stream()
                        .filter(userCommissionFlow -> UserCommissionFlowCatalog.C1001.getCode().equals(userCommissionFlow.getCatalog())
                                && UserCommissionFlowIncome.C1.getCode().equals(userCommissionFlow.getIncome()))
                        .map(UserCommissionFlow::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                data.add(transferCommission.toString());
                if (!userCommissionFlows.isEmpty()) {
                    Date createTime = userCommissionFlows.get(userCommissionFlows.size() - 1).getCreateTime();
                    if (!this.isEmpty(createTime)) {
                        data.add(DateUtil.format(createTime, DATE_FORMAT));
                    }else {
                        data.add("-");
                    }
                }else {
                    data.add("-");
                }
            }else {
                data.add("0");
                data.add("0");
                data.add("0");
                data.add("-");
                data.add("-");
            }

            if (userIdCaptainUserTeamMap.containsKey(user.getId()) && userMap.containsKey(userIdCaptainUserTeamMap.get(user.getId()).getCaptainUserId())) {
                data.add(userMap.get(userIdCaptainUserTeamMap.get(user.getId()).getCaptainUserId()).getName());
            }else {
                data.add("-");
            }

            data.add(DataStatus.getName(user.getStatus()));

            if (!this.isEmpty(user.getLastLoginTime())){
                data.add(DateUtil.format(user.getLastLoginTime(), DATE_FORMAT));
            }else {
                data.add("-");
            }

            data.add(DateUtil.format(user.getCreateTime(), DATE_FORMAT));
            datas.add(data);
        }
        return getDownloadUrl("用户分佣导出.xlsx", titles, datas);
    }

    /**
     * 获取文件下载地址
     * @return
     */
    private String getDownloadUrl(String filename, List<String> titles, List<List<String>> datas) {
        File file = this.getDestFile(filename);
        POIUtil.create(file, titles, datas);
        FileSystemClient<String> fileSystemClientUpload = new FileSystemClient<>();
        Response<String> response = fileSystemClientUpload.upload(App.FILESYSTEM_URL + "/v1/file/upload", file, String.class);
        if (response != null) {
            if (Response.OK.equals(response.getCode())) {
                String downloadUrl = response.getData();
                logger.info("file url :{}", downloadUrl);
                return downloadUrl;
            }
        }
        return null;
    }
}

