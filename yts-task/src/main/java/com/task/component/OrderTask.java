package com.task.component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.common.constant.App;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.OrderCatalog;
import com.common.constant.OrderStage;
import com.common.util.DateUtil;
import com.domain.GoodsSpecificationSku;
import com.domain.Order;
import com.domain.OrderProduct;
import com.domain.complex.OrderProductQuery;
import com.service.GoodsSpecificationSkuService;
import com.service.OrderProductService;
import com.service.OrderService;

@Component
public class OrderTask extends BaseTask{

    @Autowired
    private GoodsSpecificationSkuService goodsSpecificationSkuService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderProductService orderProductService;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    /**
     * 订单超时自动取消
     */
    @Scheduled(initialDelay = 1000,fixedDelay = 1000)
    public void autoCancel(){
        while (true) {
            Set<String> ids = redisTemplate.opsForZSet().rangeByScore(CacheKey.ORDER_EXPIRE_QUEUE, 0, DateUtil.getServerTime().getTime());
            if (ids == null || ids.isEmpty()) {
                break;
            }
            logger.info("auto cancel order task start");
            for (String id : ids) {
                Order order = orderService.findById(Long.valueOf(id));
                if (order == null) {
                    redisTemplate.opsForZSet().remove(CacheKey.ORDER_EXPIRE_QUEUE, id);
                    continue;
                }
                Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_ORDER, id);
                if (!lock.tryLock()) {
                    logger.info("auto cancel order task end");
                    return;
                }
                try {
                    if (!order.getStage().equals(OrderStage.NOTPAY.getCode())){
                        redisTemplate.opsForZSet().remove(CacheKey.ORDER_EXPIRE_QUEUE, id);
                    }
                    Date serverTime = this.getServerTime();
                    // 判断是否超时未支付
                    if (serverTime.after(DateUtil.getFutureMinute(order.getCreateTime(), App.USER_ORDER_EXPIRE_TIME)) && OrderStage.NOTPAY.getCode().equals(order.getStage())) {
                        // 修改订单为超时
                        Order orderModify = new Order();
                        orderModify.setId(order.getId());
                        orderModify.setStage(OrderStage.CLOSED.getCode());
                        orderModify.setModifyTime(serverTime);

                        // 只有商品订单才需要返还库存
                        List<GoodsSpecificationSku> goodsSpecificationSkus = new ArrayList<>();
                        if (OrderCatalog.C1.getCode().equals(order.getOrderCatalog())) {
                            // 查询订单商品信息，准备返还库存
                            OrderProductQuery orderProductQuery = new OrderProductQuery();
                            orderProductQuery.setOrderId(order.getId());
                            orderProductQuery.setStatus(DataStatus.Y.getCode());
                            List<OrderProduct> orderProductList = orderProductService.findAll(orderProductQuery);

                            // 返还商品规格SKU库存
                            for (OrderProduct orderProduct : orderProductList) {
                                // 返还到GoodsSpecificationSku表中
                                if (orderProduct.getSpecValues() == null) {
                                    continue;
                                }
                                GoodsSpecificationSku goodsSpecificationSku = goodsSpecificationSkuService.findBySpecValuesAndGoodsId(orderProduct.getSpecValues(),orderProduct.getProductId());
                                if (goodsSpecificationSku != null && goodsSpecificationSku.getStock() != null) {
                                    GoodsSpecificationSku updateSku = new GoodsSpecificationSku();
                                    updateSku.setId(goodsSpecificationSku.getId());
                                    updateSku.setStock(goodsSpecificationSku.getStock() + orderProduct.getNumber());
                                    updateSku.setModifyTime(serverTime);
                                    goodsSpecificationSkus.add(updateSku);
                                }
                            }
                        }
                        orderService.modifyById(orderModify,goodsSpecificationSkus);
                        redisTemplate.opsForZSet().remove(CacheKey.ORDER_EXPIRE_QUEUE, id);
                    }
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                } finally {
                    lock.unlock();
                }
            }
        }
    }
}
