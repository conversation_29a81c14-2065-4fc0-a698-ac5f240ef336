package com.task.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.common.bean.ExportMessage;
import com.common.bean.UserCommissionDTO;
import com.common.bean.UserExport;
import com.common.constant.App;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.ExportStage;
import com.common.constant.ExportType;
import com.common.constant.OrderCatalog;
import com.common.constant.OrderStage;
import com.common.constant.UserCommissionFlowCatalog;
import com.common.constant.UserCommissionFlowIncome;
import com.common.constant.UserCommissionFlowPayChannel;
import com.common.util.DateUtil;
import com.domain.GoodsSpecificationSku;
import com.domain.Order;
import com.domain.OrderProduct;
import com.domain.UserCommission;
import com.domain.UserCommissionCashFlow;
import com.domain.UserCommissionFlow;
import com.domain.complex.OrderProductQuery;
import com.domain.complex.UserCommissionCashFlowQuery;
import com.service.GoodsSpecificationSkuService;
import com.service.OrderProductService;
import com.service.OrderService;
import com.service.UserCommissionCashFlowService;
import com.service.UserCommissionService;

@Component
public class UserCommissionTask extends BaseTask{

    @Autowired
    private UserCommissionService userCommissionService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private UserCommissionCashFlowService userCommissionCashFlowService;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    /**
     * 添加用户佣金
     */
    @Scheduled(initialDelay = 30,fixedRate = 1 * 1000)
    public void start(){
        String userCommissionJson = redisTemplate.opsForList().rightPop(CacheKey.USER_COMMISSION);
        if (userCommissionJson == null){
            return;
        }
        // 先强转为父类，判断是那个导出类型
        UserCommissionDTO userCommissionDTO = (UserCommissionDTO) this.getObject(userCommissionJson, UserCommissionDTO.class);

        logger.info("user commission task start ,dto:{}",userCommissionJson);

        //获取用户分佣流水操作锁
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_COMMISSION, userCommissionDTO.getUserId().toString());
        if (!lock.tryLock()) {
            logger.info("user commission task end");
            redisTemplate.opsForList().leftPush(CacheKey.USER_COMMISSION, userCommissionJson);
            return;
        }
        try {
            Boolean result = false;
            switch (UserCommissionFlowCatalog.get(userCommissionDTO.getCatalog())){
                case C1000:
                    // 分佣流水添加
                    result = userCommissionFlowCreate(userCommissionDTO);
                    break;
                case C1001:
                    // 提现流水扣除
                    result = userCommissionCashRemove(userCommissionDTO);
                    break;
                case C1002:
                    // 退款流水扣除
                    result = userCommissionRefundRemove(userCommissionDTO);
                    break;
                default:
                    break;
            }
            //没处理成功需要重新放进去
            if (!result){
                redisTemplate.opsForList().leftPush(CacheKey.USER_COMMISSION, userCommissionJson);
            }

            logger.info("user commission task end");
        }catch (Exception e){
            redisTemplate.opsForList().leftPush(CacheKey.USER_COMMISSION, userCommissionJson);
            logger.error(e.getMessage(),e);
        }finally {
            lock.unlock();
        }

    }

    private Boolean userCommissionFlowCreate(UserCommissionDTO userCommissionDTO) {
        Date serverTime = this.getServerTime();

        Order order = orderService.findById(userCommissionDTO.getOrderId());
        if (order == null){
            //数据异常不进行重新处理
            return true;
        }
        //订单如果没支付成功直接返回
        if (!OrderStage.SUCCESS.getCode().equals(order.getStage())){
            //数据异常不进行重新处理
            return true;
        }
        //判断当前订单是否有佣金人
        if (order.getCommissionUserId() == null){
            //数据异常不进行重新处理
            return true;
        }
        //查询用户总佣金
        UserCommission userCommissionForDB = userCommissionService.findByUserId(order.getCommissionUserId());
        UserCommission userCommission = new UserCommission();
        if (userCommissionForDB == null) {
            userCommission.setUserId(order.getCommissionUserId());
            userCommission.setAmount(order.getSumCommission());
            userCommission.setTotalAmount(order.getSumCommission());
            userCommission.setCreateTime(serverTime);
            userCommission.setModifyTime(serverTime);
            userCommission.setStatus(DataStatus.Y.getCode());
        }else {
            userCommission.setId(userCommissionForDB.getId());
            userCommission.setAmount(userCommissionForDB.getAmount().add(order.getSumCommission()));
            userCommission.setTotalAmount(userCommissionForDB.getTotalAmount().add(order.getSumCommission()));
            userCommission.setModifyTime(serverTime);
        }

        UserCommissionFlow userCommissionFlow = new UserCommissionFlow();
        userCommissionFlow.setFlowCode(this.getFlowCode());
        userCommissionFlow.setUserId(order.getCommissionUserId());
        userCommissionFlow.setOrderId(order.getId());
        userCommissionFlow.setOrderCode(order.getCode());
        userCommissionFlow.setTransactionTime(serverTime);
        userCommissionFlow.setFlowAmount(order.getSumCommission());
        if (!this.isEmpty(userCommissionForDB) && !this.isEmpty(userCommissionForDB.getAmount())) {
            userCommissionFlow.setAmount(userCommissionForDB.getAmount().add(order.getSumCommission()));
        }else {
            userCommissionFlow.setAmount(order.getSumCommission());
        }
        userCommissionFlow.setCatalog(UserCommissionFlowCatalog.C1000.getCode());
        userCommissionFlow.setPayChannel(UserCommissionFlowPayChannel.C1000.getCode());
        userCommissionFlow.setIncome(UserCommissionFlowIncome.C0.getCode());
        userCommissionFlow.setLabel("团员下单增加分佣");
        userCommissionFlow.setComment("团员下单增加分佣");
        userCommissionFlow.setStatus(DataStatus.Y.getCode());
        userCommissionFlow.setModifyTime(serverTime);
        userCommissionFlow.setCreateTime(serverTime);
        userCommissionService.save(userCommission,userCommissionFlow);
        return true;
    }

    private Boolean userCommissionCashRemove(UserCommissionDTO userCommissionDTO) {

        return null;
    }

    private Boolean userCommissionRefundRemove(UserCommissionDTO userCommissionDTO) {
        Date serverTime = this.getServerTime();

        Order order = orderService.findById(userCommissionDTO.getOrderId());
        if (order == null){
            //数据异常不进行重新处理
            return true;
        }
        //订单如果没已手动退款直接返回
        if (!(OrderStage.REFUND_SUCCESS.getCode().equals(order.getStage()) || OrderStage.AUTO_REFUND_SUCCESS.getCode().equals(order.getStage()))){
            //数据异常不进行重新处理
            return true;
        }
        //判断当前订单是否有佣金人
        if (order.getCommissionUserId() == null){
            //数据异常不进行重新处理
            return true;
        }

        //查询当前订单是否已经提现过，提现过不予扣除
        UserCommissionCashFlowQuery userCommissionCashFlowQuery = new UserCommissionCashFlowQuery();
        userCommissionCashFlowQuery.setUserId(order.getCommissionUserId());
        userCommissionCashFlowQuery.setOrderId(order.getId());
        userCommissionCashFlowQuery.setStatus(DataStatus.Y.getCode());
        List<UserCommissionCashFlow> userCommissionCashFlowList = userCommissionCashFlowService.findAll(userCommissionCashFlowQuery);
        if (!userCommissionCashFlowList.isEmpty()) {
            return true;
        }

        //查询用户总佣金
        UserCommission userCommissionForDB = userCommissionService.findById(order.getCommissionUserId());
        UserCommission userCommission = new UserCommission();
        if (userCommissionForDB == null) {
            userCommission.setUserId(order.getCommissionUserId());
            userCommission.setAmount(BigDecimal.ZERO.subtract(order.getSumCommission()));
            userCommission.setTotalAmount(BigDecimal.ZERO.subtract(order.getSumCommission()));
            userCommission.setCreateTime(serverTime);
            userCommission.setModifyTime(serverTime);
            userCommission.setStatus(DataStatus.Y.getCode());
        }else {
            userCommission.setId(userCommissionForDB.getId());
            userCommission.setAmount(userCommissionForDB.getAmount().add(order.getSumCommission()));
            userCommission.setTotalAmount(userCommissionForDB.getTotalAmount().add(order.getSumCommission()));
            userCommission.setModifyTime(serverTime);
        }

        // 准备创建退款扣除分佣流水记录
        UserCommissionFlow userCommissionFlow = new UserCommissionFlow();
        userCommissionFlow.setFlowCode(this.getFlowCode());
        userCommissionFlow.setUserId(order.getCommissionUserId());
        userCommissionFlow.setOrderId(order.getId());
        userCommissionFlow.setOrderCode(order.getCode());
        userCommissionFlow.setTransactionTime(serverTime);
        userCommissionFlow.setFlowAmount(BigDecimal.ZERO.subtract(order.getSumCommission()));
        if (!this.isEmpty(userCommission) && !this.isEmpty(userCommission.getAmount())) {
            userCommissionFlow.setAmount(userCommission.getAmount().subtract(order.getSumCommission()));
        }else {
            userCommissionFlow.setAmount(BigDecimal.ZERO.subtract(order.getSumCommission()));
        }
        userCommissionFlow.setCatalog(UserCommissionFlowCatalog.C1002.getCode());
        userCommissionFlow.setIncome(UserCommissionFlowIncome.C1.getCode());
        userCommissionFlow.setLabel("团员订单退款扣除分佣");
        userCommissionFlow.setComment("团员订单退款扣除分佣");
        userCommissionFlow.setStatus(DataStatus.Y.getCode());
        userCommissionFlow.setModifyTime(serverTime);
        userCommissionFlow.setCreateTime(serverTime);
        userCommissionService.save(userCommission,userCommissionFlow);
        return true;
    }
}
