package com.task.component;

import java.io.File;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.Lock;

import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.integration.redis.util.RedisLockRegistry;

import com.common.util.DateUtil;
import com.common.util.FileUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.task.constant.App;
import com.task.sequence.CommonSequence;
import com.task.sequence.FileNameSequence;

public class BaseTask {
	Logger logger = LoggerFactory.getLogger(this.getClass());
	
	protected final String DATE_FORMAT = "yyyy-MM-dd";
	protected final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	
	String getJSON(Object obj){
		ObjectMapper objectMapper = new ObjectMapper();
		try{
			return objectMapper.writeValueAsString(obj);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
	
	Lock getLock(RedisTemplate<String, String> redisTemplate,String lockKeyPrefix, String lockKey){
		return new RedisLockRegistry(redisTemplate.getConnectionFactory(), lockKeyPrefix).obtain(lockKey);
	}
	
	Boolean isEmpty(String str){
		if(str != null){
			str = str.trim();
			if(!"".equals(str) && !"null".equalsIgnoreCase(str) && !"undefined".equalsIgnoreCase(str)){
				return false;
			}
		}
		return true;
	}
	Boolean isEmpty(Object obj){
		if(obj != null){
			if(!"".equals(obj) && !"null".equals(obj) && !"undefined".equals(obj)){
				return false;
			}
		}
		return true;
	}
	Integer getInteger(String str){
		if(isEmpty(str)){
			return null;
		}
		return Integer.valueOf(str);
	}

	/**
	 * 获取订单号
	 */
	String getOrderCode(){
		return this.getDateTime() + App.ID + CommonSequence.nextValue();
	}

	/**
	 * 获取流水号
	 */
	String getFlowCode(){
		return com.common.constant.App.USER_FLOW_PREFIX + this.getDateTime() + App.ID + CommonSequence.nextValue();
	}
	
	String getString(BigDecimal bigDecimal){
		if(bigDecimal != null){
			return bigDecimal.toString();
		}
		return null;
	}
	
	String getString(Integer integer){
		if(integer != null){
			return integer.toString();
		}
		return null;
	}

	Object getObject(String str, Class<?> clazz) {
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
			return objectMapper.readValue(str, clazz);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
	
	String getDateTime(){
		return DateUtil.getServerTime("yyyyMMddHHmmss");
	}
	String getDateTime(Date date,String format){
		return DateUtil.format(date, format);
	}
	Date getServerTime(){
		return DateUtil.getServerTime();
	}
	
	String getName(String fileName){
		fileName = fileName.replace("\\", "/");
		int index = fileName.lastIndexOf("/");
		if(index == -1){
			return fileName;
		}
		return fileName.substring(index + 1);
	}
    String getExtension(String fileName){
		fileName = this.getName(fileName);
		int index = fileName.lastIndexOf(App.DOT);
		if(index == -1){
			return "";
		}
		return fileName.substring(index);
	}
    File getDestFile(String fileName){
		String datetime = DateUtil.getServerTime("yyyyMMddHHmmss");
		String dir = new StringBuilder(App.FILE_LOCATION)
				.append("/").append(datetime.substring(0,4))
				.append("/").append(datetime.substring(4,6))
				.append("/").append(datetime.substring(6,8))
				.append("/").append(datetime.substring(8,10))
				.append("/").append(datetime.substring(10,12)).toString();
		FileUtil.make(dir);
		File file = new File(dir,new StringBuilder()
				.append(datetime.substring(12,14))
				.append(FileNameSequence.nextValue())
				.append(this.getExtension(fileName)).toString());
		return file;
	}
	String getAdToken(String url) {
		String query = url.substring(url.indexOf("?") + 1);
		List<NameValuePair> nameValuePairs = URLEncodedUtils.parse(query, StandardCharsets.UTF_8);
		for (NameValuePair nameValuePair : nameValuePairs) {
			if ("token".equalsIgnoreCase(nameValuePair.getName())) {
				return nameValuePair.getValue();
			}
		}
		return null;
	}

	String getAdClient(String url) {
		String query = url.substring(url.indexOf("?") + 1);
		List<NameValuePair> nameValuePairs = URLEncodedUtils.parse(query,StandardCharsets.UTF_8);
		for (NameValuePair nameValuePair : nameValuePairs) {
			if ("client".equalsIgnoreCase(nameValuePair.getName())) {
				return nameValuePair.getValue();
			}
		}
		return null;
	}

    String getFileSystemUrl(String url) {
    	return App.FILESYSTEM_URL + "/v1/file/download?url=" + url;
    }

}
