package com.task.component;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.util.DateUtil;
import com.domain.Log;
import com.domain.complex.LogQuery;

@Component
public class LogTask extends BaseTask {
	@Autowired
	private com.service.LogService logService;
	@Autowired
	private RedisTemplate<String,String > redisTemplate;

	/**
	 * 日志清除
	 */
	@Scheduled(cron = "0 0 3 * * ? ")  //每天凌晨3点执行
	public void start() {
		try {
			//删除一个月前的日志
			LogQuery logQuery = new LogQuery();
			logQuery.setMaxCreateTime(DateUtil.getFutureMonth(DateUtil.getServerTime(),-1));
			List<Log> logList = logService.findAll(logQuery);
			for (Log log : logList) {
				logService.removeById(log.getId());
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}

	@Scheduled(initialDelay = 1000,fixedRate = 1 * 1000)
	public void createLogStart(){
		try {
			Date serverTime = DateUtil.getServerTime();
			String logMessageJson = redisTemplate.opsForList().rightPop(CacheKey.LOG);
			if (logMessageJson == null){
				return;
			}
			Log log = (Log) this.getObject(logMessageJson, Log.class);

			//计算时长
			if (!this.isEmpty(log.getEndTime()) && !this.isEmpty(log.getStartTime())) {
				Long duration = log.getEndTime().getTime() - log.getStartTime().getTime();
				log.setDuration(duration.intValue());
			}
			log.setCreateTime(serverTime);
			log.setModifyTime(serverTime);
			log.setStatus(DataStatus.Y.getCode());
			logService.create(log);

		}catch (Exception e){
			logger.error(e.getMessage(),e);
		}
	}
}
