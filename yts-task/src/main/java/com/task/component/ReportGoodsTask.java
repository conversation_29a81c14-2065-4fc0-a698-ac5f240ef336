package com.task.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.common.constant.DataStatus;
import com.common.constant.OrderCatalog;
import com.common.constant.OrderStage;
import com.common.util.DateUtil;
import com.domain.Order;
import com.domain.OrderProduct;
import com.domain.ProductBrowse;
import com.domain.ReportGoods;
import com.domain.complex.OrderProductQuery;
import com.domain.complex.OrderQuery;
import com.domain.complex.ProductBrowseQuery;
import com.domain.complex.ReportGoodsQuery;
import com.service.OrderProductService;
import com.service.OrderService;
import com.service.ProductBrowseService;
import com.service.ReportGoodsService;

@Component
public class ReportGoodsTask extends BaseTask{

    @Autowired
    private ReportGoodsService reportGoodsService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderProductService orderProductService;
    @Autowired
    private ProductBrowseService productBrowseService;

    @Scheduled(initialDelay = 1000,fixedDelay = 60 * 1000)
    public void todaySync(){
        try {
            logger.info("ReportGoodsTask todaySync 开始执行任务...");
            reportGoodsSync(getServerTime());
            logger.info("ReportGoodsTask todaySync 任务执行完毕...");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
    }

    @Scheduled(cron = "0 0 1 * * ? ")
    public void yesterdaySync(){
        try {
            logger.info("ReportGoodsTask yesterdaySync 开始执行任务...");
            reportGoodsSync(DateUtil.getFutureDay(getServerTime(), -1));
            logger.info("ReportGoodsTask yesterdaySync 任务执行完毕...");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
    }

    public void reportGoodsSync(Date serverTime){
        try {
            List<Long> goodsIds = new ArrayList<>();

            OrderQuery orderQuery = new OrderQuery();
            orderQuery.setOrderCatalog(OrderCatalog.C1.getCode());
            orderQuery.setMinPayTime(DateUtil.getDayStartTime(serverTime));
            orderQuery.setMaxPayTime(DateUtil.getDayEndTime(serverTime));
            List<String> stages = new ArrayList<>();
            stages.add(OrderStage.SUCCESS.getCode());
            stages.add(OrderStage.AUTO_REFUND_SUCCESS.getCode());
            stages.add(OrderStage.REFUND_SUCCESS.getCode());
            orderQuery.setStages(stages);
            orderQuery.setStatus(DataStatus.Y.getCode());
            List<Order> orders = orderService.findAll(orderQuery);

            List<Long> orderIds = new ArrayList<>();
            Map<Long, Order> orderMap = new HashMap<>();
            Map<Long, List<Long>> goodsIdGroupOrderIdMap = new HashMap<>();
            for (Order order : orders) {
                orderIds.add(order.getId());
                orderMap.put(order.getId(),order);
            }

            if (!orderIds.isEmpty()){
                OrderProductQuery orderProductQuery = new OrderProductQuery();
                orderProductQuery.setOrderIds(orderIds);
                orderProductQuery.setProductCatalog(OrderCatalog.C1.getCode());
                List<OrderProduct> orderProducts = orderProductService.findAll(orderProductQuery);
                for (OrderProduct orderProduct : orderProducts) {
                    goodsIds.add(orderProduct.getProductId());
                    if (!goodsIdGroupOrderIdMap.containsKey(orderProduct.getProductId())){
                        goodsIdGroupOrderIdMap.put(orderProduct.getProductId(),new ArrayList<>());
                    }
                    goodsIdGroupOrderIdMap.get(orderProduct.getProductId()).add(orderProduct.getOrderId());
                }
            }

            ProductBrowseQuery productBrowseQuery = new ProductBrowseQuery();
            productBrowseQuery.setMinCreateTime(DateUtil.getDayStartTime(serverTime));
            productBrowseQuery.setMaxCreateTime(DateUtil.getDayEndTime(serverTime));
            productBrowseQuery.setStatus(DataStatus.Y.getCode());
            List<ProductBrowse> productBrowseList = productBrowseService.findAll(productBrowseQuery);
            Map<Long,List<ProductBrowse>> productBrowseMap = productBrowseList.stream().collect(Collectors.groupingBy(ProductBrowse::getProductId));
            for (ProductBrowse productBrowse : productBrowseList) {
                goodsIds.add(productBrowse.getProductId());
            }

            for (Long goodsId : goodsIds) {
                ReportGoods reportGoods = new ReportGoods();
                reportGoods.setCreateTime(serverTime);
                reportGoods.setModifyTime(serverTime);
                reportGoods.setStatus(DataStatus.Y.getCode());
                reportGoods.setGoodsId(goodsId);
                reportGoods.setDate(DateUtil.getDayStartTime(serverTime));
                reportGoods.setBrowseNum(productBrowseMap.containsKey(goodsId)?productBrowseMap.get(goodsId).size():0L);
                if (goodsIdGroupOrderIdMap.containsKey(goodsId)){
                    List<Long> orderIdList = goodsIdGroupOrderIdMap.get(goodsId);
                    Long payNum = 0L;
                    BigDecimal payAmount = BigDecimal.ZERO;
                    Long refundNum = 0L;
                    BigDecimal refundAmount = BigDecimal.ZERO;
                    for (Long orderId : orderIdList) {
                        if (!orderMap.containsKey(orderId)){
                            continue;
                        }
                        payNum ++;
                        Order order = orderMap.get(orderId);
                        payAmount = payAmount.add(order.getAmount());
                        if (OrderStage.AUTO_REFUND_SUCCESS.getCode().equals(order.getStage()) || OrderStage.REFUND_SUCCESS.getCode().equals(order.getStage())){
                            refundNum ++;
                            refundAmount = refundAmount.add(order.getRefundAmount());
                        }
                    }
                    reportGoods.setPayNum(payNum);
                    reportGoods.setPayAmount(payAmount);
                    reportGoods.setRefundNum(refundNum);
                    reportGoods.setRefundAmount(refundAmount);
                }else {
                    reportGoods.setPayNum(0L);
                    reportGoods.setPayAmount(BigDecimal.ZERO);
                    reportGoods.setRefundNum(0L);
                    reportGoods.setRefundAmount(BigDecimal.ZERO);
                }

                ReportGoodsQuery reportGoodsQuery = new ReportGoodsQuery();
                reportGoodsQuery.setDate(DateUtil.getDayStartTime(serverTime));
                reportGoodsQuery.setGoodsId(goodsId);
                reportGoodsQuery.setStatus(DataStatus.Y.getCode());
                List<ReportGoods> reportGoodsList = reportGoodsService.findAll(reportGoodsQuery);
                if (reportGoodsList != null && !reportGoodsList.isEmpty()){
                    reportGoods.setId(reportGoodsList.get(0).getId());
                    reportGoods.setCreateTime(null);
                    reportGoodsService.modifyById(reportGoods);
                }else {
                    reportGoodsService.create(reportGoods);
                }
            }
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
    }
}
