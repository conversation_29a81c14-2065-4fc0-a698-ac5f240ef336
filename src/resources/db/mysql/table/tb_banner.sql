drop table if exists `tb_banner`;
create table `tb_banner` (
    `id` bigint not null auto_increment comment '主键',
    `img_url` varchar(128) comment '图片地址',
    `jump` varchar(1) comment '是否跳转(0:跳转 1:不跳转)',
    `jump_catalog` varchar(4) comment '跳转类型',
    `jump_id` bigint comment '跳转编号',
    `stage` varchar(1) comment '上架状态(0:上架 1:下架)',
    `sort` int comment '排序字段',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment='轮播图表';