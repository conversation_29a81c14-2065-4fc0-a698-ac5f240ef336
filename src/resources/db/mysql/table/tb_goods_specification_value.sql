drop table if exists `tb_goods_specification_value`;
create table `tb_goods_specification_value` (
    `id` bigint not null auto_increment comment '主键',
    `goods_id` bigint comment '商品id',
    `goods_specification_type_id` bigint comment '规格类型ID',
    `name` varchar(64) comment '规格值',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time`datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '商品规格值表';