drop table if exists `tb_user_address` ;
create table `tb_user_address` (
	`id` bigint not null auto_increment comment '主键',
	`user_id` bigint comment '用户编号',
	`province_id` bigint comment '省份编号',
	`city_id` bigint comment '城市编号',
	`district_id` bigint comment '区县编号',
	`detail_address` varchar(500) comment '详细地址',
	`name` varchar(64) comment '姓名',
    `mobile` varchar(15) comment '手机号',
    `default_address` varchar(1) comment '是否默认地址(0:是 1:否)',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户地址表';