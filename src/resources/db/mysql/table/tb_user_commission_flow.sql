drop table if exists `tb_user_commission_flow` ;
create table `tb_user_commission_flow` (
    `id` bigint not null auto_increment comment '主键',
    `flow_code` varchar(64) comment '流水号',
    `user_id` bigint comment '用户编号',
    `order_id` bigint comment '订单ID',
    `order_code` varchar(64) comment '订单号',
    `prepay_id` varchar(64) comment '预支付交易会话标识',
    `transaction_time` datetime comment '交易时间',
    `flow_amount` decimal(10,2) comment '流转金额',
    `amount` decimal(10,2) comment '总金额',
    `catalog` varchar(4) comment '分类(1000:分佣流水)',
    `pay_channel` varchar(4) comment '支付方式(1000:微信小程序支付)',
    `income` varchar(1) comment '收支出(0:收入 1:支出)',
    `label` varchar(100) comment '标签',
    `comment` varchar(64) comment '备注',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户佣金明细表';
alter table `tb_user_commission_flow` add index `idx_user_commission_flow_01` (`user_id`);
alter table `tb_user_commission_flow` add index `idx_user_commission_flow_02` (`order_code`);
alter table `tb_user_commission_flow` add unique index `idx_user_commission_flow_03` (`flow_code`);