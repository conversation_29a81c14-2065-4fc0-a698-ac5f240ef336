drop table if exists `tb_service_appointment`;
create table `tb_service_appointment` (
    `id` bigint not null auto_increment comment '主键',
    `user_id` bigint comment '用户id',
    `service_id` bigint comment '服务id',
    `name` varchar(64) comment '姓名',
    `mobile` varchar(15) comment '手机号',
    `remark` text comment '备注',
    `contact` varchar(1) comment '是否联系(0:已联系 1:未联系)',
    `comment` text comment '描述',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time`datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '服务预约表';
alter table `tb_service_appointment` add index `idx_service_appointment_01` (`user_id`);
alter table `tb_service_appointment` add index `idx_service_appointment_02` (`service_id`);