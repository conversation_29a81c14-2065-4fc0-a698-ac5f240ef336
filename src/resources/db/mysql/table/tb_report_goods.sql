drop table if exists `tb_report_goods` ;
create table `tb_report_goods` (
	`id` bigint not null auto_increment comment '主键',
    `date` date comment '日期',
    `goods_id` bigint comment '商品编号',
    `browse_num` bigint comment '浏览数量',
    `pay_num` bigint comment '支付数量',
    `pay_amount` decimal(10,2) comment '支付金额',
    `refund_num` bigint comment '退款数量',
    `refund_amount` decimal(10,2) comment '退款金额',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time`datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '统计商品表';
alter table `tb_report_goods` add unique index `idx_report_goods_01` (`goods_id`);