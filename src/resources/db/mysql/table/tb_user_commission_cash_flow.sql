drop table if exists `tb_user_commission_cash_flow` ;
create table `tb_user_commission_cash_flow` (
	`id` bigint not null auto_increment comment '主键',
	`user_id` bigint comment '用户编号',
    `order_id` bigint comment '订单编号',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户佣金提现明细表';
