drop table if exists `tb_wechat_app` ;
create table `tb_wechat_app` (
	`id` bigint not null auto_increment comment '主键',
	`appId` varchar(64) comment '应用ID',
	`secret` varchar(32) comment '应用密钥',
	`name` varchar(10) comment '应用名称',
	`access_token` varchar(2000) comment '访问凭证',
	`access_token_datetime` datetime comment '凭证刷新日期',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '微信应用表';