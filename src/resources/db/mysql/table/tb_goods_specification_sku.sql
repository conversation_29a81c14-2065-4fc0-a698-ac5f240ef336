drop table if exists `tb_goods_specification_sku`;
create table `tb_goods_specification_sku` (
    `id` bigint not null auto_increment comment '主键',
    `goods_id` bigint comment '商品id',
    `spec_values` text not null comment 'SKU属性',
    `img_url` varchar(256) comment '商品封面',
    `buying_price` decimal(10,2) comment '成本价',
    `original_price` decimal(10,2) comment '划线价',
    `price` decimal(10,2) comment '售价',
    `gross_profit` decimal(10,2) comment '毛利(售价-成本价)',
    `stock` int not null comment '库存数量',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time`datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '商品规格sku表';