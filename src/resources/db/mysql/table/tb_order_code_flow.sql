drop table if exists `tb_order_code_flow` ;
create table `tb_order_code_flow` (
	`id` bigint not null auto_increment comment '主键',
    `order_code` varchar(64) comment '订单号',
    `prepay_id` varchar(64) comment '预支付交易会话标识',
    `payment_type` varchar(4) comment '支付类型(1000:微信小程序 1001:支付宝)',
    `trade_type` varchar(20) comment '交易类型',
    `trade_state` varchar(50) comment '交易状态',
    `payer_total` int(11) comment '支付金额(分)',
    `content` text comment '通知参数',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '订单号流水表';