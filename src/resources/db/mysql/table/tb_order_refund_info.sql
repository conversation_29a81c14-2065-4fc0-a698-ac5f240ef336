drop table if exists `tb_order_refund_info` ;
create table `tb_order_refund_info` (
	`id` bigint not null auto_increment comment '主键',
    `order_no` varchar(50) comment '商户订单编号',
    `refund_no` varchar(50) comment '商户退款单编号',
    `refund_id` varchar(50) comment '支付系统退款单号',
    `total_fee` int(11) comment '原订单金额(分)',
    `refund` int(11) comment '退款金额(分)',
    `reason` varchar(50) comment '退款原因',
    `refund_status` varchar(10) comment '退款状态',
    `content_return` text comment '申请退款返回参数',
    `content_notify` text comment '退款结果通知参数',
    `payment_type` varchar(4) comment '支付类型',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time`datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '订单退款记录表';