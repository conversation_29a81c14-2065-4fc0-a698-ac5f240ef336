drop table if exists `tb_sales_teacher`;
create table `tb_sales_teacher` (
    `id` bigint not null auto_increment comment '主键',
    `name` varchar(20) comment '姓名',
    `gender` varchar(1) comment '性别(0:男 1:女)',
    `mobile` varchar(11) comment '手机号',
    `stage` varchar(1) comment '启用/禁用(0:启用 1:禁用)',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '销售老师表';
alter table `tb_sales_teacher` add index `idx_sales_teacher_01` (`mobile`);
alter table `tb_sales_teacher` add index `idx_sales_teacher_02` (`name`);