drop table if exists `tb_order` ;
create table `tb_order` (
	`id` bigint not null auto_increment comment '主键',
    `code` varchar(64) comment '订单号',
    `prepay_id` varchar(64) comment '预支付交易会话标识',
    `user_id` bigint comment '用户id',
    `user_catalog` varchar(4) comment '用户类型',
    `order_catalog` varchar(4) comment '订单类型(0:课程 1:商品)',
    `select_catalog` varchar(4) comment '选择类型(0:自选 1:帮选)',
    `amount` decimal(10,2) comment '实付金额',
    `refund_amount` decimal(10,2) comment '退款金额',
    `sum_commission` decimal(10,2) comment '团长总佣金',
    `commission_user_id` bigint comment '团长佣金人',
    `sum_sales_teacher_commission` decimal(10,2) comment '销售老师佣金金额',
    `sales_teacher_id` bigint comment '销售老师ID',
    `stage` varchar(1) comment '支付状态',
    `operate_stage` varchar(1) comment '提货状态(0:已提货 1:未提货)',
    `delivery_catalog` varchar(1) comment '配送方式(0:自提 1:派送)',
    `reservation_time` datetime comment '预约时间',
    `user_address_id` bigint comment  '用户地址编号',
    `address_province_id` bigint comment '地址-省份编号',
    `address_city_id` bigint comment '地址-城市编号',
    `address_district_id` bigint comment '地址-区县编号',
    `address_detail_address` varchar(500) comment '地址-详细地址',
    `address_name` varchar(64) comment '地址-姓名',
    `address_mobile` varchar(15) comment '地址-手机号',
    `pay_time` datetime comment '支付时间',
    `comment` text comment '备注',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time`datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '订单表';