drop table if exists `tb_course_appointment`;
create table `tb_course_appointment` (
    `id` bigint not null auto_increment comment '主键',
    `user_id` bigint comment '用户id',
    `course_id` bigint comment '课程id',
    `mobile` varchar(15) comment '手机号',
    `remark` text comment '备注',
    `contact` varchar(1) comment '是否联系(0:已联系 1:未联系)',
    `comment` text comment '描述',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time`datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '课程预约表';
alter table `tb_course_appointment` add index `idx_course_appointment_01` (`user_id`);
alter table `tb_course_appointment` add index `idx_course_appointment_02` (`course_id`);