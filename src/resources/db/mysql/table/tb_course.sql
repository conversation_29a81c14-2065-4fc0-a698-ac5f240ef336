drop table if exists `tb_course`;
create table `tb_course` (
    `id` bigint not null auto_increment comment '主键',
    `title` varchar(256) comment '课程标题',
    `img_url` varchar(128) comment '课程封面',
    `length` int comment '课程时长',
    `detail_img_url` varchar(256) comment '课程详情图片',
    `original_price` decimal(10,2) comment '原价',
    `price` decimal(10,2) comment '价格',
    `comment` text comment '描述',
    `sales_volume` int comment '虚拟销量',
    `home` varchar(1) comment '是否首页展示(0:展示 1:不展示)',
    `sort` int comment '排序字段',
    `stage` varchar(1) comment '上架状态(0:上架 1:下架)',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time`datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '课程表';