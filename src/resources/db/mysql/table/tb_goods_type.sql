drop table if exists `tb_goods_type`;
create table `tb_goods_type` (
    `id` bigint not null auto_increment comment '主键',
    `catalog` varchar(4) comment '商品类型',
    `name` varchar(64) comment '分类名称',
    `sort` int comment '排序字段',
    `stage` varchar(1) comment '上架状态(0:上架 1:下架)',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time`datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '商品分类表';