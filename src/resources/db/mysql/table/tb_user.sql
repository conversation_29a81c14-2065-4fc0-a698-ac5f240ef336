drop table if exists `tb_user` ;
create table `tb_user` (
	`id` bigint not null auto_increment comment '主键',
	`union_id` varchar(32) comment '用户统一标识(微信开放平台)',
	`open_id` varchar(32) comment '用户统一标识(服务号)',
	`invitation_code` bigint comment '邀请码',
	`qr_code` varchar(32) comment '二维码路径',
	`catalog` varchar(4) comment '用户类型(0:普通用户 1:常驻用户)',
	`name` varchar(10) comment '名字',
	`mobile` varchar(11) comment '手机号',
	`avatar` varchar(64) comment '头像',
	`gender` varchar(1) comment '性别(0:男 1:女)',
	`last_login_time` date comment '最后登录时间',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户表';