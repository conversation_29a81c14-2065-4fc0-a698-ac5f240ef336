drop table if exists `tb_sys_user` ;
create table `tb_sys_user` (
	`id` bigint not null auto_increment comment '主键',
	`name` varchar(10) comment '姓名',
	`username` varchar(30) comment '用户名',
	`mobile` varchar(15) comment '手机号',
	`password` varchar(50) comment '密码',
	`stage` varchar(1) comment '启用/禁用(0: 启用 1:禁用)',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户表';
alter table `tb_sys_user` add index `idx_sys_user_01` (`username`);
alter table `tb_sys_user` add index `idx_sys_user_02` (`mobile`);