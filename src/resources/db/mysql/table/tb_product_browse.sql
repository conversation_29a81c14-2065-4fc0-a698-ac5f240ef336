drop table if exists `tb_product_browse` ;
create table `tb_product_browse` (
	`id` bigint not null auto_increment comment '主键',
    `product_catalog` varchar(4) comment '类型(0:课程 1:商品)',
    `product_id` bigint comment '产品编号',
    `user_id` bigint comment '用户编号',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time`datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '产品浏览表';
alter table `tb_product_browse` add unique index `idx_product_browse_01` (`product_catalog`);
alter table `tb_product_browse` add unique index `idx_product_browse_02` (`product_id`);
alter table `tb_product_browse` add unique index `idx_product_browse_03` (`user_id`);