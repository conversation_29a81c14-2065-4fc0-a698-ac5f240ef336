drop table if exists `tb_goods_specification_type`;
create table `tb_goods_specification_type` (
    `id` bigint not null auto_increment comment '主键',
    `goods_id` bigint comment '商品id',
    `name` varchar(64) comment '规格类型名称',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time`datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '商品规格类型表';