drop table if exists `tb_order_product` ;
create table `tb_order_product` (
	`id` bigint not null auto_increment comment '主键',
    `order_id` bigint comment '订单编号',
    `product_catalog` varchar(4) comment '类型(0:课程 1:商品)',
    `product_id` bigint comment '产品编号',
    `number` int comment '数量',
    `amount` decimal(10,2) comment '实付金额',
    `product_name` varchar(64) comment '产品名称',
    `sku_id` bigint comment 'SKU编号',
    `spec_values` text comment 'SKU属性',
    `img_url` varchar(256) comment '商品封面',
    `buying_price` decimal(10,2) comment '成本价',
    `original_price` decimal(10,2) comment '划线价',
    `price` decimal(10,2) comment '售价',
    `gross_profit` decimal(10,2) comment '毛利(售价-成本价)',
    `commission_ratio` decimal(10,2) comment '团长佣金比例(%)',
    `commission` decimal(10,2) comment '团长佣金',
    `sales_teacher_commission_ratio` decimal(10,2) comment '销售老师佣金比例(%)',
    `sales_teacher_commission` decimal(10,2) comment '销售老师佣金',
    `unit` varchar(16) comment '单位',
    `goods_catalog` varchar(4) comment '商品类型',
    `goods_type_id` bigint comment '商品分类编号',
    `goods_type_name` varchar(64) comment '分类名称',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time`datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '订单产品表';