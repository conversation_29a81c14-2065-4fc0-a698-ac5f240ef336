drop table if exists `tb_goods`;
create table `tb_goods` (
    `id` bigint not null auto_increment comment '主键',
    `name` varchar(64) comment '商品名称',
    `unit` varchar(5) comment '商品单位',
    `img_url` varchar(256) comment '商品封面',
    `catalog` varchar(4) comment '商品类型',
    `goods_type_id` bigint comment '商品分类编号',
    `buying_price` decimal(10,2) comment '成本价',
    `original_price` decimal(10,2) comment '划线价',
    `price` decimal(10,2) comment '售价',
    `gross_profit` decimal(10,2) comment '毛利(售价-成本价)',
    `open_commission` varchar(1) comment '是否分佣(0:是 1:否)',
    `commission_ratio` decimal(10,2) comment '团长佣金比例(%)',
    `sales_teacher_commission_ratio` decimal(10,2) comment '销售老师佣金比例(%)',
    `detail_img_url` varchar(256) comment '商品详情图片',
    `comment` text comment '描述',
    `sales_volume` int comment '虚拟销量',
    `real_sales_volume` int comment '真实销量',
    `home` varchar(1) comment '是否首页展示(0:展示 1:不展示)',
    `stage` varchar(1) comment '上架状态(0:上架 1:下架)',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time`datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '商品表';