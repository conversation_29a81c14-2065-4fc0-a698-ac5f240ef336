drop table if exists `tb_user_commission` ;
create table `tb_user_commission` (
	`id` bigint not null auto_increment comment '主键',
	`user_id` bigint comment '用户编号',
    `amount` decimal(10,2) comment '可用余额',
    `total_amount` decimal(10,2) comment '总余额',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户佣金表';
alter table `tb_user_commission` add unique index `idx_user_commission_01` (`user_id`);