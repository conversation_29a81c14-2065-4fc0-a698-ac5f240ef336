drop table if exists `tb_user_team` ;
create table `tb_user_team` (
	`id` bigint not null auto_increment comment '主键',
	`captain_user_id` bigint comment '队长编号',
	`team_member_user_id` bigint comment '队员编号',
	`binding_catalog` varchar(4) comment '绑定类型',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户团队表';
alter table `tb_user_team` add unique index `idx_user_team_01` (`captain_user_id`,`team_member_user_id`);