drop table if exists `tb_service_info`;
create table `tb_service_info` (
    `id` bigint not null auto_increment comment '主键',
    `name` varchar(128) comment '服务名称',
    `cover_url` varchar(128) comment '服务封面url',
    `detail_img_url` varchar(256) comment '商品详情图片',
    `comment` text comment '备注',
    `sort` int comment '排序字段',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null comment '修改时间',
    `create_time` datetime not null comment '创建时间',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment='服务信息表';
alter table `tb_service_info` add index `idx_service_info_01` (`name`);
alter table `tb_service_info` add index `idx_service_info_02` (`type`);