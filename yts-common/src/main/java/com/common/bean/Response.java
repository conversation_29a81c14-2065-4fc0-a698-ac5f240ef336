package com.common.bean;

import io.swagger.v3.oas.annotations.media.Schema;

public class Response<T> extends <PERSON> implements ResponseHandler {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@Schema(description = "响应编码")
	private String code = OK;

	@Schema(description = "响应消息")
	private String message = SUCCESS;

	@Schema(description = "响应数据")
	private T data;
	
	public Response(){
	}
	public Response(String code, String message){
		this.code = code;
		this.message = message;
	}
	public Response(T data){
		this.data = data;
	}
	public Response(String code, String message, T data){
		this.data = data;
		this.code = code;
		this.message = message;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public T getData() {
		return data;
	}
	public void setData(T data) {
		this.data = data;
	}
}
