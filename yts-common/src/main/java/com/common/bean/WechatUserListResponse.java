package com.common.bean;

import com.fasterxml.jackson.annotation.JsonProperty;

public class WechatUserListResponse extends Bean{

    private static final long serialVersionUID = 2428038068058184466L;
    private Integer total;

    private String count;

    private WechatUserListDataResponse data;

    @JsonProperty("next_openid")
    private String nextOpenid;


    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public WechatUserListDataResponse getData() {
        return data;
    }

    public void setData(WechatUserListDataResponse data) {
        this.data = data;
    }

    public String getNextOpenid() {
        return nextOpenid;
    }

    public void setNextOpenid(String nextOpenid) {
        this.nextOpenid = nextOpenid;
    }
}
