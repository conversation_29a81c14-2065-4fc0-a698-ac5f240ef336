package com.common.bean;

public class UserToken extends Bean {

	private static final long serialVersionUID = 1L;
	/**
	 * tokenID
	 */
	private Long id;
	/**
	 * 系统用户名称
	 */
	private String username;
	/**
	 * 用户名称
	 */
	private String name;

	/**
	 * 用户身份
	 */
	private String catalog;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCatalog() {
		return catalog;
	}

	public void setCatalog(String catalog) {
		this.catalog = catalog;
	}
}
