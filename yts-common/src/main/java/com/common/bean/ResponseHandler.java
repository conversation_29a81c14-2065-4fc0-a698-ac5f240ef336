package com.common.bean;

public interface ResponseHandler {
	public final String OK = "0";
	public final String SUCCESS = "操作成功";
	public final String ERROR = "1";
	public final String FAILURE = "操作失败";
	public final String FORBIDDEN = "1000";
	public final String FORBIDDEN_MESSAGE = "无权限";
	public final String TOKEN_TIMEOUT = "1001";
	public final String TOKEN_TIMEOUT_MESSAGE = "用户令牌过期";
	public final String TOKEN_INVALID_MESSAGE = "用户令牌无效";
	public final String LOGOUT = "1002";
	public final String LOGOUT_MESSAGE = "请重新登录";
	public final String FLUSH = "1003";
	public final String FLUSH_MESSAGE = "请按 Ctrl + F5 刷新页面";
}
