package com.common.bean;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

public class WechatUserPhoneNumberResponse implements Serializable {

    private static final long serialVersionUID = 7856808800231019463L;

    /**
     * 用户手机号信息
     */
    @JsonProperty("phone_info")
    private WechatPhoneInfoResponse phoneInfo;

    /**
     * 错误码
     */
    private Integer errcode;

    /**
     * 错误信息
     */
    private String errmsg;

    public WechatPhoneInfoResponse getPhoneInfo() {
        return phoneInfo;
    }

    public void setPhoneInfo(WechatPhoneInfoResponse phoneInfo) {
        this.phoneInfo = phoneInfo;
    }

    public Integer getErrcode() {
        return errcode;
    }

    public void setErrcode(Integer errcode) {
        this.errcode = errcode;
    }

    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }
}
