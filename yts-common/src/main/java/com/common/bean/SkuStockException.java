package com.common.bean;

public class SkuStockException extends RuntimeException {
    private Long goodsId;
    private String specValues;
    private Integer stock;
    private String productName;
    public SkuStockException(Long goodsId, String specValues, Integer stock, String productName) {
        this.goodsId = goodsId;
        this.specValues = specValues;
        this.stock = stock;
        this.productName = productName;
    }
    public SkuStockException(String message) {
        super(message);
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getSpecValues() {
        return specValues;
    }

    public void setSpecValues(String specValues) {
        this.specValues = specValues;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
}
