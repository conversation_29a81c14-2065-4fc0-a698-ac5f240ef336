package com.common.bean;

import java.util.Date;

public class JsonWebToken {
	private String id;
	/**
	 * 签发时间
	 */
	private Date issuedAt;
	/**
	 * 主题
	 */
	private String subject;
	/**
	 * 签发人
	 */
	private String issuer;
	/**
	 * 过期时间
	 */
	private Date expirationTime;
	/**
	 * 用户编号
	 */
	private Long userId;

	/**
	 * 用户名
	 */
	private String username;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 类型
	 */
	private String type;

	/**
	 * 用户身份
	 */
	private String userCatalog;
	/**
	 * 过期时间
	 */
	private Date expiration;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Date getIssuedAt() {
		return issuedAt;
	}

	public void setIssuedAt(Date issuedAt) {
		this.issuedAt = issuedAt;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getIssuer() {
		return issuer;
	}

	public void setIssuer(String issuer) {
		this.issuer = issuer;
	}

	public Date getExpirationTime() {
		return expirationTime;
	}

	public void setExpirationTime(Date expirationTime) {
		this.expirationTime = expirationTime;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Date getExpiration() {
		return expiration;
	}

	public void setExpiration(Date expiration) {
		this.expiration = expiration;
	}

	public String getUserCatalog() {
		return userCatalog;
	}

	public void setUserCatalog(String userCatalog) {
		this.userCatalog = userCatalog;
	}
}
