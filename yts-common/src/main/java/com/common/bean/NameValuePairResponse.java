package com.common.bean;

import io.swagger.v3.oas.annotations.media.Schema;

public class NameValuePairResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "code")
	private String code;

	@Schema(description = "名称")
	private String name;

	public NameValuePairResponse(){
		
	}

	public NameValuePairResponse(String code, String name){
		this.code = code;
		this.name = name;
	}

	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
}
