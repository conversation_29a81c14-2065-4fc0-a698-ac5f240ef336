package com.common.bean;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户表请求对象
 *
 * @date 2025-07-24 23:22:31
 */
public class UserExport extends ExportMessage {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "用户统一标识(微信开放平台)")
	private String unionId;

	@Schema(description = "用户统一标识(微信开放平台)集合")
	private List<String> unionIds;

	@Schema(description = "用户统一标识(服务号)")
	private String openId;

	@Schema(description = "邀请码")
	private Long invitationCode;

	@Schema(description = "二维码")
	private String qrCode;

	@Schema(description = "用户类型(0:普通用户 1:常驻用户)")
	private String catalog;

	@Schema(description = "名字")
	private String name;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "头像")
	private String avatar;

	@Schema(description = "性别(0:男 1:女)")
	private String gender;

	@Schema(description = "最后登录时间")
	private String lastLoginTime;

	@Schema(description = "最小最后登录时间")
	private String minLastLoginTime;

	@Schema(description = "最大最后登录时间")
	private String maxLastLoginTime;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;

	@Schema(description = "小程序授权code")
	private String code;

	@Schema(description = "队长编号")
	private Long captainUserId;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public String getUnionId (){
		return unionId;
	}

	public void setUnionId (String unionId) {
		this.unionId = unionId;
	}

	public List<String> getUnionIds (){
		return unionIds;
	}

	public void setUnionIds (List<String> unionIds) {
		this.unionIds = unionIds;
	}

	public Long getInvitationCode (){
		return invitationCode;
	}

	public void setInvitationCode (Long invitationCode) {
		this.invitationCode = invitationCode;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getAvatar (){
		return avatar;
	}

	public void setAvatar (String avatar) {
		this.avatar = avatar;
	}

	public String getGender (){
		return gender;
	}

	public void setGender (String gender) {
		this.gender = gender;
	}

	public String getLastLoginTime (){
		return lastLoginTime;
	}

	public void setLastLoginTime (String lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	public String getMinLastLoginTime (){
		return minLastLoginTime;
	}

	public void setMinLastLoginTime (String minLastLoginTime) {
		this.minLastLoginTime = minLastLoginTime;
	}

	public String getMaxLastLoginTime (){
		return maxLastLoginTime;
	}

	public void setMaxLastLoginTime (String maxLastLoginTime) {
		this.maxLastLoginTime = maxLastLoginTime;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Long getCaptainUserId() {
		return captainUserId;
	}

	public void setCaptainUserId(Long captainUserId) {
		this.captainUserId = captainUserId;
	}

	public String getQrCode() {
		return qrCode;
	}

	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}
}
