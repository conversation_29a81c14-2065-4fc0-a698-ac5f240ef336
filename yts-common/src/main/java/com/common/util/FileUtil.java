package com.common.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.Scanner;
import java.util.Set;

public final class FileUtil {
    private FileUtil(){} 
    public static void main(String[] args) throws IOException{
    		Set<String> uriSet = new HashSet<String>();
    		System.out.println(list("/Users/<USER>/develop/test",true,uriSet));
    }
    /**
     * 新建目录
     * @param dir
     */
	public static void make(String dir){
		File file = new File(dir);
		if(!file.exists()){
			file.mkdirs();
		}
	}
	/**
	 * 读取目录下文件
	 * @param dir 目录
	 * @param isRecursive 是否递归读取
	 * @param uriSet 返回文件路径集合
	 */
	public static Set<String> list(String dir,boolean isRecursive,Set<String> uriSet) {
		File file = new File(dir);
		if(file.exists()){
			if(!file.isDirectory()){
				uriSet.add(file.getPath());
			}else if(file.isDirectory()) {
				File[] fileList = file.listFiles();
				if(fileList != null){
					for(int i = 0; i < fileList.length; i++){
						file = fileList[i];
						if(!file.isDirectory()){
							uriSet.add(file.getPath());
						}else if(file.isDirectory()){
							if(isRecursive){
								list(fileList[i].getPath(),isRecursive,uriSet);
							}
						}
					}
				}
			}
		}
		return uriSet;
	}
	/**
	 * 目录或文件是否存在
	 * @param path
	 * @return
	 */
	public static boolean exists(String path){
		File file = new File(path);
		return file.exists();
	}
	/**
	 * 拷贝文件
	 * @param src 源文件
	 * @param dest 目标文件
	 */
	public static void copy(String src,String dest){
		FileChannel in = null;
		FileChannel out = null;
		FileInputStream inStream = null;
		FileOutputStream outStream = null;
		try{
			inStream = new FileInputStream(src);
			outStream = new FileOutputStream(dest);
			in = inStream.getChannel();
			out = outStream.getChannel();
			in.transferTo(0, in.size(), out);
		}catch(Exception e) {
			throw new RuntimeException(e);
		}finally{
			if(inStream != null){
				try{
					inStream.close();
				}catch(Exception e){
					throw new RuntimeException(e);
				}
			}
			if(in != null){
				try{
					in.close();
				}catch(Exception e){
					throw new RuntimeException(e);
				}
			}
			if(outStream != null){
				try{
					outStream.close();
				}catch(Exception e){
					throw new RuntimeException(e);
				}
			}
			if(out != null){
				try{
					out.close();
				}catch(Exception e){
					throw new RuntimeException(e);
				}
			}
		}
	}
	/**
	 * 读取文件内容
	 * @param file
	 * @return
	 */
	public static String read(File file){
		Scanner scanner = null;
		StringBuffer buffer = new StringBuffer();
		try {
			scanner = new Scanner(file,StandardCharsets.UTF_8.name());
			while (scanner.hasNextLine()) {
				buffer.append(scanner.nextLine());
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		} finally {
			if (scanner != null) {
				scanner.close();
			}
		}
		return buffer.toString();
	}
}