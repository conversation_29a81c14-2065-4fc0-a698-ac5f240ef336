package com.common.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegxUtil {

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		
	}
	public static String getEmail(String str){
		String regx = "[\\w[.-]]+@[\\w[.-]]+\\.[\\w]+";
		return getText(str, regx);
	}
	public static String getCas(String str){
		String regx = "\\d{2,10}-\\d{2}-\\d";
		return getText(str, regx);
	}
	public static List<String> getCasList(String str){
		String regx = "\\d{2,10}-\\d{2}-\\d";
		return getTextList(str, regx);
	}
	public static String getQQ(String str){
		String regx = "[1-9][0-9]{4,}";
		return getText(str, regx);
	}
	public static String getText(String str,String regx){
		Pattern p = Pattern.compile(regx);
		Matcher m = p.matcher(str);
		while(m.find()){
			return m.group();
		}
		return null;
	}
	public static List<String> getTextList(String str,String regx){
		Pattern p = Pattern.compile(regx);
		Matcher m = p.matcher(str);
		List<String> list = new ArrayList<String>();
		while(m.find()){
			list.add(m.group());
		}
		return list;
	}
}
