package com.common.constant;

public enum UserCatalog {
	C0("0","普通用户"),
	C1("1","常驻用户");
	private String code;
	private String name;
	private UserCatalog(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (UserCatalog value : UserCatalog.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (UserCatalog value : UserCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}

	public static UserCatalog get(String code){
		for (UserCatalog value : UserCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
		return null;
	}
}
