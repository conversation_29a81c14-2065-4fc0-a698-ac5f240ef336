package com.common.constant;

public enum WxTradeState {
	SUCCESS("SUCCESS","支付成功"),
	REFUND("REFUND","转入退款"),
	NOTPAY("NOTPAY","未支付"),
	CLOSED("CLOSED","已关闭"),
	REVOKED("REVOKED","已撤销（仅付款码支付会返回）"),
	USERPAYING("USERPAYING","用户支付中（仅付款码支付会返回）"),
	PAYERROR("PAYERROR","支付失败（仅付款码支付会返回）"),
	;
	private String code;
	private String name;
	private WxTradeState(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (WxTradeState value : WxTradeState.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (WxTradeState value : WxTradeState.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
