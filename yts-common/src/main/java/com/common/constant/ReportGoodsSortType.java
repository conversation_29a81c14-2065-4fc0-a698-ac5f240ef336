package com.common.constant;

public enum ReportGoodsSortType {

    C0("0","浏览数量最小","browse_num asc"),
    C1("1","浏览数量最大","browse_num desc"),
    C2("2","支付数量最低","pay_num asc"),
    C3("3","支付数量最高","pay_num desc"),
    C4("4","支付金额最低","pay_amount asc"),
    C5("5","支付金额最高","pay_amount desc"),
    C6("6","退款数量最低","refund_num asc"),
    C7("7","退款数量最高","refund_num desc"),
    C8("8","退款金额最低","refund_amount asc"),
    C9("9","退款金额最高","refund_amount desc"),
    C10("10","创建最晚","id desc"),
    ;

    private String code;
    private String name;
    private String sql;

    private ReportGoodsSortType(String code, String name, String sql){
        this.code = code;
        this.name = name;
        this.sql = sql;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public static String getName(String code){
        for (ReportGoodsSortType gender : ReportGoodsSortType.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
        return null;
    }
    public static String getSql(String code){
        for (ReportGoodsSortType gender : ReportGoodsSortType.values()) {
            if (gender.getCode().equals(code)) {
                return gender.sql;
            }
        }
        return null;
    }
    public static ReportGoodsSortType get(String code){
        for (ReportGoodsSortType value : ReportGoodsSortType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
