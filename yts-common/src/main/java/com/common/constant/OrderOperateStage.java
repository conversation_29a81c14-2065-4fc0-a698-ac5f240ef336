package com.common.constant;

public enum OrderOperateStage {
    C0("0","已提货"),
    C1("1","未提货"),
    ;

    private String code;
    private String name;

    private OrderOperateStage(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getName(String code){
        for (OrderOperateStage gender : OrderOperateStage.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
        return null;
    }
    public static OrderOperateStage get(String code){
        for (OrderOperateStage value : OrderOperateStage.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
