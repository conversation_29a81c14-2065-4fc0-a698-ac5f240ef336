package com.common.constant;

public enum DataStage {
	Y("0","启用"),
	N("1","禁用");
	private String code;
	private String name;
	private DataStage(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String
	getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getName(String code){
		for (DataStage value : DataStage.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
