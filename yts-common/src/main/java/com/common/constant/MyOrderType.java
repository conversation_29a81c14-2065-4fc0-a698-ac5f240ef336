package com.common.constant;

/**
 * 我的订单查询类型枚举
 */
public enum MyOrderType {
    ALL("0","全部"),
    WAIT_PAY("1","待付款"),
    WAIT_PICKUP("2","待收货"),
    REFUND("3","售后中");

    private String code;
    private String name;

    private MyOrderType(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getName(String code){
        for (MyOrderType value : MyOrderType.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
        return null;
    }

    public static MyOrderType get(String code){
        for (MyOrderType value : MyOrderType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
