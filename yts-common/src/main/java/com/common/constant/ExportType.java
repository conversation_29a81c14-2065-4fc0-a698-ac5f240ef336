package com.common.constant;

public enum ExportType {
	C0("0","用户分佣导出"),
	;
	private String code;
	private String name;
	private ExportType(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getName(String code){
		for (ExportType value : ExportType.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}

	public static ExportType get(String code){
		for (ExportType value : ExportType.values()) {
			if (value.getCode().equals(code)) {
				return value;
			}
		}
		return null;
	}
}
