package com.common.constant;

public enum UserCommissionFlowCatalog {
	C1000("1000","分佣流水"),
	C1001("1001","提现流水"),
	C1002("1002","退款流水"),
	;
	private String code;
	private String name;
	private UserCommissionFlowCatalog(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (UserCommissionFlowCatalog value : UserCommissionFlowCatalog.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (UserCommissionFlowCatalog value : UserCommissionFlowCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}

	public static UserCommissionFlowCatalog get(String code){
		for (UserCommissionFlowCatalog value : UserCommissionFlowCatalog.values()) {
			if (value.getCode().equals(code)) {
				return value;
			}
		}
		return null;
	}
}
