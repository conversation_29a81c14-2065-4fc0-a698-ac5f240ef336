package com.common.constant;

public enum UserTeamBindingCatalog {
	C1000("1000","扫码绑定"),
	C1001("1001","邀请码绑定"),
	;
	private String code;
	private String name;
	private UserTeamBindingCatalog(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getName(String code){
		for (UserTeamBindingCatalog value : UserTeamBindingCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}

	public static UserTeamBindingCatalog get(String code){
		for (UserTeamBindingCatalog value : UserTeamBindingCatalog.values()) {
			if (value.getCode().equals(code)) {
				return value;
			}
		}
		return null;
	}
}
