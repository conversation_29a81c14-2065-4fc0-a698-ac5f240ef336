package com.common.constant;

/**
 * 公共开关枚举
 */
public enum PublicSwitch {
	Y("0","开启"),
	N("1","关闭");
	private String code;
	private String name;
	private PublicSwitch(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (PublicSwitch value : PublicSwitch.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (PublicSwitch value : PublicSwitch.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
