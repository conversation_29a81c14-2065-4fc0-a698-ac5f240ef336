package com.common.constant;

public enum GoodsCatalog {

    C0("0","平台优选"),
    C1("1","每日生鲜");

    private String code;
    private String name;

    private GoodsCatalog(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getName(String code){
        for (GoodsCatalog gender : GoodsCatalog.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
        return null;
    }
    public static GoodsCatalog get(String code){
        for (GoodsCatalog value : GoodsCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
