package com.common.constant;

public enum WxNotifyType {

	/**
	 * 支付通知
	 */
	NATIVE_NOTIFY("/v1/wechat/pay/jsapi/notify"),

	/**
	 * 退款结果通知
	 */
	REFUND_NOTIFY("/v1/wechat/pay/refunds/notify");

	/**
	 * 类型
	 */
	private String type;

	WxNotifyType() {
	}

	WxNotifyType(String type) {
		this.type = type;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}
