package com.common.constant;

public enum DataStatus {
	Y("0","正常"),
	N("1","无效");
	private String code;
	private String name;
	private DataStatus(String code,String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getName(String code){
		for (DataStatus value : DataStatus.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
