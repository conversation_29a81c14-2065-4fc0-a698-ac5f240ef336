package com.common.constant;

public enum OrderSelectCatalog {
	C0("0","老师帮选"),
	C1("1","会员自选");
	private String code;
	private String name;
	private OrderSelectCatalog(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (OrderSelectCatalog value : OrderSelectCatalog.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (OrderSelectCatalog value : OrderSelectCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
