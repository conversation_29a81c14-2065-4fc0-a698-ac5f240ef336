package com.common.constant;

/**
 * 用于页面暂存，自定义表头，自定义查询条件
 * <AUTHOR>
 * @date 2023/1/7 15:27
 */
public enum TemporaryStorageCatalog {
    C10("C10","页面暂存"),
    C11("C11","自定义表头"),
    C12("C12","自定义查询条件");

    private String code;
    private String name;

    private TemporaryStorageCatalog(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getName(String code){
        for (TemporaryStorageCatalog gender : TemporaryStorageCatalog.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
        return null;
    }
    public static TemporaryStorageCatalog get(String code){
        for (TemporaryStorageCatalog value : TemporaryStorageCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
