package com.common.constant;

public enum OrderStage {
    NOTPAY("0","未支付"),
    SUCCESS("1","支付成功"),
    CLOSED("2","超时已关闭"),
    CANCEL("3","已取消"),
    AUTO_REFUND_PROCESSING("4","自动退款中"),
    AUTO_REFUND_SUCCESS("5","已自动退款"),
    AUTO_REFUND_ABNORMAL("6","自动退款异常"),
    REFUND_PROCESSING("7","手动退款中"),
    REFUND_SUCCESS("8","已手动退款"),
    REFUND_ABNORMAL("9","手动退款异常"),
    PENDING_REFUND("10","待退款"),
    ;

    private String code;
    private String name;

    private OrderStage(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getName(String code){
        for (OrderStage gender : OrderStage.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
        return null;
    }
    public static OrderStage get(String code){
        for (OrderStage value : OrderStage.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
