package com.common.constant;

public enum UserCommissionFlowPayChannel {
	C1000("1000","微信小程序支付"),
	;
	private String code;
	private String name;
	private UserCommissionFlowPayChannel(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (UserCommissionFlowPayChannel value : UserCommissionFlowPayChannel.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (UserCommissionFlowPayChannel value : UserCommissionFlowPayChannel.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
