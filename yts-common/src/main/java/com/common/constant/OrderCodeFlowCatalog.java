package com.common.constant;

public enum OrderCodeFlowCatalog {

    C1000("1000","活动缴费"),
    C1001("1001","充值缴费");

    private String code;
    private String name;

    private OrderCodeFlowCatalog(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getName(String code){
        for (OrderCodeFlowCatalog gender : OrderCodeFlowCatalog.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
        return null;
    }
    public static OrderCodeFlowCatalog get(String code){
        for (OrderCodeFlowCatalog value : OrderCodeFlowCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
