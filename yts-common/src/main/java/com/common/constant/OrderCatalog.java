package com.common.constant;

public enum OrderCatalog {
	C0("0","课程"),
	C1("1","商品");
	private String code;
	private String name;
	private OrderCatalog(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (OrderCatalog value : OrderCatalog.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (OrderCatalog value : OrderCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
