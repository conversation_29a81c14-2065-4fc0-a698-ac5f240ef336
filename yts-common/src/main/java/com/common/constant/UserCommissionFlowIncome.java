package com.common.constant;

public enum UserCommissionFlowIncome {
	C0("0","收入"),
	C1("1","支出");
	private String code;
	private String name;
	private UserCommissionFlowIncome(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (UserCommissionFlowIncome value : UserCommissionFlowIncome.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (UserCommissionFlowIncome value : UserCommissionFlowIncome.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
