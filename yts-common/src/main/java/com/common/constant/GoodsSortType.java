package com.common.constant;

public enum GoodsSortType {

    C0("0","销量最小","real_sales_volume asc"),
    C1("1","销量最大","real_sales_volume desc"),
    C2("2","价格最低","price asc"),
    C3("3","价格最高","price desc"),
    C4("4","创建最晚","id desc"),
    ;

    private String code;
    private String name;
    private String sql;

    private GoodsSortType(String code, String name, String sql){
        this.code = code;
        this.name = name;
        this.sql = sql;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public static String getName(String code){
        for (GoodsSortType gender : GoodsSortType.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
        return null;
    }
    public static String getSql(String code){
        for (GoodsSortType gender : GoodsSortType.values()) {
            if (gender.getCode().equals(code)) {
                return gender.sql;
            }
        }
        return null;
    }
    public static GoodsSortType get(String code){
        for (GoodsSortType value : GoodsSortType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
