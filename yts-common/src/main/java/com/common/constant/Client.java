package com.common.constant;

public enum Client {
	Y("1000","PC"),
	N("1001","移动");
	private String code;
	private String name;
	private Client(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getName(String code){
		for (Client value : Client.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
