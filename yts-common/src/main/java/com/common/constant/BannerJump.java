package com.common.constant;

/**
 * 轮播图是否跳转
 */
public enum BannerJump {
	C0("0","是"),
	C1("1","否");
	private String code;
	private String name;
	private BannerJump(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (BannerJump value : BannerJump.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (BannerJump value : BannerJump.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
