package com.common.constant;

/**
 * 轮播图跳转类型
 */
public enum BannerJumpCatalog {
	C0("0","商品"),
	C1("1","课程");
	private String code;
	private String name;
	private BannerJumpCatalog(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (BannerJumpCatalog value : BannerJumpCatalog.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (BannerJumpCatalog value : BannerJumpCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
