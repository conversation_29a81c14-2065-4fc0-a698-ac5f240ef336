package com.common.constant;

/**
 * 公共联系枚举
 */
public enum PublicContact {
	Y("0","已联系"),
	N("1","未联系");
	private String code;
	private String name;
	private PublicContact(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (PublicContact value : PublicContact.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (PublicContact value : PublicContact.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
