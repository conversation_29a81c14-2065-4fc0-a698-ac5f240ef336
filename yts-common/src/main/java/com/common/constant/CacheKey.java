package com.common.constant;

public class CacheKey {

	/**
	 * 用户Token
	 */
	public static final String USER_TOKEN = "CK1000";


	/**
	 * 系统用户登录限制
	 */
	public static final String SYS_USER_LOGIN_LIMIT = "CK1001";


	/**
	 * 文件导出
	 */
	public static final String FILE_EXPORT = "CK1002";

	/**
	 * 文件的下载地址
	 */
	public static final String FILE_DOWNLOAD_URL = "CK1003";

	/**
	 * 日记记录
	 */
	public static final String LOG = "CK1004";

	/**
	 * 活动订单
	 */
	public static final String LOCK_ORDER = "CK1005";

	/**
	 * 订单过期队列
	 */
	public static final String ORDER_EXPIRE_QUEUE = "CK1006";

	/**
	 * 用户登录
	 */
	public static final String LOCK_USER_LOGIN = "CK1007";

	/**
	 * 邀请码
	 */
	public static final String INVITATION_CODE = "CK1008";

	/**
	 * 系统页面数据暂存
	 */
	public static final String MENU_DATA_TEMPORARY_STORAGE  = "CK1009";

	/**
	 * 用户分佣
	 */
	public static final String USER_COMMISSION = "CK1010";

	/**
	 * 用户分佣锁
	 */
	public static final String LOCK_USER_COMMISSION = "CK1011";
}

