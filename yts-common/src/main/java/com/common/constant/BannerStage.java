package com.common.constant;

public enum BannerStage {
	Y("0","上架"),
	N("1","下架");
	private String code;
	private String name;
	private BannerStage(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getName(String code){
		for (BannerStage value : BannerStage.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
