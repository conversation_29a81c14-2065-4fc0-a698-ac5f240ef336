package com.api.validator;

import java.util.List;


/**
 * 轮播图表数据验证
 *
 * @date 2025-07-19 10:58:28
 */
public class BannerValidator extends Validator {

	public BannerValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onImgUrl(String imgUrl) {
		if (isEmpty(imgUrl)) {
			this.addAttribute(errors,"图片地址不能为空");
			this.result = false;
		} else if (imgUrl.trim().length() > 128L) {
			this.addAttribute(errors,"图片地址长度不能超过128");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onJump(String jump) {
		if (isEmpty(jump)) {
			this.addAttribute(errors,"是否跳转(0:跳转 1:不跳转)不能为空");
			this.result = false;
		} else if (jump.trim().length() > 1L) {
			this.addAttribute(errors,"是否跳转(0:跳转 1:不跳转)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onJumpCatalog(String jumpCatalog) {
		if (isEmpty(jumpCatalog)) {
			this.addAttribute(errors,"跳转类型不能为空");
			this.result = false;
		} else if (jumpCatalog.trim().length() > 4L) {
			this.addAttribute(errors,"跳转类型长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onJumpId(Long jumpId) {
		if (isEmpty(jumpId)) {
			this.addAttribute(errors,"跳转编号不能为空");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onJumpIds(List<Long> jumpIds) {
		if (isEmpty(jumpIds)) {
			this.addAttribute(errors,"跳转编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onStage(String stage) {
		if (isEmpty(stage)) {
			this.addAttribute(errors,"上架状态(0:上架 1:下架)不能为空");
			this.result = false;
		} else if (stage.trim().length() > 1L) {
			this.addAttribute(errors,"上架状态(0:上架 1:下架)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onSort(Integer sort) {
		if (isEmpty(sort)) {
			this.addAttribute(errors,"排序字段不能为空");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public BannerValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
