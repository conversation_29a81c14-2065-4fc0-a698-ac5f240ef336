package com.api.validator;

import java.util.List;


/**
 * 商品规格类型表数据验证
 *
 * @date 2025-07-27 21:26:17
 */
public class GoodsSpecificationTypeValidator extends Validator {

	public GoodsSpecificationTypeValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationTypeValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationTypeValidator onGoodsId(Long goodsId) {
		if (isEmpty(goodsId)) {
			this.addAttribute(errors,"商品id不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationTypeValidator onGoodsIds(List<Long> goodsIds) {
		if (isEmpty(goodsIds)) {
			this.addAttribute(errors,"商品id集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationTypeValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"规格类型名称不能为空");
			this.result = false;
		} else if (name.trim().length() > 64L) {
			this.addAttribute(errors,"规格类型名称长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationTypeValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationTypeValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationTypeValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationTypeValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationTypeValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationTypeValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationTypeValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
