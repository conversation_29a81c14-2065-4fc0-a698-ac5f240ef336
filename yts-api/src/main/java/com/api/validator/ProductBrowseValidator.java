package com.api.validator;

import java.util.List;


/**
 * 产品浏览表数据验证
 *
 * @date 2025-07-20 16:36:02
 */
public class ProductBrowseValidator extends Validator {

	public ProductBrowseValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onProductCatalog(String productCatalog) {
		if (isEmpty(productCatalog)) {
			this.addAttribute(errors,"类型(0:课程 1:商品)不能为空");
			this.result = false;
		} else if (productCatalog.trim().length() > 4L) {
			this.addAttribute(errors,"类型(0:课程 1:商品)长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onProductId(Long productId) {
		if (isEmpty(productId)) {
			this.addAttribute(errors,"产品编号不能为空");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onProductIds(List<Long> productIds) {
		if (isEmpty(productIds)) {
			this.addAttribute(errors,"产品编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onUserId(Long userId) {
		if (isEmpty(userId)) {
			this.addAttribute(errors,"用户编号不能为空");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onUserIds(List<Long> userIds) {
		if (isEmpty(userIds)) {
			this.addAttribute(errors,"用户编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ProductBrowseValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
