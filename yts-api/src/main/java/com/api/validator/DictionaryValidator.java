package com.api.validator;


public class DictionaryValidator extends Validator {

	//验证分类id
	public DictionaryValidator onTemporaryStorageCatalog(String str) {
		if (this.isEmpty(str)) {
			this.addAttribute(errors, "分类ID为空");
			this.result = false;
		}
		return this;
	}
	//验证菜单name
	public DictionaryValidator onMenuName(String str) {
		if (this.isEmpty(str)) {
			this.addAttribute(errors, "菜单id为空");
			this.result = false;
		}
		return this;
	}

	//验证数据
	public DictionaryValidator onValue(String str) {
		if (this.isEmpty(str)) {
			this.addAttribute(errors, "请输入要保存的数据");
			this.result = false;
		}
		return this;
	}


}
