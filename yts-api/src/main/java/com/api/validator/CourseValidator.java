package com.api.validator;

import java.math.BigDecimal;
import java.util.List;


/**
 * 课程表数据验证
 *
 * @date 2025-07-19 10:58:28
 */
public class CourseValidator extends Validator {

	public CourseValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onTitle(String title) {
		if (isEmpty(title)) {
			this.addAttribute(errors,"课程标题不能为空");
			this.result = false;
		} else if (title.trim().length() > 256L) {
			this.addAttribute(errors,"课程标题长度不能超过256");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onImgUrl(String imgUrl) {
		if (isEmpty(imgUrl)) {
			this.addAttribute(errors,"课程封面不能为空");
			this.result = false;
		} else if (imgUrl.trim().length() > 128L) {
			this.addAttribute(errors,"课程封面长度不能超过128");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onLength(Integer length) {
		if (isEmpty(length)) {
			this.addAttribute(errors,"课程时长不能为空");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onDetailImgUrl(String detailImgUrl) {
		if (isEmpty(detailImgUrl)) {
			this.addAttribute(errors,"课程详情图片不能为空");
			this.result = false;
		} else if (detailImgUrl.trim().length() > 256L) {
			this.addAttribute(errors,"课程详情图片长度不能超过256");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onOriginalPrice(BigDecimal originalPrice) {
		if (isEmpty(originalPrice)) {
			this.addAttribute(errors,"原价不能为空");
			this.result = false;
		} else if (originalPrice.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"原价不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onPrice(BigDecimal price) {
		if (isEmpty(price)) {
			this.addAttribute(errors,"价格不能为空");
			this.result = false;
		} else if (price.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"价格不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onComment(String comment) {
		if (isEmpty(comment)) {
			this.addAttribute(errors,"描述不能为空");
			this.result = false;
		} else if (comment.trim().length() > 65535L) {
			this.addAttribute(errors,"描述长度不能超过65535");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onSalesVolume(String salesVolume) {
		if (isEmpty(salesVolume)) {
			this.addAttribute(errors,"虚拟销量不能为空");
			this.result = false;
		} else if (salesVolume.trim().length() > 65535L) {
			this.addAttribute(errors,"虚拟销量长度不能超过65535");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onHome(String home) {
		if (isEmpty(home)) {
			this.addAttribute(errors,"是否首页展示(0:展示 1:不展示)不能为空");
			this.result = false;
		} else if (home.trim().length() > 1L) {
			this.addAttribute(errors,"是否首页展示(0:展示 1:不展示)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onSort(Integer sort) {
		if (isEmpty(sort)) {
			this.addAttribute(errors,"排序字段不能为空");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onStage(String stage) {
		if (isEmpty(stage)) {
			this.addAttribute(errors,"上架状态(0:上架 1:下架)不能为空");
			this.result = false;
		} else if (stage.trim().length() > 1L) {
			this.addAttribute(errors,"上架状态(0:上架 1:下架)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public CourseValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
