package com.api.validator;

import java.util.List;


/**
 * 文件表数据验证
 *
 * @date 2025-07-16 21:03:32
 */
public class FileValidator extends Validator {

	public FileValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public FileValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public FileValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"名称不能为空");
			this.result = false;
		} else if (name.trim().length() > 100L) {
			this.addAttribute(errors,"名称长度不能超过100");
			this.result = false;
		}
		return this;
	}

	public FileValidator onType(String type) {
		if (isEmpty(type)) {
			this.addAttribute(errors,"类型不能为空");
			this.result = false;
		} else if (type.trim().length() > 100L) {
			this.addAttribute(errors,"类型长度不能超过100");
			this.result = false;
		}
		return this;
	}

	public FileValidator onSize(Integer size) {
		if (isEmpty(size)) {
			this.addAttribute(errors,"大小不能为空");
			this.result = false;
		}
		return this;
	}

	public FileValidator onUrl(String url) {
		if (isEmpty(url)) {
			this.addAttribute(errors,"位置不能为空");
			this.result = false;
		} else if (url.trim().length() > 64L) {
			this.addAttribute(errors,"位置长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public FileValidator onOssUrl(String ossUrl) {
		if (isEmpty(ossUrl)) {
			this.addAttribute(errors,"oss位置不能为空");
			this.result = false;
		} else if (ossUrl.trim().length() > 300L) {
			this.addAttribute(errors,"oss位置长度不能超过300");
			this.result = false;
		}
		return this;
	}

	public FileValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public FileValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public FileValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public FileValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public FileValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public FileValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public FileValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
