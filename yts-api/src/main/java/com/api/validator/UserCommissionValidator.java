package com.api.validator;

import java.math.BigDecimal;
import java.util.List;


/**
 * 用户佣金表数据验证
 *
 * @date 2025-07-27 11:32:54
 */
public class UserCommissionValidator extends Validator {

	public UserCommissionValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onUserId(Long userId) {
		if (isEmpty(userId)) {
			this.addAttribute(errors,"用户编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onUserIds(List<Long> userIds) {
		if (isEmpty(userIds)) {
			this.addAttribute(errors,"用户编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onAmount(BigDecimal amount) {
		if (isEmpty(amount)) {
			this.addAttribute(errors,"可用余额不能为空");
			this.result = false;
		} else if (amount.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"可用余额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onTotalAmount(BigDecimal totalAmount) {
		if (isEmpty(totalAmount)) {
			this.addAttribute(errors,"总余额不能为空");
			this.result = false;
		} else if (totalAmount.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"总余额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
