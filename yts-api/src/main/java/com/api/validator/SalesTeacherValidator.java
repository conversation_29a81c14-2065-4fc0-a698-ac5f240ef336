package com.api.validator;

import java.util.List;


/**
 * 销售老师表数据验证
 *
 * @date 2025-07-24 23:22:31
 */
public class SalesTeacherValidator extends Validator {

	public SalesTeacherValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"姓名不能为空");
			this.result = false;
		} else if (name.trim().length() > 20L) {
			this.addAttribute(errors,"姓名长度不能超过20");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onGender(String gender) {
		if (isEmpty(gender)) {
			this.addAttribute(errors,"性别(0:男 1:女)不能为空");
			this.result = false;
		} else if (gender.trim().length() > 1L) {
			this.addAttribute(errors,"性别(0:男 1:女)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onMobile(String mobile) {
		if (isEmpty(mobile)) {
			this.addAttribute(errors,"手机号不能为空");
			this.result = false;
		} else if (mobile.trim().length() > 11L) {
			this.addAttribute(errors,"手机号长度不能超过11");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onStage(String stage) {
		if (isEmpty(stage)) {
			this.addAttribute(errors,"启用/禁用状态不能为空");
			this.result = false;
		} else if (stage.trim().length() > 1L) {
			this.addAttribute(errors,"启用/禁用状态长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public SalesTeacherValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
