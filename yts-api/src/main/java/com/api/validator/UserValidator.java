package com.api.validator;

import java.util.List;


/**
 * 用户表数据验证
 *
 * @date 2025-07-24 23:22:31
 */
public class UserValidator extends Validator {

	public UserValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onUnionId(String unionId) {
		if (isEmpty(unionId)) {
			this.addAttribute(errors,"用户统一标识(微信开放平台)不能为空");
			this.result = false;
		} else if (unionId.trim().length() > 32L) {
			this.addAttribute(errors,"用户统一标识(微信开放平台)长度不能超过32");
			this.result = false;
		}
		return this;
	}

	public UserValidator onUnionIds(List<String> unionIds) {
		if (isEmpty(unionIds)) {
			this.addAttribute(errors,"用户统一标识(微信开放平台)集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onInvitationCode(Long invitationCode) {
		if (isEmpty(invitationCode)) {
			this.addAttribute(errors,"邀请码不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onCatalog(String catalog) {
		if (isEmpty(catalog)) {
			this.addAttribute(errors,"用户类型(0:普通用户 1:常驻用户)不能为空");
			this.result = false;
		} else if (catalog.trim().length() > 4L) {
			this.addAttribute(errors,"用户类型(0:普通用户 1:常驻用户)长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public UserValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"名字不能为空");
			this.result = false;
		} else if (name.trim().length() > 10L) {
			this.addAttribute(errors,"名字长度不能超过10");
			this.result = false;
		}
		return this;
	}

	public UserValidator onMobile(String mobile) {
		if (isEmpty(mobile)) {
			this.addAttribute(errors,"手机号不能为空");
			this.result = false;
		} else if (mobile.trim().length() > 11L) {
			this.addAttribute(errors,"手机号长度不能超过11");
			this.result = false;
		}
		return this;
	}

	public UserValidator onAvatar(String avatar) {
		if (isEmpty(avatar)) {
			this.addAttribute(errors,"头像不能为空");
			this.result = false;
		} else if (avatar.trim().length() > 64L) {
			this.addAttribute(errors,"头像长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public UserValidator onGender(String gender) {
		if (isEmpty(gender)) {
			this.addAttribute(errors,"性别(0:男 1:女)不能为空");
			this.result = false;
		} else if (gender.trim().length() > 1L) {
			this.addAttribute(errors,"性别(0:男 1:女)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserValidator onLastLoginTime(String lastLoginTime) {
		if (isEmpty(lastLoginTime)) {
			this.addAttribute(errors,"最后登录时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onMinLastLoginTime(String minLastLoginTime) {
		if (isEmpty(minLastLoginTime)) {
			this.addAttribute(errors,"最小最后登录时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onMaxLastLoginTime(String maxLastLoginTime) {
		if (isEmpty(maxLastLoginTime)) {
			this.addAttribute(errors,"最大最后登录时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
