package com.api.validator;

import java.math.BigDecimal;
import java.util.List;


/**
 * 商品属性表数据验证
 *
 * @date 2025-07-27 16:38:19
 */
public class GoodsAttrValueValidator extends Validator {

	public GoodsAttrValueValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onGoodsId(Long goodsId) {
		if (isEmpty(goodsId)) {
			this.addAttribute(errors,"商品id不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onGoodsIds(List<Long> goodsIds) {
		if (isEmpty(goodsIds)) {
			this.addAttribute(errors,"商品id集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onSuk(String suk) {
		if (isEmpty(suk)) {
			this.addAttribute(errors,"商品属性索引值 (attr_value|attr_value[|....])不能为空");
			this.result = false;
		} else if (suk.trim().length() > 128L) {
			this.addAttribute(errors,"商品属性索引值 (attr_value|attr_value[|....])长度不能超过128");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onStock(Integer stock) {
		if (isEmpty(stock)) {
			this.addAttribute(errors,"属性对应的库存不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onSales(Integer sales) {
		if (isEmpty(sales)) {
			this.addAttribute(errors,"销量不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onPrice(BigDecimal price) {
		if (isEmpty(price)) {
			this.addAttribute(errors,"属性金额不能为空");
			this.result = false;
		} else if (price.compareTo(new BigDecimal("999999.99")) > 0) {
			this.addAttribute(errors,"属性金额不能大于999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onImage(String image) {
		if (isEmpty(image)) {
			this.addAttribute(errors,"图片不能为空");
			this.result = false;
		} else if (image.trim().length() > 1000L) {
			this.addAttribute(errors,"图片长度不能超过1000");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onUnique(String unique) {
		if (isEmpty(unique)) {
			this.addAttribute(errors,"唯一值不能为空");
			this.result = false;
		} else if (unique.trim().length() > 8L) {
			this.addAttribute(errors,"唯一值长度不能超过8");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onCost(BigDecimal cost) {
		if (isEmpty(cost)) {
			this.addAttribute(errors,"成本价不能为空");
			this.result = false;
		} else if (cost.compareTo(new BigDecimal("999999.99")) > 0) {
			this.addAttribute(errors,"成本价不能大于999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onOtPrice(BigDecimal otPrice) {
		if (isEmpty(otPrice)) {
			this.addAttribute(errors,"原价不能为空");
			this.result = false;
		} else if (otPrice.compareTo(new BigDecimal("999999.99")) > 0) {
			this.addAttribute(errors,"原价不能大于999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onAttrValue(String attrValue) {
		if (isEmpty(attrValue)) {
			this.addAttribute(errors,"attr_values 创建更新时的属性对应不能为空");
			this.result = false;
		} else if (attrValue.trim().length() > 65535L) {
			this.addAttribute(errors,"attr_values 创建更新时的属性对应长度不能超过65535");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onVersion(Integer version) {
		if (isEmpty(version)) {
			this.addAttribute(errors,"并发版本控制不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValueValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
