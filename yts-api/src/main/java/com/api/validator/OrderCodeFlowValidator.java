package com.api.validator;

import java.util.List;


/**
 * 订单号流水表数据验证
 *
 * @date 2025-07-23 20:14:44
 */
public class OrderCodeFlowValidator extends Validator {

	public OrderCodeFlowValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onOrderCode(String orderCode) {
		if (isEmpty(orderCode)) {
			this.addAttribute(errors,"订单号不能为空");
			this.result = false;
		} else if (orderCode.trim().length() > 64L) {
			this.addAttribute(errors,"订单号长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onPrepayId(String prepayId) {
		if (isEmpty(prepayId)) {
			this.addAttribute(errors,"预支付交易会话标识不能为空");
			this.result = false;
		} else if (prepayId.trim().length() > 64L) {
			this.addAttribute(errors,"预支付交易会话标识长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onPrepayIds(List<String> prepayIds) {
		if (isEmpty(prepayIds)) {
			this.addAttribute(errors,"预支付交易会话标识集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onPaymentType(String paymentType) {
		if (isEmpty(paymentType)) {
			this.addAttribute(errors,"支付类型(1000:微信小程序 1001:支付宝)不能为空");
			this.result = false;
		} else if (paymentType.trim().length() > 4L) {
			this.addAttribute(errors,"支付类型(1000:微信小程序 1001:支付宝)长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onTradeType(String tradeType) {
		if (isEmpty(tradeType)) {
			this.addAttribute(errors,"交易类型不能为空");
			this.result = false;
		} else if (tradeType.trim().length() > 20L) {
			this.addAttribute(errors,"交易类型长度不能超过20");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onTradeState(String tradeState) {
		if (isEmpty(tradeState)) {
			this.addAttribute(errors,"交易状态不能为空");
			this.result = false;
		} else if (tradeState.trim().length() > 50L) {
			this.addAttribute(errors,"交易状态长度不能超过50");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onPayerTotal(Integer payerTotal) {
		if (isEmpty(payerTotal)) {
			this.addAttribute(errors,"支付金额(分)不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onContent(String content) {
		if (isEmpty(content)) {
			this.addAttribute(errors,"通知参数不能为空");
			this.result = false;
		} else if (content.trim().length() > 65535L) {
			this.addAttribute(errors,"通知参数长度不能超过65535");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderCodeFlowValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
