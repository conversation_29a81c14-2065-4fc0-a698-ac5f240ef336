package com.api.validator;

import java.util.List;


/**
 * 用户地址表数据验证
 *
 * @date 2025-07-23 22:58:35
 */
public class UserAddressValidator extends Validator {

	public UserAddressValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onUserId(Long userId) {
		if (isEmpty(userId)) {
			this.addAttribute(errors,"用户编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onUserIds(List<Long> userIds) {
		if (isEmpty(userIds)) {
			this.addAttribute(errors,"用户编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onProvinceId(Long provinceId) {
		if (isEmpty(provinceId)) {
			this.addAttribute(errors,"省份编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onProvinceIds(List<Long> provinceIds) {
		if (isEmpty(provinceIds)) {
			this.addAttribute(errors,"省份编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onCityId(Long cityId) {
		if (isEmpty(cityId)) {
			this.addAttribute(errors,"城市编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onCityIds(List<Long> cityIds) {
		if (isEmpty(cityIds)) {
			this.addAttribute(errors,"城市编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onDistrictId(Long districtId) {
		if (isEmpty(districtId)) {
			this.addAttribute(errors,"区县编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onDistrictIds(List<Long> districtIds) {
		if (isEmpty(districtIds)) {
			this.addAttribute(errors,"区县编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onDetailAddress(String detailAddress) {
		if (isEmpty(detailAddress)) {
			this.addAttribute(errors,"详细地址不能为空");
			this.result = false;
		} else if (detailAddress.trim().length() > 500L) {
			this.addAttribute(errors,"详细地址长度不能超过500");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"姓名不能为空");
			this.result = false;
		} else if (name.trim().length() > 64L) {
			this.addAttribute(errors,"姓名长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onMobile(String mobile) {
		if (isEmpty(mobile)) {
			this.addAttribute(errors,"手机号不能为空");
			this.result = false;
		} else if (mobile.trim().length() > 15L) {
			this.addAttribute(errors,"手机号长度不能超过15");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onDefaultAddress(String defaultAddress) {
		if (isEmpty(defaultAddress)) {
			this.addAttribute(errors,"是否默认地址(0:是 1:否)不能为空");
			this.result = false;
		} else if (defaultAddress.trim().length() > 1L) {
			this.addAttribute(errors,"是否默认地址(0:是 1:否)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserAddressValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
