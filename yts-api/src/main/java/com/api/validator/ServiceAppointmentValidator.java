package com.api.validator;

import java.util.List;


/**
 * 服务预约表数据验证
 *
 * @date 2025-07-19 10:58:27
 */
public class ServiceAppointmentValidator extends Validator {

	public ServiceAppointmentValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onUserId(Long userId) {
		if (isEmpty(userId)) {
			this.addAttribute(errors,"用户id不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onUserIds(List<Long> userIds) {
		if (isEmpty(userIds)) {
			this.addAttribute(errors,"用户id集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onServiceId(Long serviceId) {
		if (isEmpty(serviceId)) {
			this.addAttribute(errors,"服务id不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onServiceIds(List<Long> serviceIds) {
		if (isEmpty(serviceIds)) {
			this.addAttribute(errors,"服务id集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"姓名不能为空");
			this.result = false;
		} else if (name.trim().length() > 64L) {
			this.addAttribute(errors,"姓名长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onMobile(String mobile) {
		if (isEmpty(mobile)) {
			this.addAttribute(errors,"手机号不能为空");
			this.result = false;
		} else if (mobile.trim().length() > 15L) {
			this.addAttribute(errors,"手机号长度不能超过15");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onRemark(String remark) {
		if (isEmpty(remark)) {
			this.addAttribute(errors,"备注不能为空");
			this.result = false;
		} else if (remark.trim().length() > 65535L) {
			this.addAttribute(errors,"备注长度不能超过65535");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onContact(String contact) {
		if (isEmpty(contact)) {
			this.addAttribute(errors,"是否联系(0:已联系 1:未联系)不能为空");
			this.result = false;
		} else if (contact.trim().length() > 1L) {
			this.addAttribute(errors,"是否联系(0:已联系 1:未联系)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onComment(String comment) {
		if (isEmpty(comment)) {
			this.addAttribute(errors,"描述不能为空");
			this.result = false;
		} else if (comment.trim().length() > 65535L) {
			this.addAttribute(errors,"描述长度不能超过65535");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceAppointmentValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
