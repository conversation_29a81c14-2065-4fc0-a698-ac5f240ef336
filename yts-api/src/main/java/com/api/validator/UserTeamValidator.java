package com.api.validator;

import java.util.List;


/**
 * 用户团队表数据验证
 *
 * @date 2025-07-19 10:58:27
 */
public class UserTeamValidator extends Validator {

	public UserTeamValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onCaptainUserId(Long captainUserId) {
		if (isEmpty(captainUserId)) {
			this.addAttribute(errors,"队长编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onCaptainUserIds(List<Long> captainUserIds) {
		if (isEmpty(captainUserIds)) {
			this.addAttribute(errors,"队长编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onTeamMemberUserId(Long teamMemberUserId) {
		if (isEmpty(teamMemberUserId)) {
			this.addAttribute(errors,"队员编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onTeamMemberUserIds(List<Long> teamMemberUserIds) {
		if (isEmpty(teamMemberUserIds)) {
			this.addAttribute(errors,"队员编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserTeamValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
