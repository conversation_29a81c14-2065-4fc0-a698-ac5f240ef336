package com.api.validator;

import java.util.List;


/**
 * 微信应用表数据验证
 *
 * @date 2025-07-16 21:29:32
 */
public class WechatAppValidator extends Validator {

	public WechatAppValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onAppid(String appid) {
		if (isEmpty(appid)) {
			this.addAttribute(errors,"应用ID不能为空");
			this.result = false;
		} else if (appid.trim().length() > 64L) {
			this.addAttribute(errors,"应用ID长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onSecret(String secret) {
		if (isEmpty(secret)) {
			this.addAttribute(errors,"应用密钥不能为空");
			this.result = false;
		} else if (secret.trim().length() > 32L) {
			this.addAttribute(errors,"应用密钥长度不能超过32");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"应用名称不能为空");
			this.result = false;
		} else if (name.trim().length() > 10L) {
			this.addAttribute(errors,"应用名称长度不能超过10");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onAccessToken(String accessToken) {
		if (isEmpty(accessToken)) {
			this.addAttribute(errors,"访问凭证不能为空");
			this.result = false;
		} else if (accessToken.trim().length() > 2000L) {
			this.addAttribute(errors,"访问凭证长度不能超过2000");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onAccessTokenDatetime(String accessTokenDatetime) {
		if (isEmpty(accessTokenDatetime)) {
			this.addAttribute(errors,"凭证刷新日期不能为空");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onMinAccessTokenDatetime(String minAccessTokenDatetime) {
		if (isEmpty(minAccessTokenDatetime)) {
			this.addAttribute(errors,"最小凭证刷新日期不能为空");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onMaxAccessTokenDatetime(String maxAccessTokenDatetime) {
		if (isEmpty(maxAccessTokenDatetime)) {
			this.addAttribute(errors,"最大凭证刷新日期不能为空");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public WechatAppValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
