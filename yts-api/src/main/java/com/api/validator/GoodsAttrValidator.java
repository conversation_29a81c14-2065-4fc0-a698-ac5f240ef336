package com.api.validator;

import java.util.List;


/**
 * 商品属性表数据验证
 *
 * @date 2025-07-27 16:38:19
 */
public class GoodsAttrValidator extends Validator {

	public GoodsAttrValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onGoodsId(Long goodsId) {
		if (isEmpty(goodsId)) {
			this.addAttribute(errors,"商品id不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onGoodsIds(List<Long> goodsIds) {
		if (isEmpty(goodsIds)) {
			this.addAttribute(errors,"商品id集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onAttrName(String attrName) {
		if (isEmpty(attrName)) {
			this.addAttribute(errors,"属性名不能为空");
			this.result = false;
		} else if (attrName.trim().length() > 32L) {
			this.addAttribute(errors,"属性名长度不能超过32");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onAttrValues(String attrValues) {
		if (isEmpty(attrValues)) {
			this.addAttribute(errors,"属性值不能为空");
			this.result = false;
		} else if (attrValues.trim().length() > 1000L) {
			this.addAttribute(errors,"属性值长度不能超过1000");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsAttrValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
