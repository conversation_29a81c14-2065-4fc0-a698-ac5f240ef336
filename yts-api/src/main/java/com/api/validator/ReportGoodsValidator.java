package com.api.validator;

import java.math.BigDecimal;
import java.util.List;


/**
 * 统计商品表数据验证
 *
 * @date 2025-07-20 16:36:02
 */
public class ReportGoodsValidator extends Validator {

	public ReportGoodsValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onDate(String date) {
		if (isEmpty(date)) {
			this.addAttribute(errors,"日期不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onMinDate(String minDate) {
		if (isEmpty(minDate)) {
			this.addAttribute(errors,"最小日期不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onMaxDate(String maxDate) {
		if (isEmpty(maxDate)) {
			this.addAttribute(errors,"最大日期不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onGoodsId(Long goodsId) {
		if (isEmpty(goodsId)) {
			this.addAttribute(errors,"商品编号不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onGoodsIds(List<Long> goodsIds) {
		if (isEmpty(goodsIds)) {
			this.addAttribute(errors,"商品编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onBrowseNum(Long browseNum) {
		if (isEmpty(browseNum)) {
			this.addAttribute(errors,"浏览数量不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onPayNum(Long payNum) {
		if (isEmpty(payNum)) {
			this.addAttribute(errors,"支付数量不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onPayAmount(BigDecimal payAmount) {
		if (isEmpty(payAmount)) {
			this.addAttribute(errors,"支付金额不能为空");
			this.result = false;
		} else if (payAmount.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"支付金额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onRefundNum(Long refundNum) {
		if (isEmpty(refundNum)) {
			this.addAttribute(errors,"退款数量不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onRefundAmount(BigDecimal refundAmount) {
		if (isEmpty(refundAmount)) {
			this.addAttribute(errors,"退款金额不能为空");
			this.result = false;
		} else if (refundAmount.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"退款金额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportGoodsValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
