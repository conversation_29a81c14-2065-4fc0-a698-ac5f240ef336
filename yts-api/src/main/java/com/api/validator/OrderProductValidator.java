package com.api.validator;

import java.math.BigDecimal;
import java.util.List;


/**
 * 订单产品表数据验证
 *
 * @date 2025-07-24 23:22:31
 */
public class OrderProductValidator extends Validator {

	public OrderProductValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onOrderId(Long orderId) {
		if (isEmpty(orderId)) {
			this.addAttribute(errors,"订单编号不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onOrderIds(List<Long> orderIds) {
		if (isEmpty(orderIds)) {
			this.addAttribute(errors,"订单编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onProductCatalog(String productCatalog) {
		if (isEmpty(productCatalog)) {
			this.addAttribute(errors,"类型(0:课程 1:商品)不能为空");
			this.result = false;
		} else if (productCatalog.trim().length() > 4L) {
			this.addAttribute(errors,"类型(0:课程 1:商品)长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onProductId(Long productId) {
		if (isEmpty(productId)) {
			this.addAttribute(errors,"产品编号不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onProductIds(List<Long> productIds) {
		if (isEmpty(productIds)) {
			this.addAttribute(errors,"产品编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onNumber(Integer number) {
		if (isEmpty(number)) {
			this.addAttribute(errors,"数量不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onAmount(BigDecimal amount) {
		if (isEmpty(amount)) {
			this.addAttribute(errors,"实付金额不能为空");
			this.result = false;
		} else if (amount.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"实付金额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onCommissionRatio(BigDecimal commissionRatio) {
		if (isEmpty(commissionRatio)) {
			this.addAttribute(errors,"团长佣金比例(%)不能为空");
			this.result = false;
		} else if (commissionRatio.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"团长佣金比例(%)不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onCommission(BigDecimal commission) {
		if (isEmpty(commission)) {
			this.addAttribute(errors,"团长佣金不能为空");
			this.result = false;
		} else if (commission.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"团长佣金不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onSalesTeacherCommissionRatio(BigDecimal salesTeacherCommissionRatio) {
		if (isEmpty(salesTeacherCommissionRatio)) {
			this.addAttribute(errors,"销售老师佣金比例(%)不能为空");
			this.result = false;
		} else if (salesTeacherCommissionRatio.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"销售老师佣金比例(%)不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onSalesTeacherCommission(BigDecimal salesTeacherCommission) {
		if (isEmpty(salesTeacherCommission)) {
			this.addAttribute(errors,"销售老师佣金不能为空");
			this.result = false;
		} else if (salesTeacherCommission.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"销售老师佣金不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderProductValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
