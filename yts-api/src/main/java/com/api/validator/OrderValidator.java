package com.api.validator;

import java.math.BigDecimal;
import java.util.List;


/**
 * 订单表数据验证
 *
 * @date 2025-07-24 23:22:31
 */
public class OrderValidator extends Validator {

	public OrderValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onCode(String code) {
		if (isEmpty(code)) {
			this.addAttribute(errors,"订单号不能为空");
			this.result = false;
		} else if (code.trim().length() > 64L) {
			this.addAttribute(errors,"订单号长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onPrepayId(String prepayId) {
		if (isEmpty(prepayId)) {
			this.addAttribute(errors,"预支付交易会话标识不能为空");
			this.result = false;
		} else if (prepayId.trim().length() > 64L) {
			this.addAttribute(errors,"预支付交易会话标识长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onPrepayIds(List<String> prepayIds) {
		if (isEmpty(prepayIds)) {
			this.addAttribute(errors,"预支付交易会话标识集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onUserId(Long userId) {
		if (isEmpty(userId)) {
			this.addAttribute(errors,"用户id不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onUserIds(List<Long> userIds) {
		if (isEmpty(userIds)) {
			this.addAttribute(errors,"用户id集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onUserCatalog(Long userCatalog) {
		if (isEmpty(userCatalog)) {
			this.addAttribute(errors,"用户类型不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onOrderCatalog(String orderCatalog) {
		if (isEmpty(orderCatalog)) {
			this.addAttribute(errors,"订单类型(0:课程 1:商品)不能为空");
			this.result = false;
		} else if (orderCatalog.trim().length() > 4L) {
			this.addAttribute(errors,"订单类型(0:课程 1:商品)长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onSelectCatalog(String selectCatalog) {
		if (isEmpty(selectCatalog)) {
			this.addAttribute(errors,"选择类型(0:自选 1:帮选)不能为空");
			this.result = false;
		} else if (selectCatalog.trim().length() > 4L) {
			this.addAttribute(errors,"选择类型(0:自选 1:帮选)长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onAmount(BigDecimal amount) {
		if (isEmpty(amount)) {
			this.addAttribute(errors,"实付金额不能为空");
			this.result = false;
		} else if (amount.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"实付金额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onRefundAmount(BigDecimal refundAmount) {
		if (isEmpty(refundAmount)) {
			this.addAttribute(errors,"退款金额不能为空");
			this.result = false;
		} else if (refundAmount.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"退款金额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onSumCommission(BigDecimal sumCommission) {
		if (isEmpty(sumCommission)) {
			this.addAttribute(errors,"团长总佣金不能为空");
			this.result = false;
		} else if (sumCommission.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"团长总佣金不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onCommissionUserId(Long commissionUserId) {
		if (isEmpty(commissionUserId)) {
			this.addAttribute(errors,"团长佣金人不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onCommissionUserIds(List<Long> commissionUserIds) {
		if (isEmpty(commissionUserIds)) {
			this.addAttribute(errors,"团长佣金人集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onSumSalesTeacherCommission(BigDecimal sumSalesTeacherCommission) {
		if (isEmpty(sumSalesTeacherCommission)) {
			this.addAttribute(errors,"销售老师佣金金额不能为空");
			this.result = false;
		} else if (sumSalesTeacherCommission.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"销售老师佣金金额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onSalesTeacherId(Long salesTeacherId) {
		if (isEmpty(salesTeacherId)) {
			this.addAttribute(errors,"销售老师ID不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onSalesTeacherIds(List<Long> salesTeacherIds) {
		if (isEmpty(salesTeacherIds)) {
			this.addAttribute(errors,"销售老师ID集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onStage(String stage) {
		if (isEmpty(stage)) {
			this.addAttribute(errors,"支付状态不能为空");
			this.result = false;
		} else if (stage.trim().length() > 1L) {
			this.addAttribute(errors,"支付状态长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onOperateStage(String operateStage) {
		if (isEmpty(operateStage)) {
			this.addAttribute(errors,"提货状态(0:已提货 1:未提货)不能为空");
			this.result = false;
		} else if (operateStage.trim().length() > 1L) {
			this.addAttribute(errors,"提货状态(0:已提货 1:未提货)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onDeliveryCatalog(String deliveryCatalog) {
		if (isEmpty(deliveryCatalog)) {
			this.addAttribute(errors,"配送方式(0:自提 1:派送)不能为空");
			this.result = false;
		} else if (deliveryCatalog.trim().length() > 1L) {
			this.addAttribute(errors,"配送方式(0:自提 1:派送)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onReservationTime(String reservationTime) {
		if (isEmpty(reservationTime)) {
			this.addAttribute(errors,"预约时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onMinReservationTime(String minReservationTime) {
		if (isEmpty(minReservationTime)) {
			this.addAttribute(errors,"最小预约时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onMaxReservationTime(String maxReservationTime) {
		if (isEmpty(maxReservationTime)) {
			this.addAttribute(errors,"最大预约时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onUserAddressId(Long userAddressId) {
		if (isEmpty(userAddressId)) {
			this.addAttribute(errors,"用户地址编号不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onUserAddressIds(List<Long> userAddressIds) {
		if (isEmpty(userAddressIds)) {
			this.addAttribute(errors,"用户地址编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onPayTime(String payTime) {
		if (isEmpty(payTime)) {
			this.addAttribute(errors,"支付时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onMinPayTime(String minPayTime) {
		if (isEmpty(minPayTime)) {
			this.addAttribute(errors,"最小支付时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onMaxPayTime(String maxPayTime) {
		if (isEmpty(maxPayTime)) {
			this.addAttribute(errors,"最大支付时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
