package com.api.validator;

import java.util.List;


/**
 * 系统用户日志表数据验证
 *
 * @date 2025-07-20 18:59:08
 */
public class LogValidator extends Validator {

	public LogValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onPlatform(String platform) {
		if (isEmpty(platform)) {
			this.addAttribute(errors,"平台不能为空");
			this.result = false;
		} else if (platform.trim().length() > 20L) {
			this.addAttribute(errors,"平台长度不能超过20");
			this.result = false;
		}
		return this;
	}

	public LogValidator onVersion(String version) {
		if (isEmpty(version)) {
			this.addAttribute(errors,"版本不能为空");
			this.result = false;
		} else if (version.trim().length() > 10L) {
			this.addAttribute(errors,"版本长度不能超过10");
			this.result = false;
		}
		return this;
	}

	public LogValidator onType(String type) {
		if (isEmpty(type)) {
			this.addAttribute(errors,"用户类型不能为空");
			this.result = false;
		} else if (type.trim().length() > 1L) {
			this.addAttribute(errors,"用户类型长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public LogValidator onUserId(Long userId) {
		if (isEmpty(userId)) {
			this.addAttribute(errors,"用户编号不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onUserIds(List<Long> userIds) {
		if (isEmpty(userIds)) {
			this.addAttribute(errors,"用户编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onIp(String ip) {
		if (isEmpty(ip)) {
			this.addAttribute(errors,"IP不能为空");
			this.result = false;
		} else if (ip.trim().length() > 32L) {
			this.addAttribute(errors,"IP长度不能超过32");
			this.result = false;
		}
		return this;
	}

	public LogValidator onUrl(String url) {
		if (isEmpty(url)) {
			this.addAttribute(errors,"地址不能为空");
			this.result = false;
		} else if (url.trim().length() > 64L) {
			this.addAttribute(errors,"地址长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public LogValidator onRequestId(String requestId) {
		if (isEmpty(requestId)) {
			this.addAttribute(errors,"请求编号不能为空");
			this.result = false;
		} else if (requestId.trim().length() > 64L) {
			this.addAttribute(errors,"请求编号长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public LogValidator onRequestIds(List<String> requestIds) {
		if (isEmpty(requestIds)) {
			this.addAttribute(errors,"请求编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onStartTime(String startTime) {
		if (isEmpty(startTime)) {
			this.addAttribute(errors,"开始时间不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onMinStartTime(String minStartTime) {
		if (isEmpty(minStartTime)) {
			this.addAttribute(errors,"最小开始时间不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onMaxStartTime(String maxStartTime) {
		if (isEmpty(maxStartTime)) {
			this.addAttribute(errors,"最大开始时间不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onEndTime(String endTime) {
		if (isEmpty(endTime)) {
			this.addAttribute(errors,"结束时间不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onMinEndTime(String minEndTime) {
		if (isEmpty(minEndTime)) {
			this.addAttribute(errors,"最小结束时间不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onMaxEndTime(String maxEndTime) {
		if (isEmpty(maxEndTime)) {
			this.addAttribute(errors,"最大结束时间不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onDuration(Integer duration) {
		if (isEmpty(duration)) {
			this.addAttribute(errors,"执行时长不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public LogValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public LogValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
