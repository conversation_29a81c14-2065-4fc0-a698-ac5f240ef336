package com.api.validator;

import java.util.List;


/**
 * 区(县)表数据验证
 *
 * @date 2025-07-23 22:58:35
 */
public class DistrictValidator extends Validator {

	public DistrictValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public DistrictValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public DistrictValidator onCityId(Long cityId) {
		if (isEmpty(cityId)) {
			this.addAttribute(errors,"城市编号不能为空");
			this.result = false;
		}
		return this;
	}

	public DistrictValidator onCityIds(List<Long> cityIds) {
		if (isEmpty(cityIds)) {
			this.addAttribute(errors,"城市编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public DistrictValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"区(县)名称不能为空");
			this.result = false;
		} else if (name.trim().length() > 20L) {
			this.addAttribute(errors,"区(县)名称长度不能超过20");
			this.result = false;
		}
		return this;
	}

	public DistrictValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public DistrictValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public DistrictValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public DistrictValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public DistrictValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public DistrictValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public DistrictValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
