package com.api.validator;

import java.math.BigDecimal;
import java.util.List;


/**
 * 商品规格sku表数据验证
 *
 * @date 2025-07-27 21:26:17
 */
public class GoodsSpecificationSkuValidator extends Validator {

	public GoodsSpecificationSkuValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onGoodsId(Long goodsId) {
		if (isEmpty(goodsId)) {
			this.addAttribute(errors,"商品id不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onGoodsIds(List<Long> goodsIds) {
		if (isEmpty(goodsIds)) {
			this.addAttribute(errors,"商品id集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onSpecValues(String specValues) {
		if (isEmpty(specValues)) {
			this.addAttribute(errors,"SKU属性不能为空");
			this.result = false;
		} else if (specValues.trim().length() > 65535L) {
			this.addAttribute(errors,"SKU属性长度不能超过65535");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onImgUrl(String imgUrl) {
		if (isEmpty(imgUrl)) {
			this.addAttribute(errors,"商品封面不能为空");
			this.result = false;
		} else if (imgUrl.trim().length() > 256L) {
			this.addAttribute(errors,"商品封面长度不能超过256");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onBuyingPrice(BigDecimal buyingPrice) {
		if (isEmpty(buyingPrice)) {
			this.addAttribute(errors,"成本价不能为空");
			this.result = false;
		} else if (buyingPrice.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"成本价不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onOriginalPrice(BigDecimal originalPrice) {
		if (isEmpty(originalPrice)) {
			this.addAttribute(errors,"划线价不能为空");
			this.result = false;
		} else if (originalPrice.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"划线价不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onPrice(BigDecimal price) {
		if (isEmpty(price)) {
			this.addAttribute(errors,"售价不能为空");
			this.result = false;
		} else if (price.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"售价不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onStock(Integer stock) {
		if (isEmpty(stock)) {
			this.addAttribute(errors,"库存数量不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationSkuValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
