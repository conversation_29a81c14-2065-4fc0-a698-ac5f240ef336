package com.api.validator;

import java.util.List;


/**
 * 服务信息表数据验证
 *
 * @date 2025-07-20 12:28:29
 */
public class ServiceInfoValidator extends Validator {

	public ServiceInfoValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceInfoValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceInfoValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"服务名称不能为空");
			this.result = false;
		} else if (name.trim().length() > 128L) {
			this.addAttribute(errors,"服务名称长度不能超过128");
			this.result = false;
		}
		return this;
	}

	public ServiceInfoValidator onCoverUrl(String coverUrl) {
		if (isEmpty(coverUrl)) {
			this.addAttribute(errors,"服务封面url不能为空");
			this.result = false;
		} else if (coverUrl.trim().length() > 128L) {
			this.addAttribute(errors,"服务封面url长度不能超过128");
			this.result = false;
		}
		return this;
	}

	public ServiceInfoValidator onComment(String comment) {
		if (isEmpty(comment)) {
			this.addAttribute(errors,"备注不能为空");
			this.result = false;
		} else if (comment.trim().length() > 65535L) {
			this.addAttribute(errors,"备注长度不能超过65535");
			this.result = false;
		}
		return this;
	}

	public ServiceInfoValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public ServiceInfoValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceInfoValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceInfoValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceInfoValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceInfoValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ServiceInfoValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
