package com.api.validator;

import java.util.List;


/**
 * 用户佣金提现明细表数据验证
 *
 * @date 2025-07-19 10:58:27
 */
public class UserCommissionCashFlowValidator extends Validator {

	public UserCommissionCashFlowValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onUserId(Long userId) {
		if (isEmpty(userId)) {
			this.addAttribute(errors,"用户编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onUserIds(List<Long> userIds) {
		if (isEmpty(userIds)) {
			this.addAttribute(errors,"用户编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onOrderId(Integer orderId) {
		if (isEmpty(orderId)) {
			this.addAttribute(errors,"订单编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onOrderIds(List<Integer> orderIds) {
		if (isEmpty(orderIds)) {
			this.addAttribute(errors,"订单编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashFlowValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
