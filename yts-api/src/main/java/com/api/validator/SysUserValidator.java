package com.api.validator;

import java.util.List;


/**
 * 系统用户表数据验证
 *
 * @date 2025-07-16 21:03:31
 */
public class SysUserValidator extends Validator {

	public SysUserValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"姓名不能为空");
			this.result = false;
		} else if (name.trim().length() > 10L) {
			this.addAttribute(errors,"姓名长度不能超过10");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onUsername(String username) {
		if (isEmpty(username)) {
			this.addAttribute(errors,"用户名不能为空");
			this.result = false;
		} else if (username.trim().length() > 30L) {
			this.addAttribute(errors,"用户名长度不能超过30");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onMobile(String mobile) {
		if (isEmpty(mobile)) {
			this.addAttribute(errors,"手机号不能为空");
			this.result = false;
		} else if (mobile.trim().length() > 15L) {
			this.addAttribute(errors,"手机号长度不能超过15");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onPassword(String password) {
		if (isEmpty(password)) {
			this.addAttribute(errors,"密码不能为空");
			this.result = false;
		} else if (password.trim().length() > 50L) {
			this.addAttribute(errors,"密码长度不能超过50");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onStage(String stage) {
		if (isEmpty(stage)) {
			this.addAttribute(errors,"启用/禁用(0: 启用 1:禁用)不能为空");
			this.result = false;
		} else if (stage.trim().length() > 1L) {
			this.addAttribute(errors,"启用/禁用(0: 启用 1:禁用)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public SysUserValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
