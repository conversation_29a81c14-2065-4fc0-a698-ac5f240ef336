package com.api.validator;

import java.math.BigDecimal;
import java.util.List;


/**
 * 用户佣金提现表数据验证
 *
 * @date 2025-07-19 10:58:27
 */
public class UserCommissionCashValidator extends Validator {

	public UserCommissionCashValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onUserId(Long userId) {
		if (isEmpty(userId)) {
			this.addAttribute(errors,"用户编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onUserIds(List<Long> userIds) {
		if (isEmpty(userIds)) {
			this.addAttribute(errors,"用户编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onSumCommission(BigDecimal sumCommission) {
		if (isEmpty(sumCommission)) {
			this.addAttribute(errors,"总佣金不能为空");
			this.result = false;
		} else if (sumCommission.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"总佣金不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onOrderSize(Integer orderSize) {
		if (isEmpty(orderSize)) {
			this.addAttribute(errors,"订单数量不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onOptSysUserId(Long optSysUserId) {
		if (isEmpty(optSysUserId)) {
			this.addAttribute(errors,"操作人编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onOptSysUserIds(List<Long> optSysUserIds) {
		if (isEmpty(optSysUserIds)) {
			this.addAttribute(errors,"操作人编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionCashValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
