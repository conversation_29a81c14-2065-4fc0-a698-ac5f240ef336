package com.api.validator;

import java.util.List;


/**
 * 订单退款记录表数据验证
 *
 * @date 2025-07-29 22:28:19
 */
public class OrderRefundInfoValidator extends Validator {

	public OrderRefundInfoValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onOrderNo(String orderNo) {
		if (isEmpty(orderNo)) {
			this.addAttribute(errors,"商户订单编号不能为空");
			this.result = false;
		} else if (orderNo.trim().length() > 50L) {
			this.addAttribute(errors,"商户订单编号长度不能超过50");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onRefundNo(String refundNo) {
		if (isEmpty(refundNo)) {
			this.addAttribute(errors,"商户退款单编号不能为空");
			this.result = false;
		} else if (refundNo.trim().length() > 50L) {
			this.addAttribute(errors,"商户退款单编号长度不能超过50");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onRefundId(String refundId) {
		if (isEmpty(refundId)) {
			this.addAttribute(errors,"支付系统退款单号不能为空");
			this.result = false;
		} else if (refundId.trim().length() > 50L) {
			this.addAttribute(errors,"支付系统退款单号长度不能超过50");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onRefundIds(List<String> refundIds) {
		if (isEmpty(refundIds)) {
			this.addAttribute(errors,"支付系统退款单号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onTotalFee(Integer totalFee) {
		if (isEmpty(totalFee)) {
			this.addAttribute(errors,"原订单金额(分)不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onRefund(Integer refund) {
		if (isEmpty(refund)) {
			this.addAttribute(errors,"退款金额(分)不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onReason(String reason) {
		if (isEmpty(reason)) {
			this.addAttribute(errors,"退款原因不能为空");
			this.result = false;
		} else if (reason.trim().length() > 50L) {
			this.addAttribute(errors,"退款原因长度不能超过50");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onRefundStatus(String refundStatus) {
		if (isEmpty(refundStatus)) {
			this.addAttribute(errors,"退款状态不能为空");
			this.result = false;
		} else if (refundStatus.trim().length() > 10L) {
			this.addAttribute(errors,"退款状态长度不能超过10");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onContentReturn(String contentReturn) {
		if (isEmpty(contentReturn)) {
			this.addAttribute(errors,"申请退款返回参数不能为空");
			this.result = false;
		} else if (contentReturn.trim().length() > 65535L) {
			this.addAttribute(errors,"申请退款返回参数长度不能超过65535");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onContentNotify(String contentNotify) {
		if (isEmpty(contentNotify)) {
			this.addAttribute(errors,"退款结果通知参数不能为空");
			this.result = false;
		} else if (contentNotify.trim().length() > 65535L) {
			this.addAttribute(errors,"退款结果通知参数长度不能超过65535");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public OrderRefundInfoValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
