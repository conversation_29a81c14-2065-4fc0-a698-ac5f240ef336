package com.api.validator;

import java.math.BigDecimal;
import java.util.List;


/**
 * 商品表数据验证
 *
 * @date 2025-07-27 11:42:53
 */
public class GoodsValidator extends Validator {

	public GoodsValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"商品名称不能为空");
			this.result = false;
		} else if (name.trim().length() > 64L) {
			this.addAttribute(errors,"商品名称长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onUnit(String unit) {
		if (isEmpty(unit)) {
			this.addAttribute(errors,"商品单位不能为空");
			this.result = false;
		} else if (unit.trim().length() > 5L) {
			this.addAttribute(errors,"商品单位长度不能超过5");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onImgUrl(String imgUrl) {
		if (isEmpty(imgUrl)) {
			this.addAttribute(errors,"商品封面不能为空");
			this.result = false;
		} else if (imgUrl.trim().length() > 256L) {
			this.addAttribute(errors,"商品封面长度不能超过256");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onCatalog(String catalog) {
		if (isEmpty(catalog)) {
			this.addAttribute(errors,"商品类型不能为空");
			this.result = false;
		} else if (catalog.trim().length() > 4L) {
			this.addAttribute(errors,"商品类型长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onGoodsTypeId(Long goodsTypeId) {
		if (isEmpty(goodsTypeId)) {
			this.addAttribute(errors,"商品分类编号不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onGoodsTypeIds(List<Long> goodsTypeIds) {
		if (isEmpty(goodsTypeIds)) {
			this.addAttribute(errors,"商品分类编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onBuyingPrice(BigDecimal buyingPrice) {
		if (isEmpty(buyingPrice)) {
			this.addAttribute(errors,"成本价不能为空");
			this.result = false;
		} else if (buyingPrice.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"成本价不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onOriginalPrice(BigDecimal originalPrice) {
		if (isEmpty(originalPrice)) {
			this.addAttribute(errors,"划线价不能为空");
			this.result = false;
		} else if (originalPrice.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"划线价不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onPrice(BigDecimal price) {
		if (isEmpty(price)) {
			this.addAttribute(errors,"售价不能为空");
			this.result = false;
		} else if (price.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"售价不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onGrossProfit(BigDecimal grossProfit) {
		if (isEmpty(grossProfit)) {
			this.addAttribute(errors,"毛利(售价-成本价)不能为空");
			this.result = false;
		} else if (grossProfit.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"毛利(售价-成本价)不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onOpenCommission(String openCommission) {
		if (isEmpty(openCommission)) {
			this.addAttribute(errors,"是否分佣(0:是 1:否)不能为空");
			this.result = false;
		} else if (openCommission.trim().length() > 1L) {
			this.addAttribute(errors,"是否分佣(0:是 1:否)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onCommissionRatio(BigDecimal commissionRatio) {
		if (isEmpty(commissionRatio)) {
			this.addAttribute(errors,"团长佣金比例(%)不能为空");
			this.result = false;
		} else if (commissionRatio.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"团长佣金比例(%)不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onSalesTeacherCommissionRatio(BigDecimal salesTeacherCommissionRatio) {
		if (isEmpty(salesTeacherCommissionRatio)) {
			this.addAttribute(errors,"销售老师佣金比例(%)不能为空");
			this.result = false;
		} else if (salesTeacherCommissionRatio.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"销售老师佣金比例(%)不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onDetailImgUrl(String detailImgUrl) {
		if (isEmpty(detailImgUrl)) {
			this.addAttribute(errors,"商品详情图片不能为空");
			this.result = false;
		} else if (detailImgUrl.trim().length() > 256L) {
			this.addAttribute(errors,"商品详情图片长度不能超过256");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onComment(String comment) {
		if (isEmpty(comment)) {
			this.addAttribute(errors,"描述不能为空");
			this.result = false;
		} else if (comment.trim().length() > 65535L) {
			this.addAttribute(errors,"描述长度不能超过65535");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onSalesVolume(Integer salesVolume) {
		if (isEmpty(salesVolume)) {
			this.addAttribute(errors,"虚拟销量不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onRealSalesVolume(Integer realSalesVolume) {
		if (isEmpty(realSalesVolume)) {
			this.addAttribute(errors,"真实销量不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onStage(String stage) {
		if (isEmpty(stage)) {
			this.addAttribute(errors,"上架状态(0:上架 1:下架)不能为空");
			this.result = false;
		} else if (stage.trim().length() > 1L) {
			this.addAttribute(errors,"上架状态(0:上架 1:下架)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
