package com.api.validator;

import java.util.List;


/**
 * 商品分类表数据验证
 *
 * @date 2025-07-20 10:32:17
 */
public class GoodsTypeValidator extends Validator {

	public GoodsTypeValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onCatalog(String catalog) {
		if (isEmpty(catalog)) {
			this.addAttribute(errors,"商品类型不能为空");
			this.result = false;
		} else if (catalog.trim().length() > 4L) {
			this.addAttribute(errors,"商品类型长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"分类名称不能为空");
			this.result = false;
		} else if (name.trim().length() > 64L) {
			this.addAttribute(errors,"分类名称长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onSort(Integer sort) {
		if (isEmpty(sort)) {
			this.addAttribute(errors,"排序字段不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onStage(String stage) {
		if (isEmpty(stage)) {
			this.addAttribute(errors,"上架状态(0:上架 1:下架)不能为空");
			this.result = false;
		} else if (stage.trim().length() > 1L) {
			this.addAttribute(errors,"上架状态(0:上架 1:下架)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsTypeValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
