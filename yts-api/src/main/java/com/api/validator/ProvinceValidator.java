package com.api.validator;

import java.util.List;


/**
 * 省份表数据验证
 *
 * @date 2025-07-23 22:58:36
 */
public class ProvinceValidator extends Validator {

	public ProvinceValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public ProvinceValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ProvinceValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"城市名称不能为空");
			this.result = false;
		} else if (name.trim().length() > 10L) {
			this.addAttribute(errors,"城市名称长度不能超过10");
			this.result = false;
		}
		return this;
	}

	public ProvinceValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public ProvinceValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ProvinceValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ProvinceValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ProvinceValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ProvinceValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ProvinceValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
