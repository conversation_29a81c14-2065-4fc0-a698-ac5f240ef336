package com.api.validator;

import java.util.List;


/**
 * 商品规格值表数据验证
 *
 * @date 2025-07-27 21:26:17
 */
public class GoodsSpecificationValueValidator extends Validator {

	public GoodsSpecificationValueValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onGoodsId(Long goodsId) {
		if (isEmpty(goodsId)) {
			this.addAttribute(errors,"商品id不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onGoodsIds(List<Long> goodsIds) {
		if (isEmpty(goodsIds)) {
			this.addAttribute(errors,"商品id集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onGoodsSpecificationTypeId(Long goodsSpecificationTypeId) {
		if (isEmpty(goodsSpecificationTypeId)) {
			this.addAttribute(errors,"规格类型ID不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onGoodsSpecificationTypeIds(List<Long> goodsSpecificationTypeIds) {
		if (isEmpty(goodsSpecificationTypeIds)) {
			this.addAttribute(errors,"规格类型ID集合不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onName(String name) {
		if (isEmpty(name)) {
			this.addAttribute(errors,"规格值不能为空");
			this.result = false;
		} else if (name.trim().length() > 64L) {
			this.addAttribute(errors,"规格值长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public GoodsSpecificationValueValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
