package com.api.validator;

import java.math.BigDecimal;
import java.util.List;


/**
 * 用户佣金明细表数据验证
 *
 * @date 2025-07-27 11:32:54
 */
public class UserCommissionFlowValidator extends Validator {

	public UserCommissionFlowValidator onId(Long id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onIds(List<Long> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onFlowCode(String flowCode) {
		if (isEmpty(flowCode)) {
			this.addAttribute(errors,"流水号不能为空");
			this.result = false;
		} else if (flowCode.trim().length() > 64L) {
			this.addAttribute(errors,"流水号长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onUserId(Long userId) {
		if (isEmpty(userId)) {
			this.addAttribute(errors,"用户编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onUserIds(List<Long> userIds) {
		if (isEmpty(userIds)) {
			this.addAttribute(errors,"用户编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onOrderId(Long orderId) {
		if (isEmpty(orderId)) {
			this.addAttribute(errors,"订单ID不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onOrderIds(List<Long> orderIds) {
		if (isEmpty(orderIds)) {
			this.addAttribute(errors,"订单ID集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onOrderCode(String orderCode) {
		if (isEmpty(orderCode)) {
			this.addAttribute(errors,"订单号不能为空");
			this.result = false;
		} else if (orderCode.trim().length() > 64L) {
			this.addAttribute(errors,"订单号长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onPrepayId(String prepayId) {
		if (isEmpty(prepayId)) {
			this.addAttribute(errors,"预支付交易会话标识不能为空");
			this.result = false;
		} else if (prepayId.trim().length() > 64L) {
			this.addAttribute(errors,"预支付交易会话标识长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onPrepayIds(List<String> prepayIds) {
		if (isEmpty(prepayIds)) {
			this.addAttribute(errors,"预支付交易会话标识集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onTransactionTime(String transactionTime) {
		if (isEmpty(transactionTime)) {
			this.addAttribute(errors,"交易时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onMinTransactionTime(String minTransactionTime) {
		if (isEmpty(minTransactionTime)) {
			this.addAttribute(errors,"最小交易时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onMaxTransactionTime(String maxTransactionTime) {
		if (isEmpty(maxTransactionTime)) {
			this.addAttribute(errors,"最大交易时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onAmount(BigDecimal amount) {
		if (isEmpty(amount)) {
			this.addAttribute(errors,"流转金额不能为空");
			this.result = false;
		} else if (amount.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"流转金额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onCatalog(String catalog) {
		if (isEmpty(catalog)) {
			this.addAttribute(errors,"分类(1000:分佣流水)不能为空");
			this.result = false;
		} else if (catalog.trim().length() > 4L) {
			this.addAttribute(errors,"分类(1000:分佣流水)长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onPayChannel(String payChannel) {
		if (isEmpty(payChannel)) {
			this.addAttribute(errors,"支付方式(1000:微信小程序支付)不能为空");
			this.result = false;
		} else if (payChannel.trim().length() > 4L) {
			this.addAttribute(errors,"支付方式(1000:微信小程序支付)长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onIncome(String income) {
		if (isEmpty(income)) {
			this.addAttribute(errors,"收支出(0:收入 1:支出)不能为空");
			this.result = false;
		} else if (income.trim().length() > 1L) {
			this.addAttribute(errors,"收支出(0:收入 1:支出)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onStage(String stage) {
		if (isEmpty(stage)) {
			this.addAttribute(errors,"处理阶段(0:处理中 1:交易失败 2:交易成功)不能为空");
			this.result = false;
		} else if (stage.trim().length() > 1L) {
			this.addAttribute(errors,"处理阶段(0:处理中 1:交易失败 2:交易成功)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onLabel(String label) {
		if (isEmpty(label)) {
			this.addAttribute(errors,"标签不能为空");
			this.result = false;
		} else if (label.trim().length() > 100L) {
			this.addAttribute(errors,"标签长度不能超过100");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onComment(String comment) {
		if (isEmpty(comment)) {
			this.addAttribute(errors,"备注不能为空");
			this.result = false;
		} else if (comment.trim().length() > 64L) {
			this.addAttribute(errors,"备注长度不能超过64");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCommissionFlowValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
