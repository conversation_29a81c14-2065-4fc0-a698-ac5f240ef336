package com.api.config;

import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.parameters.Parameter;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.web.method.HandlerMethod;

import com.api.constant.App;

@SpringBootConfiguration
public class SwaggerConfig {

    @Value("${spring.application.name}")
	private String appName;

    @Value("${spring.application.version}")
	private String appVersion;
    
    @Bean
    public OpenAPI customApi() {
        return new OpenAPI().info(new Info().title(appName).version(appVersion));
    }

    @Bean
    public OperationCustomizer customGlobalHeaders() {
        return (Operation operation, HandlerMethod handlerMethod) -> {
            Parameter token = new Parameter()
                    .in(ParameterIn.HEADER.toString())
                    .schema(new StringSchema())
                    .name(App.HTTP_HEADER_APP_TOKEN)
                    .description("凭证")
                    .required(false);
            Parameter platform = new Parameter()
                    .in(ParameterIn.HEADER.toString())
                    .schema(new StringSchema())
                    .name(App.HTTP_HEADER_APP_PLATFORM)
                    .description("平台")
                    .required(false);
            Parameter agent = new Parameter()
                    .in(ParameterIn.HEADER.toString())
                    .schema(new StringSchema())
                    .name(App.HTTP_HEADER_APP_AGENT)
                    .description("其他")
                    .required(false);
            Parameter version = new Parameter()
                    .in(ParameterIn.HEADER.toString())
                    .schema(new StringSchema())
                    .name(App.HTTP_HEADER_APP_VERSION)
                    .description("版本")
                    .required(false);
            operation.addParametersItem(token);
            operation.addParametersItem(platform);
            operation.addParametersItem(version);
            operation.addParametersItem(agent);
            return operation;
        };
    }
}