package com.api.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.PageResponse;
import com.api.bean.UserCommissionStatsResponse;
import com.api.bean.UserRequest;
import com.api.bean.UserResponse;
import com.api.config.Token;
import com.api.sequence.LogSequence;
import com.api.validator.UserValidator;
import com.common.bean.JsonWebToken;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.bean.UserExport;
import com.common.bean.UserToken;
import com.common.bean.WechatLoginResponse;
import com.common.client.WechatClient;
import com.common.constant.App;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.Device;
import com.common.constant.ExportStage;
import com.common.constant.ExportType;
import com.common.constant.OrderStage;
import com.common.constant.TokenType;
import com.common.constant.UserCatalog;
import com.common.constant.UserCommissionFlowCatalog;
import com.common.constant.UserCommissionFlowIncome;
import com.common.util.DateUtil;
import com.common.util.EncryptUtil;
import com.domain.Order;
import com.domain.User;
import com.domain.UserCommission;
import com.domain.UserCommissionFlow;
import com.domain.UserTeam;
import com.domain.complex.OrderQuery;
import com.domain.complex.UserCommissionFlowQuery;
import com.domain.complex.UserCommissionQuery;
import com.domain.complex.UserQuery;
import com.domain.complex.UserTeamQuery;
import com.service.OrderService;
import com.service.UserCommissionFlowService;
import com.service.UserCommissionService;
import com.service.UserService;
import com.service.UserTeamService;


@Tag(name = "用户表")
@RestController
public class UserController extends BaseController {
    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private UserTeamService userTeamService;
    @Autowired
    private UserCommissionFlowService userCommissionFlowService;
    @Autowired
    private UserCommissionService userCommissionService;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    /**
     * 微信小程序登录
     * @param request
     * @return
     */
    @RequestMapping("/v1/user/wechat/login")
    public Response<?> login(@RequestBody UserRequest request){
        Date serverTime = this.getServerTime();
        UserResponse response = new UserResponse();
        if (isEmpty(request.getCode())){
            return new Response<>(ERROR,"请传入小程序授权的code");
        }
        StringBuilder loginBuilder = new StringBuilder("/sns/jscode2session?");
        loginBuilder.append("appid=");
        loginBuilder.append(App.WEHCAT_APP_ID);
        loginBuilder.append("&secret=");
        loginBuilder.append(App.WEHCAT_APP_SECRET);
        loginBuilder.append("&js_code=");
        loginBuilder.append(request.getCode());
        loginBuilder.append("&grant_type=authorization_code");
        WechatClient<WechatLoginResponse> client = new WechatClient<>();
        client.setMethod(HttpMethod.GET);
        WechatLoginResponse wechatLoginResponse = client.execute(loginBuilder.toString(),null,WechatLoginResponse.class);
        if (wechatLoginResponse.getErrcode() != null && wechatLoginResponse.getErrcode() != 0){
            return new Response<>(ERROR,"微信授权小程序失败");
        }
        String openId = wechatLoginResponse.getOpenid();
        if (this.isEmpty(openId)){
            return new Response<>(ERROR,"微信授权小程序失败");
        }
        Lock lock = getLock(redisTemplate, CacheKey.LOCK_USER_LOGIN, openId);
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿点击过快");
        }
        try {
            response.setOpenId(openId);
            // 查询是否已经注册过
            User user = userService.findByOpenId(openId);
            if (user != null){
                User tokenUser = new User();
                tokenUser.setId(user.getId());
                tokenUser.setOpenId(user.getOpenId());
                String token = this.getUserToken(tokenUser);
                response.setToken(token);
                response.setInvitationCode(user.getInvitationCode());
                response.setQrCode(user.getQrCode());
                response.setCatalog(user.getCatalog());
                response.setName(user.getName());
                response.setMobile(user.getMobile());
                response.setAvatar(user.getAvatar());
                response.setGender(user.getGender());
                response.setLastLoginTime(DateUtil.format(user.getLastLoginTime(), DATETIME_FORMAT));
                response.setId(user.getId());
                //修改用户最后登录时间
                User modifyUser = new User();
                modifyUser.setId(user.getId());
                modifyUser.setLastLoginTime(serverTime);
                userService.modifyById(modifyUser);
            }else {
                //未注册
                return new Response<>("-1");
            }
            return new Response<>(response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }finally {
            lock.unlock();
        }
    }


    /**
     * 微信小程序注册
     * @param request
     * @return
     */
    @RequestMapping("/v1/user/wechat/register")
    public Response<?> register(@RequestBody UserRequest request){
        Date serverTime = this.getServerTime();
        UserResponse response = new UserResponse();
        if (isEmpty(request.getCode())){
            return new Response<>(ERROR,"请传入小程序授权的code");
        }
        StringBuilder loginBuilder = new StringBuilder("/sns/jscode2session?");
        loginBuilder.append("appid=");
        loginBuilder.append(App.WEHCAT_APP_ID);
        loginBuilder.append("&secret=");
        loginBuilder.append(App.WEHCAT_APP_SECRET);
        loginBuilder.append("&js_code=");
        loginBuilder.append(request.getCode());
        loginBuilder.append("&grant_type=authorization_code");
        WechatClient<WechatLoginResponse> client = new WechatClient<>();
        client.setMethod(HttpMethod.GET);
        WechatLoginResponse wechatLoginResponse = client.execute(loginBuilder.toString(),null,WechatLoginResponse.class);
        if (wechatLoginResponse.getErrcode() != null && wechatLoginResponse.getErrcode() != 0){
            return new Response<>(ERROR,"微信授权小程序失败");
        }
        String openId = wechatLoginResponse.getOpenid();
        if (this.isEmpty(openId)){
            return new Response<>(ERROR,"微信授权小程序失败");
        }
        Lock lock = getLock(redisTemplate, CacheKey.LOCK_USER_LOGIN, openId);
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿点击过快");
        }
        try {
            response.setOpenId(openId);
            // 查询是否已经注册过
            User user = userService.findByOpenId(openId);
            if (user != null){
                User tokenUser = new User();
                tokenUser.setId(user.getId());
                tokenUser.setOpenId(user.getOpenId());
                String token = this.getUserToken(tokenUser);
                response.setToken(token);
                response.setInvitationCode(user.getInvitationCode());
                response.setQrCode(user.getQrCode());
                response.setCatalog(user.getCatalog());
                response.setName(user.getName());
                response.setMobile(user.getMobile());
                response.setAvatar(user.getAvatar());
                response.setGender(user.getGender());
                response.setLastLoginTime(DateUtil.format(user.getLastLoginTime(), DATETIME_FORMAT));
                response.setId(user.getId());
                //修改用户最后登录时间
                User modifyUser = new User();
                modifyUser.setId(user.getId());
                modifyUser.setLastLoginTime(serverTime);
                userService.modifyById(modifyUser);
            }else {
                //判断姓名手机号是否为空
                if (isEmpty(request.getName()) || isEmpty(request.getMobile())){
                    return new Response<>(ERROR,"姓名和手机号不能为空");
                }
                //判断手机号是否已经注册
                UserQuery userQuery = new UserQuery();
                userQuery.setMobile(request.getMobile());
                userQuery.setStatus(DataStatus.Y.getCode());
                List<User> users = userService.findAll(userQuery);
                if (!users.isEmpty()){
                    return new Response<>(ERROR,"手机号已经注册");
                }
                // 直接注册为新用户
                User userCreate = new User();
                userCreate.setOpenId(openId);
                //生成邀请码
                Long invitationCode = this.redisTemplate.opsForValue().increment(CacheKey.INVITATION_CODE, 1L);
                userCreate.setInvitationCode(invitationCode);
                userCreate.setQrCode("pages/user/user?invitationCode='" + invitationCode + "'");
                userCreate.setCatalog(UserCatalog.C0.getCode());
                userCreate.setName(request.getName());
                userCreate.setMobile(request.getMobile());
                userCreate.setAvatar(request.getAvatar());
                userCreate.setGender(request.getGender());
                userCreate.setLastLoginTime(serverTime);
                userCreate.setStatus(DataStatus.Y.getCode());
                userCreate.setModifyTime(serverTime);
                userCreate.setCreateTime(serverTime);

                userService.create(userCreate);

                String token = this.getUserToken(userCreate);
                response.setToken(token);
                response.setInvitationCode(userCreate.getInvitationCode());
                response.setQrCode(userCreate.getQrCode());
                response.setCatalog(userCreate.getCatalog());
                response.setName(userCreate.getName());
                response.setMobile(userCreate.getMobile());
                response.setAvatar(userCreate.getAvatar());
                response.setGender(userCreate.getGender());
                response.setLastLoginTime(DateUtil.format(userCreate.getLastLoginTime(), DATETIME_FORMAT));
                response.setId(userCreate.getId());
            }
            return new Response<>(response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }finally {
            lock.unlock();
        }
    }

    @Operation(summary = "用户分页查询")
    @RequestMapping(value = "/v1/user/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<UserResponse>> query(@RequestBody UserRequest request){
        try {
            PageResponse<UserResponse> response = new PageResponse<>();
            UserQuery userQuery = new UserQuery();
            userQuery.setName(request.getName());
            userQuery.setMobile(request.getMobile());
            userQuery.setCatalog(request.getCatalog());
            if (!this.isEmpty(request.getCaptainUserId())){
                UserTeamQuery userTeamQuery = new UserTeamQuery();
                userTeamQuery.setCaptainUserId(request.getCaptainUserId());
                userTeamQuery.setStatus(DataStatus.Y.getCode());
                List<UserTeam> userTeamList = userTeamService.findAll(userTeamQuery);
                if (this.isEmpty(userTeamList) || userTeamList.isEmpty()){
                    response.setPageNum(request.getPage());
                    response.setPageSize(request.getLimit());
                    response.setTotal(0);
                    response.setSize(0);
                    response.setList(new ArrayList<>());
                    return new Response<>(OK, SUCCESS, response);
                }
                List<Long> userIdList = userTeamList.stream().map(UserTeam::getTeamMemberUserId).collect(Collectors.toList());
                userQuery.setIds(userIdList);
            }
            userQuery.setStatus(DataStatus.Y.getCode());
            Integer total = userService.count(userQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            userQuery.setStart(pager.getOffset());
            userQuery.setLimit(pager.getLimit());
            List<User> users = userService.find(userQuery);

            List<Long> userIdList = users.stream().map(User::getId).collect(Collectors.toList());
            Map<Long,List<Order>> userIdGroupOrderMap = new HashMap<>();
            Map<Long,UserTeam> userIdCaptainUserTeamMap = new HashMap<>();
            Map<Long,User> userMap = new HashMap<>();
            if (!userIdList.isEmpty()){
                userMap = userService.findMapByIds(userIdList);
                OrderQuery orderQuery = new OrderQuery();
                //查询非未支付订单
                List<String> stages = new ArrayList<>();
                stages.add(OrderStage.SUCCESS.getCode());
                stages.add(OrderStage.AUTO_REFUND_PROCESSING.getCode());
                stages.add(OrderStage.AUTO_REFUND_SUCCESS.getCode());
                stages.add(OrderStage.AUTO_REFUND_ABNORMAL.getCode());
                stages.add(OrderStage.REFUND_PROCESSING.getCode());
                stages.add(OrderStage.REFUND_SUCCESS.getCode());
                stages.add(OrderStage.REFUND_ABNORMAL.getCode());
                stages.add(OrderStage.PENDING_REFUND.getCode());
                orderQuery.setStages(stages);
                orderQuery.setCommissionUserIds(userIdList);
                if (!this.isEmpty(request.getMinCreateTime())){
                    orderQuery.setMinPayTime(DateUtil.parse(request.getMinCreateTime(),DATETIME_FORMAT));
                }
                if (!this.isEmpty(request.getMaxCreateTime())){
                    orderQuery.setMaxPayTime(DateUtil.parse(request.getMaxCreateTime(),DATETIME_FORMAT));
                }
                orderQuery.setStatus(DataStatus.Y.getCode());
                List<Order> orderList = orderService.findAll(orderQuery);
                for (Order order : orderList) {
                    if (!userIdGroupOrderMap.containsKey(order.getCommissionUserId())){
                        userIdGroupOrderMap.put(order.getCommissionUserId(),new ArrayList<>());
                    }
                    userIdGroupOrderMap.get(order.getCommissionUserId()).add(order);
                }

                UserTeamQuery userTeamQuery = new UserTeamQuery();
                userTeamQuery.setTeamMemberUserIds(userIdList);
                if (!this.isEmpty(request.getMinCreateTime())){
                    userTeamQuery.setMinCreateTime(DateUtil.parse(request.getMinCreateTime(),DATETIME_FORMAT));
                }
                if (!this.isEmpty(request.getMaxCreateTime())){
                    userTeamQuery.setMaxCreateTime(DateUtil.parse(request.getMaxCreateTime(),DATETIME_FORMAT));
                }
                userTeamQuery.setStatus(DataStatus.Y.getCode());
                List<UserTeam> captainUserTeamList = userTeamService.findAll(userTeamQuery);
                for (UserTeam userTeam : captainUserTeamList) {
                    userIdCaptainUserTeamMap.put(userTeam.getTeamMemberUserId(),userTeam);
                }
            }

            List<UserResponse> userResponses = new ArrayList<>();
            for (User user : users) {
                UserResponse userResponse = new UserResponse(user);
                if (userIdGroupOrderMap.containsKey(user.getId())) {
                    List<Order> orders = userIdGroupOrderMap.get(user.getId());
                    userResponse.setOrderCount(orders.size());
                    userResponse.setOrderAmount(orders.stream().map(Order::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    userResponse.setRefundAmount(orders.stream().map(Order::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                if (userIdCaptainUserTeamMap.containsKey(user.getId()) && userMap.containsKey(userIdCaptainUserTeamMap.get(user.getId()).getCaptainUserId())) {
                    userResponse.setCaptainUserName(userMap.get(userIdCaptainUserTeamMap.get(user.getId()).getCaptainUserId()).getName());
                }
                userResponses.add(userResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(userResponses.size());
            response.setList(userResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    @Operation(summary = "用户分佣分页查询")
    @RequestMapping(value = "/v1/user/commission/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<UserResponse>> queryCommission(@RequestBody UserRequest request){
        try {
            PageResponse<UserResponse> response = new PageResponse<>();
            UserQuery userQuery = new UserQuery();
            userQuery.setName(request.getName());
            userQuery.setMobile(request.getMobile());
            userQuery.setCatalog(request.getCatalog());
            if (!this.isEmpty(request.getCaptainUserId())){
                UserTeamQuery userTeamQuery = new UserTeamQuery();
                userTeamQuery.setCaptainUserId(request.getCaptainUserId());
                userTeamQuery.setStatus(DataStatus.Y.getCode());
                List<UserTeam> userTeamList = userTeamService.findAll(userTeamQuery);
                if (this.isEmpty(userTeamList) || userTeamList.isEmpty()){
                    response.setPageNum(request.getPage());
                    response.setPageSize(request.getLimit());
                    response.setTotal(0);
                    response.setSize(0);
                    response.setList(new ArrayList<>());
                    return new Response<>(OK, SUCCESS, response);
                }
                List<Long> userIdList = userTeamList.stream().map(UserTeam::getTeamMemberUserId).collect(Collectors.toList());
                userQuery.setIds(userIdList);
            }
            userQuery.setStatus(DataStatus.Y.getCode());
            Integer total = userService.count(userQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            userQuery.setStart(pager.getOffset());
            userQuery.setLimit(pager.getLimit());
            List<User> users = userService.find(userQuery);

            List<Long> userIdList = users.stream().map(User::getId).collect(Collectors.toList());
            Map<Long,List<UserTeam>> userIdGroupUserTeamMap = new HashMap<>();
            Map<Long,UserTeam> userIdCaptainUserTeamMap = new HashMap<>();
            Map<Long,User> userMap = new HashMap<>();
            Map<Long,List<UserCommissionFlow>> userIdGroupUserCommissionFlowMap = new HashMap<>();
            if (!userIdList.isEmpty()){
                List<Long> teamUserIds = new ArrayList<>();
                UserTeamQuery userTeamQuery = new UserTeamQuery();
                userTeamQuery.setCaptainUserIds(userIdList);
                if (!this.isEmpty(request.getMinCreateTime())){
                    userTeamQuery.setMinCreateTime(DateUtil.parse(request.getMinCreateTime(),DATE_FORMAT));
                }
                if (!this.isEmpty(request.getMaxCreateTime())){
                    userTeamQuery.setMaxCreateTime(DateUtil.parse(request.getMaxCreateTime(),DATE_FORMAT));
                }
                userTeamQuery.setStatus(DataStatus.Y.getCode());
                List<UserTeam> userTeamList = userTeamService.findAll(userTeamQuery);
                for (UserTeam userTeam : userTeamList) {
                    if (!userIdGroupUserTeamMap.containsKey(userTeam.getCaptainUserId())){
                        userIdGroupUserTeamMap.put(userTeam.getCaptainUserId(),new ArrayList<>());
                    }
                    userIdGroupUserTeamMap.get(userTeam.getCaptainUserId()).add(userTeam);
                    teamUserIds.add(userTeam.getCaptainUserId());
                }

                userTeamQuery = new UserTeamQuery();
                userTeamQuery.setTeamMemberUserIds(userIdList);
                if (!this.isEmpty(request.getMinCreateTime())){
                    userTeamQuery.setMinCreateTime(DateUtil.parse(request.getMinCreateTime(),DATE_FORMAT));
                }
                if (!this.isEmpty(request.getMaxCreateTime())){
                    userTeamQuery.setMaxCreateTime(DateUtil.parse(request.getMaxCreateTime(),DATE_FORMAT));
                }
                userTeamQuery.setStatus(DataStatus.Y.getCode());
                List<UserTeam> captainUserTeamList = userTeamService.findAll(userTeamQuery);
                for (UserTeam userTeam : captainUserTeamList) {
                    userIdCaptainUserTeamMap.put(userTeam.getTeamMemberUserId(),userTeam);
                    teamUserIds.add(userTeam.getTeamMemberUserId());
                }

                if (!teamUserIds.isEmpty()) {
                    userMap = userService.findMapByIds(userIdList);
                }

                UserCommissionFlowQuery userCommissionFlowQuery = new UserCommissionFlowQuery();
                userCommissionFlowQuery.setUserIds(userIdList);
                if (!this.isEmpty(request.getMinCreateTime())){
                    userCommissionFlowQuery.setMinTransactionTime(DateUtil.parse(request.getMinCreateTime(),DATE_FORMAT));
                }
                if (!this.isEmpty(request.getMaxCreateTime())){
                    userCommissionFlowQuery.setMaxTransactionTime(DateUtil.parse(request.getMaxCreateTime(),DATE_FORMAT));
                }
                userCommissionFlowQuery.setStatus(DataStatus.Y.getCode());
                List<UserCommissionFlow> userCommissionFlowList = userCommissionFlowService.findAll(userCommissionFlowQuery);
                userIdGroupUserCommissionFlowMap = userCommissionFlowList.stream().collect(Collectors.groupingBy(UserCommissionFlow::getUserId));
            }

            List<UserResponse> userResponses = new ArrayList<>();
            for (User user : users) {
                UserResponse userResponse = new UserResponse(user);
                //赋值默认值
                userResponse.setCommission(BigDecimal.ZERO);
                userResponse.setCommissionOrderCount(0);
                userResponse.setUnsettledCommission(BigDecimal.ZERO);
                userResponse.setUnsettledOrderCount(0);
                userResponse.setTransferCommission(BigDecimal.ZERO);
                userResponse.setLastTransferTime("-");
                userResponse.setCaptainUserName("-");
                userResponse.setTeamCount(0);

                if (userIdGroupUserCommissionFlowMap.containsKey(user.getId())) {
                    List<UserCommissionFlow> userCommissionFlows = userIdGroupUserCommissionFlowMap.get(user.getId());
                    //累计分佣(除了提现记录累加)
                    BigDecimal commission = BigDecimal.ZERO;
                    Set<Long> commissionOrderIds = new HashSet<>();
                    for (UserCommissionFlow userCommissionFlow : userCommissionFlows) {
                        if (UserCommissionFlowCatalog.C1001.getCode().equals(userCommissionFlow.getCatalog())){
                            continue;
                        }
                        commission = commission.add(userCommissionFlow.getFlowAmount());
                        commissionOrderIds.add(userCommissionFlow.getOrderId());
                    }
                    //倒序排序
                    //未结算佣金(元)
                    BigDecimal unsettledCommission = BigDecimal.ZERO;
                    Set<Long> unsettledOrderIds = new HashSet<>();
                        userCommissionFlows.sort(Comparator.comparing(UserCommissionFlow::getId).reversed());
                    for (UserCommissionFlow userCommissionFlow : userCommissionFlows) {
                        //判断当前流水是否为提现记录
                        if (UserCommissionFlowCatalog.C1001.getCode().equals(userCommissionFlow.getCatalog())
                                && UserCommissionFlowIncome.C1.getCode().equals(userCommissionFlow.getIncome())) {
                            break;
                        }
                        unsettledCommission = unsettledCommission.add(userCommissionFlow.getFlowAmount());
                        unsettledOrderIds.add(userCommissionFlow.getOrderId());
                    }
                    //已提现金额
                    BigDecimal transferCommission = BigDecimal.ZERO;
                    for (UserCommissionFlow userCommissionFlow : userCommissionFlows) {
                        if (UserCommissionFlowCatalog.C1001.getCode().equals(userCommissionFlow.getCatalog())
                                && UserCommissionFlowIncome.C1.getCode().equals(userCommissionFlow.getIncome())) {
                            transferCommission = transferCommission.add(userCommissionFlow.getAmount());
                        }
                    }
                    //上次提现时间
                    Date lastTransferTime = null;
                    for (UserCommissionFlow userCommissionFlow : userCommissionFlows) {
                        if (UserCommissionFlowCatalog.C1001.getCode().equals(userCommissionFlow.getCatalog())
                                && UserCommissionFlowIncome.C1.getCode().equals(userCommissionFlow.getIncome())) {
                            lastTransferTime = userCommissionFlow.getTransactionTime();
                            break;
                        }
                    }
                    if (!this.isEmpty(lastTransferTime)) {
                        userResponse.setLastTransferTime(DateUtil.format(lastTransferTime, DATE_FORMAT));
                    }

                    userResponse.setCommission(commission);
                    userResponse.setCommissionOrderCount(commissionOrderIds.size());
                    userResponse.setUnsettledCommission(unsettledCommission);
                    userResponse.setUnsettledOrderCount(unsettledOrderIds.size());
                    userResponse.setTransferCommission(transferCommission);
                }

                //团队人数
                if (userIdGroupUserTeamMap.containsKey(user.getId())) {
                    userResponse.setTeamCount(userIdGroupUserTeamMap.get(user.getId()).size());
                }

                //队长名称
                if (userIdCaptainUserTeamMap.containsKey(user.getId()) && userMap.containsKey(userIdCaptainUserTeamMap.get(user.getId()).getCaptainUserId())) {
                    userResponse.setCaptainUserName(userMap.get(userIdCaptainUserTeamMap.get(user.getId()).getCaptainUserId()).getName());
                }
                userResponses.add(userResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(userResponses.size());
            response.setList(userResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "用户分佣统计(表格右上角统计)")
    @RequestMapping(value = "/v1/user/commission/report/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<UserResponse> queryReportCommission(@RequestBody UserRequest request){
        try {
            UserResponse response = new UserResponse();
            response.setSumCommission(BigDecimal.ZERO);

            UserQuery userQuery = new UserQuery();
            userQuery.setName(request.getName());
            userQuery.setMobile(request.getMobile());
            userQuery.setCatalog(request.getCatalog());
            if (!this.isEmpty(request.getCaptainUserId())){
                UserTeamQuery userTeamQuery = new UserTeamQuery();
                userTeamQuery.setCaptainUserId(request.getCaptainUserId());
                userTeamQuery.setStatus(DataStatus.Y.getCode());
                List<UserTeam> userTeamList = userTeamService.findAll(userTeamQuery);
                if (this.isEmpty(userTeamList) || userTeamList.isEmpty()){
                    return new Response<>(OK, SUCCESS, response);
                }
                List<Long> userIdList = userTeamList.stream().map(UserTeam::getTeamMemberUserId).collect(Collectors.toList());
                userQuery.setIds(userIdList);
            }
            userQuery.setStatus(DataStatus.Y.getCode());
            List<User> users = userService.findAll(userQuery);
            if (this.isEmpty(users) || users.isEmpty()){
                return new Response<>(OK, SUCCESS, response);
            }

            List<Long> userIdList = users.stream().map(User::getId).collect(Collectors.toList());
            UserCommissionFlowQuery userCommissionFlowQuery = new UserCommissionFlowQuery();
            userCommissionFlowQuery.setUserIds(userIdList);
            if (!this.isEmpty(request.getMinCreateTime())){
                userCommissionFlowQuery.setMinTransactionTime(DateUtil.parse(request.getMinCreateTime(),DATE_FORMAT));
            }
            if (!this.isEmpty(request.getMaxCreateTime())){
                userCommissionFlowQuery.setMaxTransactionTime(DateUtil.parse(request.getMaxCreateTime(),DATE_FORMAT));
            }
            userCommissionFlowQuery.setStatus(DataStatus.Y.getCode());
            List<UserCommissionFlow> userCommissionFlowList = userCommissionFlowService.findAll(userCommissionFlowQuery);

            BigDecimal sumCommission = BigDecimal.ZERO;
            for (UserCommissionFlow userCommissionFlow : userCommissionFlowList) {
                if (UserCommissionFlowCatalog.C1001.getCode().equals(userCommissionFlow.getCatalog())){
                    continue;
                }
                sumCommission = sumCommission.add(userCommissionFlow.getFlowAmount());
            }
            response.setSumCommission(sumCommission);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "用户分佣导出")
    @RequestMapping(value = "/v1/user/commission/export", method = RequestMethod.POST)
    @ResponseBody
    @Token
    public Response<String> exportCommission(@RequestBody UserRequest request){
        // 封装导出参数
        UserExport userExport = new UserExport();
        BeanUtils.copyProperties(request, userExport);

        // 放入到导出的缓存队列中
        String code = LogSequence.get();
        userExport.setCode(code);
        userExport.setType(ExportType.C0.getCode());
        redisTemplate.opsForList().leftPush(CacheKey.FILE_EXPORT,this.getJSON(userExport));
        // 设置该状态为排队中
        redisTemplate.opsForValue().set(CacheKey.FILE_DOWNLOAD_URL + code, ExportStage.WAIT.getCode(), 1, TimeUnit.HOURS);
        return new Response<>(code);
    }

    @Operation(summary = "修改用户类型")
    @RequestMapping(value = "/v1/user/catalog/modify", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modifyUserCatalog(@RequestBody UserRequest request) {
        try {
            Date serverTime = this.getServerTime();

            // 参数验证
            UserValidator validator = new UserValidator();
            if (!validator
                    .onId(request.getId())
                    .onCatalog(request.getCatalog())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            // 验证用户是否存在
            User existingUser = userService.findById(request.getId());
            if (existingUser == null) {
                return new Response<>(ERROR, "用户不存在");
            }

            // 验证用户类型值是否有效
            UserCatalog targetCatalog = UserCatalog.get(request.getCatalog());
            if (targetCatalog == null) {
                return new Response<>(ERROR, "无效的用户类型");
            }

            // 检查是否需要更新
            if (request.getCatalog().equals(existingUser.getCatalog())) {
                return new Response<>(OK, "用户类型无需更改，当前已是：" + targetCatalog.getName());
            }

            // 更新用户类型
            User user = new User();
            user.setId(request.getId());
            user.setCatalog(request.getCatalog());
            user.setModifyTime(serverTime);

            userService.modifyById(user);

            return new Response<>(OK, "用户类型修改成功，当前类型：" + targetCatalog.getName());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "批量修改用户类型")
    @RequestMapping(value = "/v1/user/catalog/batch/modify", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> batchModifyUserCatalog(@RequestBody UserRequest request) {
        try {
            Date serverTime = this.getServerTime();

            // 参数验证
            UserValidator validator = new UserValidator();
            if (!validator
                    .onIds(request.getIds())
                    .onCatalog(request.getCatalog())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            // 验证用户类型值是否有效
            UserCatalog targetCatalog = UserCatalog.get(request.getCatalog());
            if (targetCatalog == null) {
                return new Response<>(ERROR, "无效的用户类型");
            }

            // 查询所有用户
            List<User> users = userService.findByIds(request.getIds());
            if (users.isEmpty()) {
                return new Response<>(ERROR, "未找到任何用户");
            }

            // 验证用户状态
            List<String> invalidUsers = new ArrayList<>();
            for (User user : users) {
                if (!DataStatus.Y.getCode().equals(user.getStatus())) {
                    invalidUsers.add(user.getName() + "(" + user.getMobile() + ")");
                }
            }

            if (!invalidUsers.isEmpty()) {
                return new Response<>(ERROR, "以下用户状态无效，无法修改类型：" + String.join(", ", invalidUsers));
            }

            // 批量更新用户类型
            List<User> updateUsers = new ArrayList<>();
            for (Long userId : request.getIds()) {
                User user = new User();
                user.setId(userId);
                user.setCatalog(request.getCatalog());
                user.setModifyTime(serverTime);
                updateUsers.add(user);
            }

            userService.modifyBatch(updateUsers);

            return new Response<>(OK, "批量修改成功，共修改 " + updateUsers.size() + " 个用户，类型：" + targetCatalog.getName());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "查询自身信息")
    @RequestMapping(value = "/v1/user/my/query", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<UserResponse> queryMy() {
        try {
            // 获取当前用户token信息
            UserToken userToken = this.getUserToken();
            if (userToken == null || userToken.getId() == null) {
                return new Response<>(ERROR, "用户未登录");
            }

            // 查询用户信息
            User user = userService.findById(userToken.getId());
            if (user == null) {
                return new Response<>(ERROR, "用户不存在");
            }

            // 验证用户状态
            if (!DataStatus.Y.getCode().equals(user.getStatus())) {
                return new Response<>(ERROR, "用户状态异常");
            }

            UserResponse userResponse = new UserResponse(user);
            return new Response<>(OK, SUCCESS, userResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "修改自身信息")
    @RequestMapping(value = "/v1/user/my/modify", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modifyMy(@RequestBody UserRequest request) {
        try {
            Date serverTime = this.getServerTime();

            // 获取当前用户token信息
            UserToken userToken = this.getUserToken();
            if (userToken == null || userToken.getId() == null) {
                return new Response<>(ERROR, "用户未登录");
            }

            // 参数验证
            UserValidator validator = new UserValidator();
            if (!validator
                    .onName(request.getName())
                    .onMobile(request.getMobile())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            // 查询当前用户信息
            User currentUser = userService.findById(userToken.getId());
            if (currentUser == null) {
                return new Response<>(ERROR, "用户不存在");
            }

            // 验证用户状态
            if (!DataStatus.Y.getCode().equals(currentUser.getStatus())) {
                return new Response<>(ERROR, "用户状态异常");
            }

            // 检查手机号是否被其他用户使用
            if (!request.getMobile().equals(currentUser.getMobile())) {
                UserQuery userQuery = new UserQuery();
                userQuery.setMobile(request.getMobile());
                userQuery.setStatus(DataStatus.Y.getCode());
                List<User> existingUsers = userService.findAll(userQuery);

                // 过滤掉当前用户自己
                existingUsers = existingUsers.stream()
                    .filter(user -> !user.getId().equals(userToken.getId()))
                    .collect(Collectors.toList());

                if (!existingUsers.isEmpty()) {
                    return new Response<>(ERROR, "手机号已被其他用户使用");
                }
            }

            // 更新用户信息
            User updateUser = new User();
            updateUser.setId(userToken.getId());
            updateUser.setName(request.getName());
            updateUser.setMobile(request.getMobile());

            // 头像和性别为可选字段
            if (!isEmpty(request.getAvatar())) {
                updateUser.setAvatar(request.getAvatar());
            }
            if (!isEmpty(request.getGender())) {
                updateUser.setGender(request.getGender());
            }

            updateUser.setModifyTime(serverTime);

            userService.modifyById(updateUser);

            return new Response<>(OK, "个人信息修改成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "查询自身佣金统计信息")
    @RequestMapping(value = "/v1/user/my/commission/stats", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<UserCommissionStatsResponse> getMyCommissionStats() {
        try {
            // 获取当前用户token信息
            UserToken userToken = this.getUserToken();
            if (userToken == null || userToken.getId() == null) {
                return new Response<>(ERROR, "用户未登录");
            }

            Long userId = userToken.getId();

            // 验证用户是否存在且状态正常
            User user = userService.findById(userId);
            if (user == null) {
                return new Response<>(ERROR, "用户不存在");
            }

            if (!DataStatus.Y.getCode().equals(user.getStatus())) {
                return new Response<>(ERROR, "用户状态异常");
            }

            UserCommissionStatsResponse response = new UserCommissionStatsResponse();

            // 1. 查询累计佣金 - 从订单表中统计所有已支付订单的佣金总和
            OrderQuery orderQuery = new OrderQuery();
            List<String> stages = new ArrayList<>();
            stages.add(OrderStage.SUCCESS.getCode());
            stages.add(OrderStage.AUTO_REFUND_PROCESSING.getCode());
            stages.add(OrderStage.AUTO_REFUND_SUCCESS.getCode());
            stages.add(OrderStage.AUTO_REFUND_ABNORMAL.getCode());
            stages.add(OrderStage.REFUND_PROCESSING.getCode());
            stages.add(OrderStage.REFUND_SUCCESS.getCode());
            stages.add(OrderStage.REFUND_ABNORMAL.getCode());
            stages.add(OrderStage.PENDING_REFUND.getCode());
            orderQuery.setStages(stages);
            orderQuery.setCommissionUserId(userId);
            orderQuery.setStatus(DataStatus.Y.getCode());
            List<Order> orders = orderService.findAll(orderQuery);

            // 计算累计佣金和分销订单数量
            BigDecimal totalCommission = orders.stream()
                .map(Order::getSumCommission)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            response.setTotalCommission(totalCommission);
            response.setCommissionOrderCount(orders.size());

            // 2. 查询团员人数 - 查询以当前用户为队长的团队成员数量
            UserTeamQuery userTeamQuery = new UserTeamQuery();
            userTeamQuery.setCaptainUserId(userId);
            userTeamQuery.setStatus(DataStatus.Y.getCode());
            List<UserTeam> teamMembers = userTeamService.findAll(userTeamQuery);
            response.setTeamMemberCount(teamMembers.size());

            // 3. 查询可提现佣金 - 从用户佣金表中获取可用余额
            UserCommissionQuery commissionQuery = new UserCommissionQuery();
            commissionQuery.setUserId(userId);
            commissionQuery.setStatus(DataStatus.Y.getCode());
            List<UserCommission> commissions = userCommissionService.findAll(commissionQuery);

            BigDecimal availableCommission = BigDecimal.ZERO;
            if (!commissions.isEmpty()) {
                UserCommission userCommission = commissions.get(0);
                if (userCommission.getAmount() != null) {
                    availableCommission = userCommission.getAmount();
                }
            }
            response.setAvailableCommission(availableCommission);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    private String getUserToken(User user) {
        Date date = this.getServerTime();
        JsonWebToken jsonWebToken = new JsonWebToken();
        jsonWebToken.setId(UUID.randomUUID().toString());
        jsonWebToken.setIssuedAt(date);
        jsonWebToken.setExpiration(DateUtil.add(date, com.api.constant.App.TOKEN_EXPIRE_UNIT, com.api.constant.App.TOKEN_EXPIRE_NUMBER));
        jsonWebToken.setIssuer(String.valueOf(user.getId()));
        jsonWebToken.setSubject(user.getOpenId());
        // 用户信息
        jsonWebToken.setUserId(user.getId());
        jsonWebToken.setUsername(user.getOpenId());
        jsonWebToken.setType(TokenType.S.name());
        String token = EncryptUtil.createJwt(jsonWebToken);
        this.redisTemplate.opsForValue().set(CacheKey.USER_TOKEN + TokenType.S.name() + Device.MD.name() + user.getId(), token);
        return token;
    }
}
