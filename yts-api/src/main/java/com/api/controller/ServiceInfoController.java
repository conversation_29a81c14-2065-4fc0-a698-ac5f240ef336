package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.PageResponse;
import com.api.bean.ServiceInfoRequest;
import com.api.bean.ServiceInfoResponse;
import com.api.config.Token;
import com.api.validator.ServiceInfoValidator;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.ServiceInfo;
import com.domain.complex.ServiceInfoQuery;
import com.service.ServiceInfoService;


@Tag(name = "服务信息表")
@RestController
public class ServiceInfoController extends BaseController {
    @Autowired
    private ServiceInfoService serviceInfoService;

    @Operation(summary = "服务信息分页查询")
    @RequestMapping(value = "/v1/service/info/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<ServiceInfoResponse>> query(@RequestBody ServiceInfoRequest request){
        try {
            PageResponse<ServiceInfoResponse> response = new PageResponse<>();
            ServiceInfoQuery serviceInfoQuery = new ServiceInfoQuery();
            serviceInfoQuery.setName(request.getName());
            serviceInfoQuery.setStatus(DataStatus.Y.getCode());
            Integer total = serviceInfoService.count(serviceInfoQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            serviceInfoQuery.setStart(pager.getOffset());
            serviceInfoQuery.setLimit(pager.getLimit());
            List<ServiceInfo> serviceInfos = serviceInfoService.find(serviceInfoQuery);

            List<ServiceInfoResponse> serviceInfoResponses = new ArrayList<>();

            for (ServiceInfo serviceInfo : serviceInfos) {
                ServiceInfoResponse serviceInfoResponse = new ServiceInfoResponse(serviceInfo);
                serviceInfoResponses.add(serviceInfoResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(serviceInfoResponses.size());
            response.setList(serviceInfoResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "服务信息查询")
    @RequestMapping(value = "/v1/service/info/all/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<List<ServiceInfoResponse>> queryAll(@RequestBody ServiceInfoRequest request){
        try {
            ServiceInfoQuery serviceInfoQuery = new ServiceInfoQuery();
            serviceInfoQuery.setName(request.getName());
            serviceInfoQuery.setStatus(DataStatus.Y.getCode());
            List<ServiceInfo> serviceInfos = serviceInfoService.findAll(serviceInfoQuery);

            List<ServiceInfoResponse> serviceInfoResponses = new ArrayList<>();

            for (ServiceInfo serviceInfo : serviceInfos) {
                ServiceInfoResponse serviceInfoResponse = new ServiceInfoResponse(serviceInfo);
                serviceInfoResponses.add(serviceInfoResponse);
            }

            return new Response<>(OK, SUCCESS, serviceInfoResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "服务信息id查询")
    @RequestMapping(value = "/v1/service/info/id/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<ServiceInfoResponse> queryById(@RequestBody ServiceInfoRequest request){
        try {
            ServiceInfoValidator validator = new ServiceInfoValidator();
            if (!validator
                    .onId(request.getId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            ServiceInfo serviceInfo = serviceInfoService.findById(request.getId());

            ServiceInfoResponse serviceInfoResponse = new ServiceInfoResponse(serviceInfo);

            return new Response<>(OK, SUCCESS, serviceInfoResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    @Operation(summary = "服务信息新增")
    @RequestMapping(value = "/v1/service/info/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> create(@RequestBody ServiceInfoRequest request){
        try {
            Date serverTime = this.getServerTime();
            ServiceInfoValidator validator = new ServiceInfoValidator();
            if (!validator
                    .onName(request.getName())
                    .onCoverUrl(request.getCoverUrl())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            ServiceInfo serviceInfo = new ServiceInfo();
            serviceInfo.setName(request.getName());
            serviceInfo.setCoverUrl(request.getCoverUrl());
            serviceInfo.setComment(request.getComment());
            serviceInfo.setDetailImgUrl(request.getDetailImgUrl());
            serviceInfo.setSort(request.getSort() != null ? request.getSort() : 0);
            serviceInfo.setStatus(DataStatus.Y.getCode());
            serviceInfo.setModifyTime(serverTime);
            serviceInfo.setCreateTime(serverTime);
            serviceInfoService.create(serviceInfo);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "服务信息修改")
    @RequestMapping(value = "/v1/service/info/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modify(@RequestBody ServiceInfoRequest request){
        try {
            Date serverTime = this.getServerTime();
            ServiceInfoValidator validator = new ServiceInfoValidator();
            if (!validator
                    .onId(request.getId())
                    .onName(request.getName())
                    .onCoverUrl(request.getCoverUrl())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            ServiceInfo serviceInfo = new ServiceInfo();
            serviceInfo.setId(request.getId());
            serviceInfo.setName(request.getName());
            serviceInfo.setCoverUrl(request.getCoverUrl());
            serviceInfo.setComment(request.getComment());
            serviceInfo.setDetailImgUrl(request.getDetailImgUrl());
            serviceInfo.setSort(request.getSort() != null ? request.getSort() : 0);
            serviceInfo.setModifyTime(serverTime);
            serviceInfoService.modifyById(serviceInfo);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "服务信息删除")
    @RequestMapping(value = "/v1/service/info/remove",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> remove(@RequestBody ServiceInfoRequest request){
        try {
            Date serverTime = this.getServerTime();
            ServiceInfoValidator validator = new ServiceInfoValidator();
            if (!validator
                    .onId(request.getId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            ServiceInfo serviceInfo = new ServiceInfo();
            serviceInfo.setId(request.getId());
            serviceInfo.setStatus(DataStatus.N.getCode());
            serviceInfo.setModifyTime(serverTime);
            serviceInfoService.modifyById(serviceInfo);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
