package com.api.controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.OrderDetailResponse;
import com.api.bean.OrderProductDetailResponse;
import com.api.bean.OrderProductRequest;
import com.api.bean.OrderProductResponse;
import com.api.bean.OrderRequest;
import com.api.bean.OrderResponse;
import com.api.bean.PageResponse;
import com.api.config.Token;
import com.api.validator.OrderValidator;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.bean.SkuStockException;
import com.common.bean.UserCommissionDTO;
import com.common.bean.UserToken;
import com.common.constant.App;
import com.common.constant.CacheKey;
import com.common.constant.DataStage;
import com.common.constant.DataStatus;
import com.common.constant.GoodsCatalog;
import com.common.constant.MyOrderType;
import com.common.constant.OrderCatalog;
import com.common.constant.OrderDeliveryCatalog;
import com.common.constant.OrderOperateStage;
import com.common.constant.OrderSelectCatalog;
import com.common.constant.OrderStage;
import com.common.constant.PayType;
import com.common.constant.UserCatalog;
import com.common.constant.UserCommissionFlowCatalog;
import com.common.constant.WxTradeState;
import com.common.util.DateUtil;
import com.domain.Course;
import com.domain.Goods;
import com.domain.GoodsSpecificationSku;
import com.domain.GoodsType;
import com.domain.Order;
import com.domain.OrderCodeFlow;
import com.domain.OrderProduct;
import com.domain.SalesTeacher;
import com.domain.User;
import com.domain.UserAddress;
import com.domain.UserTeam;
import com.domain.complex.OrderCodeFlowQuery;
import com.domain.complex.OrderProductQuery;
import com.domain.complex.OrderQuery;
import com.domain.complex.UserTeamQuery;
import com.service.CourseService;
import com.service.GoodsService;
import com.service.GoodsSpecificationSkuService;
import com.service.GoodsSpecificationTypeService;
import com.service.GoodsSpecificationValueService;
import com.service.GoodsTypeService;
import com.service.OrderCodeFlowService;
import com.service.OrderProductService;
import com.service.OrderService;
import com.service.SalesTeacherService;
import com.service.UserAddressService;
import com.service.UserService;
import com.service.UserTeamService;


@Tag(name = "订单表")
@RestController
public class OrderController extends BaseController {
    @Autowired
    private OrderService orderService;
    @Autowired
    private CourseService courseService;
    @Autowired
    private GoodsService goodsService;
    @Autowired
    private UserTeamService userTeamService;
    @Autowired
    private UserService userService;
    @Autowired
    private GoodsSpecificationTypeService goodsSpecificationTypeService;
    @Autowired
    private GoodsSpecificationValueService goodsSpecificationValueService;
    @Autowired
    private GoodsSpecificationSkuService goodsSpecificationSkuService;
    @Autowired
    private GoodsTypeService goodsTypeService;
    @Autowired
    private OrderProductService orderProductService;
    @Autowired
    private SalesTeacherService salesTeacherService;
    @Autowired
    private OrderCodeFlowService orderCodeFlowService;
    @Autowired
    private UserAddressService userAddressService;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;


    @Operation(summary = "订单分页查询")
    @RequestMapping(value = "/v1/order/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<OrderResponse>> query(@RequestBody OrderRequest request){
        try {
            PageResponse<OrderResponse> response = new PageResponse<>();
            OrderQuery orderQuery = new OrderQuery();
            orderQuery.setStage(request.getStage());
            orderQuery.setStages(request.getStages());
            orderQuery.setUserName(request.getUserName());
            orderQuery.setMobile(request.getMobile());
            orderQuery.setUserCatalog(request.getUserCatalog());
            orderQuery.setGoodsCatalog(request.getGoodsCatalog());
            orderQuery.setOperateStage(request.getOperateStage());
            orderQuery.setStatus(DataStatus.Y.getCode());
            Integer total = orderService.count(orderQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            orderQuery.setStart(pager.getOffset());
            orderQuery.setLimit(pager.getLimit());
            List<Order> orders = orderService.find(orderQuery);
            if (orders.isEmpty()){
                response.setPageNum(request.getPage());
                response.setPageSize(request.getLimit());
                response.setTotal(0);
                response.setSize(0);
                response.setList(new ArrayList<>());
                return new Response<>(OK, SUCCESS, response);
            }

            Map<Long,List<OrderProduct>> orderIdGroupOrderProductMap = new HashMap<>();
            List<Long> userIds = orders.stream().map(Order::getUserId).collect(Collectors.toList());
            Map<Long, User> userMap = userService.findMapByIds(userIds);
            List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
            OrderProductQuery orderProductQuery = new OrderProductQuery();
            orderProductQuery.setStatus(DataStatus.Y.getCode());
            orderProductQuery.setOrderIds(orderIds);
            List<OrderProduct> orderProducts = orderProductService.findAll(orderProductQuery);
            for (OrderProduct orderProduct : orderProducts) {
                if (!orderIdGroupOrderProductMap.containsKey(orderProduct.getOrderId())){
                    orderIdGroupOrderProductMap.put(orderProduct.getOrderId(),new ArrayList<>());
                }
                orderIdGroupOrderProductMap.get(orderProduct.getOrderId()).add(orderProduct);
            }
            //团长map
            Map<Long,User> commissionUserMap = new HashMap<>();
            List<Long> commissionUserIds = orders.stream().map(Order::getCommissionUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (!commissionUserIds.isEmpty()){
                commissionUserMap = userService.findMapByIds(commissionUserIds);
            }
            //销售老师map
            Map<Long, SalesTeacher> salesTeacherMap = new HashMap<>();
            List<Long> salesTeacherIds = orders.stream().map(Order::getSalesTeacherId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (!salesTeacherIds.isEmpty()){
                salesTeacherMap = salesTeacherService.findMapByIds(salesTeacherIds);
            }

            List<OrderResponse> orderResponses = new ArrayList<>();
            for (Order order : orders) {
                OrderResponse orderResponse = new OrderResponse(order);
                if (userMap.containsKey(order.getUserId())){
                    orderResponse.setUserName(userMap.get(order.getUserId()).getName());
                    orderResponse.setMobile(userMap.get(order.getUserId()).getMobile());
                }
                List<String> productNames = new ArrayList<>();
                if (orderIdGroupOrderProductMap.containsKey(order.getId())){
                    List<OrderProduct> orderProductList = orderIdGroupOrderProductMap.get(order.getId());
                    for (OrderProduct orderProduct : orderProductList) {
                        if (OrderCatalog.C0.getCode().equals(orderProduct.getProductCatalog())){
                            productNames.add(orderProduct.getProductName());
                        }else if (OrderCatalog.C1.getCode().equals(orderProduct.getProductCatalog())){
                            String productName = orderProduct.getProductName();
                            //如果产品名称长度为大于7个字符，则截取前7个字符+省略号
                            if (!this.isEmpty(productName) && productName.length() > 7){
                                productName = productName.substring(0, 7) + "...";
                            }
                            String goodsName = productName +
                                    " [" +
                                    orderProduct.getSpecValues() +
                                    "] " +
                                    "x" +
                                    orderProduct.getNumber() +
                                    " ¥" +
                                    orderProduct.getPrice();
                            productNames.add(goodsName);
                        }
                    }
                }
                orderResponse.setProductNames(productNames);
                if (commissionUserMap.containsKey(order.getCommissionUserId())){
                    orderResponse.setCommissionUserName(commissionUserMap.get(order.getCommissionUserId()).getName());
                }
                if (salesTeacherMap.containsKey(order.getSalesTeacherId())){
                    orderResponse.setSalesTeacherName(salesTeacherMap.get(order.getSalesTeacherId()).getName());
                }

                orderResponses.add(orderResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(orderResponses.size());
            response.setList(orderResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "订单统计查询")
    @RequestMapping(value = "/v1/order/report/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<OrderResponse> queryReport(@RequestBody OrderRequest request){
        try {
            OrderResponse response = new OrderResponse();
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalCommission = BigDecimal.ZERO;
            BigDecimal totalNotPayAmount = BigDecimal.ZERO;
            BigDecimal totalRefundAmount = BigDecimal.ZERO;
            response.setTotalAmount(totalAmount);
            response.setTotalCommission(totalCommission);
            response.setTotalNotPayAmount(totalNotPayAmount);
            response.setTotalRefundAmount(totalRefundAmount);

            OrderQuery orderQuery = new OrderQuery();
            orderQuery.setStage(request.getStage());
            orderQuery.setStages(request.getStages());
            orderQuery.setUserName(request.getUserName());
            orderQuery.setMobile(request.getMobile());
            orderQuery.setUserCatalog(request.getUserCatalog());
            orderQuery.setGoodsCatalog(request.getGoodsCatalog());
            orderQuery.setOperateStage(request.getOperateStage());
            orderQuery.setStatus(DataStatus.Y.getCode());
            List<Order> orders = orderService.findAll(orderQuery);
            if (orders.isEmpty()){
                return new Response<>(OK, SUCCESS, response);
            }

            for (Order order : orders) {
                //超时已关闭、已取消的订单不参与统计
                if (OrderStage.CANCEL.getCode().equals(order.getStage()) || OrderStage.CLOSED.getCode().equals(order.getStage())){
                    continue;
                }

                //总金额
                totalAmount = totalAmount.add(order.getAmount());
                //总佣金金额(已支付的)
                if (OrderStage.SUCCESS.getCode().equals(order.getStage())){
                    totalCommission = totalCommission.add(order.getSumCommission());
                    totalCommission = totalCommission.add(order.getSumSalesTeacherCommission());
                }
                //未支付金额
                if (OrderStage.NOTPAY.getCode().equals(order.getStage())){
                    totalNotPayAmount = totalNotPayAmount.add(order.getAmount());
                }
                //退款金额(退款中和退款成功)
                if (OrderStage.AUTO_REFUND_PROCESSING.getCode().equals(order.getStage())
                        || OrderStage.AUTO_REFUND_SUCCESS.getCode().equals(order.getStage())
                        || OrderStage.REFUND_PROCESSING.getCode().equals(order.getStage())
                        || OrderStage.REFUND_SUCCESS.getCode().equals(order.getStage())){
                    totalRefundAmount = totalRefundAmount.add(order.getRefundAmount());
                }
            }

            response.setTotalAmount(totalAmount);
            response.setTotalCommission(totalCommission);
            response.setTotalNotPayAmount(totalNotPayAmount);
            response.setTotalRefundAmount(totalRefundAmount);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "订单详情查询")
    @RequestMapping(value = "/v1/order/id/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<OrderDetailResponse> queryById(@RequestBody OrderRequest request){
        try {
            //判断参数
            if (this.isEmpty(request.getId())){
                return new Response<>(ERROR, "订单编号不能为空");
            }
            Order order = orderService.findById(request.getId());
            if (this.isEmpty(order)){
                return new Response<>(ERROR, "订单不存在");
            }

            // 查询用户信息
            User user = userService.findById(order.getUserId());
            if (this.isEmpty(user)){
                return new Response<>(ERROR, "用户不存在");
            }

            // 查询订单商品明细
            OrderProductQuery orderProductQuery = new OrderProductQuery();
            orderProductQuery.setOrderId(order.getId());
            orderProductQuery.setStatus(DataStatus.Y.getCode());
            List<OrderProduct> orderProducts = orderProductService.findAll(orderProductQuery);

            // 构建订单详情响应
            OrderDetailResponse orderDetailResponse = new OrderDetailResponse();
            orderDetailResponse.setOrderCode(order.getCode());
            orderDetailResponse.setCreateTime(DateUtil.format(order.getCreateTime(), DATETIME_FORMAT));
            orderDetailResponse.setPayTime(DateUtil.format(order.getPayTime(), DATETIME_FORMAT));
            orderDetailResponse.setUserName(user.getName());
            orderDetailResponse.setMobile(user.getMobile());
            orderDetailResponse.setUserType(UserCatalog.getName(user.getCatalog()));
            orderDetailResponse.setPayStatus(OrderStage.getName(order.getStage()));
            orderDetailResponse.setOrderStatus(OrderOperateStage.getName(order.getOperateStage()));
            orderDetailResponse.setTotalAmount(order.getAmount());
            orderDetailResponse.setRefundAmount(order.getRefundAmount());
            orderDetailResponse.setSumCommission(order.getSumCommission());
            orderDetailResponse.setSumSalesTeacherCommission(order.getSumSalesTeacherCommission());

            // 构建商品明细列表
            List<OrderProductDetailResponse> productDetails = new ArrayList<>();
            for (OrderProduct orderProduct : orderProducts) {
                OrderProductDetailResponse productDetail = new OrderProductDetailResponse();
                productDetail.setProductName(orderProduct.getProductName());

                // 设置规格信息
                if (!this.isEmpty(orderProduct.getSpecValues())) {
                    productDetail.setSpecification(orderProduct.getSpecValues());
                } else {
                    productDetail.setSpecification("-");
                }

                // 设置商品类型
                if (!this.isEmpty(orderProduct.getGoodsTypeName())) {
                    productDetail.setProductType(orderProduct.getGoodsTypeName());
                } else {
                    productDetail.setProductType(GoodsCatalog.getName(orderProduct.getGoodsCatalog()));
                }

                productDetail.setUnitPrice(orderProduct.getPrice());
                productDetail.setQuantity(orderProduct.getNumber());
                productDetail.setUnit(orderProduct.getUnit());
                productDetail.setAmount(orderProduct.getAmount());

                // 设置分佣比例
                if (orderProduct.getCommissionRatio() != null) {
                    productDetail.setCommissionRatio(orderProduct.getCommissionRatio() + "%");
                } else {
                    productDetail.setCommissionRatio("0%");
                }
                if (orderProduct.getSalesTeacherCommissionRatio() != null) {
                    productDetail.setSalesTeacherCommissionRatio(orderProduct.getSalesTeacherCommissionRatio() + "%");
                } else {
                    productDetail.setSalesTeacherCommissionRatio("0%");
                }
                productDetail.setCommission(orderProduct.getCommission());
                productDetail.setSalesTeacherCommission(orderProduct.getSalesTeacherCommission());

                productDetails.add(productDetail);
            }

            orderDetailResponse.setProductDetails(productDetails);

            return new Response<>(OK, SUCCESS, orderDetailResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "订单收货操作")
    @RequestMapping(value = "/v1/order/operate/stage/modify", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modifyOperateStage(@RequestBody OrderRequest request) {
        try {
            Date serverTime = this.getServerTime();

            // 参数验证
            OrderValidator validator = new OrderValidator();
            if (!validator
                    .onId(request.getId())
                    .onOperateStage(request.getOperateStage())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            // 验证订单是否存在
            Order existingOrder = orderService.findById(request.getId());
            if (existingOrder == null) {
                return new Response<>(ERROR, "订单不存在");
            }

            // 验证订单状态是否可以操作
            if (!OrderStage.SUCCESS.getCode().equals(existingOrder.getStage())) {
                return new Response<>(ERROR, "只有已支付的订单才能进行收货操作");
            }

            // 验证提货状态值是否有效
            OrderOperateStage targetStage = OrderOperateStage.get(request.getOperateStage());
            if (targetStage == null) {
                return new Response<>(ERROR, "无效的提货状态");
            }

            // 更新订单提货状态
            Order order = new Order();
            order.setId(request.getId());
            order.setOperateStage(request.getOperateStage());
            order.setModifyTime(serverTime);

            orderService.modifyById(order);

            return new Response<>(OK, "订单状态更新成功，当前状态：" + targetStage.getName());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "批量订单收货操作")
    @RequestMapping(value = "/v1/order/operate/stage/batch/modify", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> batchModifyOperateStage(@RequestBody OrderRequest request) {
        try {
            Date serverTime = this.getServerTime();

            // 参数验证
            OrderValidator validator = new OrderValidator();
            if (!validator
                    .onIds(request.getIds())
                    .onOperateStage(request.getOperateStage())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            // 验证提货状态值是否有效
            OrderOperateStage targetStage = OrderOperateStage.get(request.getOperateStage());
            if (targetStage == null) {
                return new Response<>(ERROR, "无效的提货状态");
            }

            // 查询所有订单
            List<Order> orders = orderService.findByIds(request.getIds());
            if (orders.isEmpty()) {
                return new Response<>(ERROR, "未找到任何订单");
            }

            // 验证订单状态
            List<String> invalidOrders = new ArrayList<>();
            for (Order order : orders) {
                if (!OrderStage.SUCCESS.getCode().equals(order.getStage())) {
                    invalidOrders.add(order.getCode());
                }
            }

            if (!invalidOrders.isEmpty()) {
                return new Response<>(ERROR, "以下订单未支付，无法进行收货操作：" + String.join(", ", invalidOrders));
            }

            // 批量更新订单状态
            List<Order> updateOrders = new ArrayList<>();
            for (Long orderId : request.getIds()) {
                Order order = new Order();
                order.setId(orderId);
                order.setOperateStage(request.getOperateStage());
                order.setModifyTime(serverTime);
                updateOrders.add(order);
            }

            orderService.modifyBatch(updateOrders);

            return new Response<>(OK, "批量更新成功，共更新 " + updateOrders.size() + " 个订单，状态：" + targetStage.getName());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "创建订单")
    @RequestMapping(value = "/v1/order/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public synchronized Response<?> create(@RequestBody OrderRequest request){
        try {
            Date serverTime = this.getServerTime();
            UserToken userToken = this.getUserToken();
            User user = userService.findById(userToken.getId());
            if (this.isEmpty(user)){
                return new Response<>(ERROR, "用户不存在");
            }
            if (this.isEmpty(request.getOrderProductRequestList()) || request.getOrderProductRequestList().isEmpty()){
                return new Response<>(ERROR, "请选择商品");
            }
            //课程不下单了，只有商品下单
            request.setOrderCatalog(OrderCatalog.C1.getCode());
            if (this.isEmpty(request.getDeliveryCatalog())){
                return new Response<>(ERROR, "请选择配送方式");
            }
            if (OrderDeliveryCatalog.C0.getCode().equals(request.getDeliveryCatalog()) && this.isEmpty(request.getReservationTime())){
                return new Response<>(ERROR, "请选择预约时间");
            }else if (OrderDeliveryCatalog.C1.getCode().equals(request.getDeliveryCatalog()) && this.isEmpty(request.getUserAddressId())){
                return new Response<>(ERROR, "收货地址不能为空");
            }
            if (this.isEmpty(request.getSelectCatalog())){
                return new Response<>(ERROR, "请选择自选、帮选");
            }
            //查询商品是否有效
            List<Long> productIds = request.getOrderProductRequestList().stream().map(OrderProductRequest::getProductId).collect(Collectors.toList());
            BigDecimal sumAmount = BigDecimal.ZERO;
            BigDecimal sumCommission = BigDecimal.ZERO;
            BigDecimal sumSalesTeacherCommission = BigDecimal.ZERO;
            List<OrderProduct> orderProducts = new ArrayList<>();
            List<GoodsSpecificationSku> goodsSpecificationSkus = new ArrayList<>();
            if (OrderCatalog.C0.getCode().equals(request.getOrderCatalog())){
                //查询课程
                Map<Long, Course> courseMap = courseService.findMapByIds(productIds);
                for (OrderProductRequest orderProductRequest : request.getOrderProductRequestList()) {
                    if (!courseMap.containsKey(orderProductRequest.getProductId())){
                        return new Response<>(ERROR, "课程不存在");
                    }
                    Course course = courseMap.get(orderProductRequest.getProductId());
                    //判断课程是否有效
                    if (!DataStatus.Y.getCode().equals(course.getStatus())){
                        return new Response<>(ERROR, course.getTitle() + "课程无效");
                    }
                    //判断课程是否在售
                    if (!DataStage.Y.getCode().equals(course.getStage())){
                        return new Response<>(ERROR, course.getTitle() + "课程已下架");
                    }
                    //判断价格是否为空
                    if (this.isEmpty(orderProductRequest.getAmount())){
                        return new Response<>(ERROR, course.getTitle() + "课程价格为空");
                    }
                    //判断课程价格与后台价格一致
                    if (course.getPrice().compareTo(orderProductRequest.getAmount()) != 0){
                        return new Response<>(ERROR, course.getTitle() + "课程价格有误");
                    }

                    sumAmount = sumAmount.add(course.getPrice());

                    OrderProduct orderProduct = new OrderProduct();
                    orderProduct.setProductCatalog(OrderCatalog.C0.getCode());
                    orderProduct.setProductId(course.getId());
                    orderProduct.setNumber(1);
                    orderProduct.setAmount(course.getPrice());
                    orderProduct.setProductName(course.getTitle());
                    orderProduct.setImgUrl(course.getImgUrl());
                    orderProduct.setPrice(course.getPrice());
                    orderProduct.setOriginalPrice(course.getOriginalPrice());
                    orderProduct.setCommission(BigDecimal.ZERO);
                    orderProduct.setCommissionRatio(BigDecimal.ZERO);
                    orderProduct.setSalesTeacherCommission(BigDecimal.ZERO);
                    orderProduct.setSalesTeacherCommissionRatio(BigDecimal.ZERO);
                    orderProduct.setStatus(DataStatus.Y.getCode());
                    orderProduct.setCreateTime(serverTime);
                    orderProduct.setModifyTime(serverTime);
                    orderProducts.add(orderProduct);
                }
            }else if (OrderCatalog.C1.getCode().equals(request.getOrderCatalog())){
                //查询商品
                Map<Long, Goods> goodsMap = goodsService.findMapByIds(productIds);
                for (OrderProductRequest orderProductRequest : request.getOrderProductRequestList()) {
                    if (!goodsMap.containsKey(orderProductRequest.getProductId())){
                        return new Response<>(ERROR, "商品不存在");
                    }
                    Goods goods = goodsMap.get(orderProductRequest.getProductId());
                    //判断商品是否有效
                    if (!DataStatus.Y.getCode().equals(goods.getStatus())){
                        return new Response<>(ERROR, goods.getName() + "商品无效");
                    }
                    //判断商品是否在售
                    if (!DataStage.Y.getCode().equals(goods.getStage())){
                        return new Response<>(ERROR, goods.getName() + "商品已下架");
                    }
                    //判断sku
                    if (this.isEmpty(orderProductRequest.getSpecValues())){
                        return new Response<>(ERROR, "请选择商品规格");
                    }
                    GoodsSpecificationSku goodsSpecificationSku = goodsSpecificationSkuService.findBySpecValuesAndGoodsId(orderProductRequest.getSpecValues(),orderProductRequest.getProductId());
                    if (this.isEmpty(goodsSpecificationSku) || !DataStatus.Y.getCode().equals(goodsSpecificationSku.getStatus())){
                        return new Response<>(ERROR, goods.getName() + "商品规格无效");
                    }
                    if (!goodsSpecificationSku.getGoodsId().equals(goods.getId())){
                        return new Response<>(ERROR, "商品规格有误");
                    }
                    //判断商品价格是否为空
                    if (this.isEmpty(orderProductRequest.getAmount())){
                        return new Response<>(ERROR, goods.getName() + "商品价格为空");
                    }
                    if (goodsSpecificationSku.getPrice().compareTo(orderProductRequest.getAmount()) != 0){
                        OrderProductResponse orderProductResponse = new OrderProductResponse();
                        orderProductResponse.setProductId(goodsSpecificationSku.getGoodsId());
                        orderProductResponse.setSpecValues(goodsSpecificationSku.getSpecValues());
                        orderProductResponse.setAmount(goodsSpecificationSku.getPrice());
                        orderProductResponse.setCode("1000");
                        return new Response<>(ERROR, goods.getName() + "商品价格已过期,已自动更新价格", orderProductResponse);
                    }
                    //判断商品规格库存
                    if (goodsSpecificationSku.getStock() < orderProductRequest.getNumber()){
                        OrderProductResponse orderProductResponse = new OrderProductResponse();
                        orderProductResponse.setProductId(goodsSpecificationSku.getGoodsId());
                        orderProductResponse.setSpecValues(goodsSpecificationSku.getSpecValues());
                        orderProductResponse.setNumber(goodsSpecificationSku.getStock());
                        orderProductResponse.setCode("1001");
                        return new Response<>(ERROR, goods.getName() + "商品规格库存不足",orderProductResponse);
                    }

                    sumAmount = sumAmount.add(goodsSpecificationSku.getPrice().multiply(new BigDecimal(orderProductRequest.getNumber().toString())));

                    OrderProduct orderProduct = new OrderProduct();
                    orderProduct.setProductCatalog(OrderCatalog.C1.getCode());
                    orderProduct.setProductId(goods.getId());
                    orderProduct.setNumber(orderProductRequest.getNumber());
                    orderProduct.setAmount(goodsSpecificationSku.getPrice().multiply(new BigDecimal(orderProductRequest.getNumber().toString())));
                    orderProduct.setProductName(goods.getName());
                    orderProduct.setSkuId(goodsSpecificationSku.getId());
                    //转换存成name
                    String specValues = goodsSpecificationSku.getSpecValues();
                    if (!this.isEmpty(specValues)) {
                        StringBuilder specValuesStr = new StringBuilder();
                        String[] specValueArray = specValues.split(App.COMMA);
                        for (String s : specValueArray) {
                            if (specValuesStr.length() > 0) {
                                specValuesStr.append("/");
                            }
                            specValuesStr.append(s.split(":")[1]);
                        }
                        if (specValuesStr.length() > 0) {
                            orderProduct.setSpecValues(specValuesStr.toString());
                        }
                    }
                    if (!this.isEmpty(goodsSpecificationSku.getImgUrl())) {
                        orderProduct.setImgUrl(goodsSpecificationSku.getImgUrl());
                    }else {
                        if (!this.isEmpty(goods.getImgUrl())){
                            orderProduct.setImgUrl(goods.getImgUrl().split(App.COMMA)[0]);
                        }
                    }
                    orderProduct.setBuyingPrice(goodsSpecificationSku.getBuyingPrice());
                    orderProduct.setOriginalPrice(goodsSpecificationSku.getOriginalPrice());
                    orderProduct.setPrice(goodsSpecificationSku.getPrice());
                    orderProduct.setGrossProfit(goodsSpecificationSku.getGrossProfit());

                    //判断商品是否开通佣金
                    if (goods.getOpenCommission().equals(DataStatus.Y.getCode())){
                        //计算累计毛利
                        BigDecimal sumGrossProfit = goodsSpecificationSku.getGrossProfit().multiply(new BigDecimal(orderProductRequest.getNumber().toString()));
                        if (!this.isEmpty(goods.getCommissionRatio())) {
                            orderProduct.setCommissionRatio(goods.getCommissionRatio());
                            //计算团长佣金 团长佣金 = 商品规格毛利 * 团长佣金比例 * 数量 / 100
                            orderProduct.setCommission(
                                    goods.getCommissionRatio()
                                            .multiply(sumGrossProfit)
                                            .divide(new BigDecimal("100"),4, RoundingMode.HALF_UP));
                            sumCommission = sumCommission.add(orderProduct.getCommission());
                        }
                        if (OrderSelectCatalog.C1.getCode().equals(request.getSelectCatalog()) && !this.isEmpty(goods.getSalesTeacherCommissionRatio())) {
                            orderProduct.setSalesTeacherCommissionRatio(goods.getSalesTeacherCommissionRatio());
                            orderProduct.setSalesTeacherCommission(
                                    goods.getSalesTeacherCommissionRatio()
                                            .multiply(sumGrossProfit)
                                            .divide(new BigDecimal("100"),4, RoundingMode.HALF_UP));
                            sumSalesTeacherCommission = sumSalesTeacherCommission.add(orderProduct.getSalesTeacherCommission());
                        }
                    }else {
                        orderProduct.setCommissionRatio(BigDecimal.ZERO);
                        orderProduct.setCommission(BigDecimal.ZERO);
                        orderProduct.setSalesTeacherCommissionRatio(BigDecimal.ZERO);
                        orderProduct.setSalesTeacherCommission(BigDecimal.ZERO);
                    }
                    orderProduct.setUnit(goods.getUnit());
                    orderProduct.setGoodsCatalog(goods.getCatalog());
                    orderProduct.setGoodsTypeId(goods.getGoodsTypeId());
                    if (!this.isEmpty(goods.getGoodsTypeId())) {
                        GoodsType goodsType = goodsTypeService.findById(goods.getGoodsTypeId());
                        if (!this.isEmpty(goodsType)) {
                            orderProduct.setGoodsTypeName(goodsType.getName());
                        }
                    }
                    orderProduct.setStatus(DataStatus.Y.getCode());
                    orderProduct.setCreateTime(serverTime);
                    orderProduct.setModifyTime(serverTime);
                    orderProducts.add(orderProduct);

                    GoodsSpecificationSku modifyGoodsSpecificationSku = new GoodsSpecificationSku();
                    modifyGoodsSpecificationSku.setId(goodsSpecificationSku.getId());
                    modifyGoodsSpecificationSku.setProductName(goods.getName());
                    modifyGoodsSpecificationSku.setStock(orderProductRequest.getNumber());
                    modifyGoodsSpecificationSku.setModifyTime(serverTime);
                    goodsSpecificationSkus.add(modifyGoodsSpecificationSku);
                }
            }else {
                return new Response<>(ERROR, "请选择正确的订单类型");
            }

            Order order = new Order();
            order.setCode(this.getOrderCode());
            order.setUserId(userToken.getId());
            order.setUserCatalog(user.getCatalog());
            order.setOrderCatalog(request.getOrderCatalog());
            order.setSelectCatalog(request.getSelectCatalog());
            order.setAmount(sumAmount);
            order.setSumCommission(sumCommission);
            //判断下单人是否为别人团员
            UserTeamQuery userTeamQuery = new UserTeamQuery();
            userTeamQuery.setTeamMemberUserId(userToken.getId());
            userTeamQuery.setStatus(DataStatus.Y.getCode());
            List<UserTeam> userTeamList = userTeamService.findAll(userTeamQuery);
            if (!userTeamList.isEmpty()){
                order.setCommissionUserId(userTeamList.get(0).getCaptainUserId());
            }
            order.setSumSalesTeacherCommission(sumSalesTeacherCommission);
            order.setSalesTeacherId(request.getSalesTeacherId());
            order.setStage(OrderStage.NOTPAY.getCode());
            order.setOperateStage(OrderOperateStage.C1.getCode());
            order.setDeliveryCatalog(request.getDeliveryCatalog());
            if (!this.isEmpty(request.getReservationTime())) {
                order.setReservationTime( DateUtil.parse(request.getReservationTime(), DATEMINUTE_FORMAT));
            }
            order.setUserAddressId(request.getUserAddressId());
            order.setPayTime(serverTime);
            order.setComment(request.getComment());
            if (!this.isEmpty(request.getUserAddressId())){
                UserAddress userAddress = userAddressService.findById(request.getUserAddressId());
                if (!this.isEmpty(userAddress)){
                    order.setAddressProvinceId(userAddress.getProvinceId());
                    order.setAddressCityId(userAddress.getCityId());
                    order.setAddressDistrictId(userAddress.getDistrictId());
                    order.setAddressDetailAddress(userAddress.getDetailAddress());
                    order.setAddressName(userAddress.getName());
                    order.setAddressMobile(userAddress.getMobile());
                }
            }

            order.setStatus(DataStatus.Y.getCode());
            order.setCreateTime(serverTime);
            order.setModifyTime(serverTime);

            orderService.create(order, orderProducts, goodsSpecificationSkus);

            // 将订单编号放入缓存中，做延迟队列;
            long expireTimeStamp = DateUtil.getFutureMinute(serverTime, App.USER_ORDER_EXPIRE_TIME).getTime();
            redisTemplate.opsForZSet().add(CacheKey.ORDER_EXPIRE_QUEUE,String.valueOf(order.getId()),expireTimeStamp);

            //创建支付单
            String uuidStr = UUID.randomUUID().toString();
            Order modifyOrder = new Order();
            modifyOrder.setId(order.getId());
            modifyOrder.setPrepayId(uuidStr);
            modifyOrder.setModifyTime(serverTime);

            OrderCodeFlow orderCodeFlowCreate = new OrderCodeFlow();
            orderCodeFlowCreate.setOrderCode(order.getCode());
            orderCodeFlowCreate.setPaymentType(PayType.C1000.getCode());
            orderCodeFlowCreate.setPrepayId(uuidStr);
            orderCodeFlowCreate.setTradeState(WxTradeState.NOTPAY.getCode());
            orderCodeFlowCreate.setStatus(DataStage.Y.getCode());
            orderCodeFlowCreate.setCreateTime(serverTime);
            orderCodeFlowCreate.setModifyTime(serverTime);
            orderService.modifyById(modifyOrder, orderCodeFlowCreate);

            //支付回调
            // 3.更新订单状态
            modifyOrder = new Order();
            modifyOrder.setId(order.getId());
            modifyOrder.setStage(OrderStage.SUCCESS.getCode());
            modifyOrder.setModifyTime(serverTime);
            OrderCodeFlowQuery orderCodeFlowQuery = new OrderCodeFlowQuery();
            orderCodeFlowQuery.setPrepayId(uuidStr);
            List<OrderCodeFlow> orderCodeFlowList = orderCodeFlowService.findAll(orderCodeFlowQuery);

            OrderCodeFlow orderCodeFlow = new OrderCodeFlow();
            if (!orderCodeFlowList.isEmpty()){
                orderCodeFlow.setId(orderCodeFlowList.get(0).getId());
            }
            orderCodeFlow.setOrderCode(order.getCode());
            orderCodeFlow.setPaymentType(PayType.C1000.getCode());
            orderCodeFlow.setPrepayId(uuidStr);
            orderCodeFlow.setTradeType("JSAPI");
            orderCodeFlow.setTradeState("SUCCESS");
            orderCodeFlow.setPayerTotal(order.getAmount().multiply(BigDecimal.valueOf(100)).intValue());
            orderCodeFlow.setContent("通知参数");
            orderCodeFlow.setStatus(DataStage.Y.getCode());
            orderCodeFlow.setCreateTime(serverTime);
            orderCodeFlow.setModifyTime(serverTime);

            //判断订单是否有分佣人
            if (order.getCommissionUserId() != null) {
                //放入缓存添加佣金
                UserCommissionDTO userCommissionDTO = new UserCommissionDTO();
                userCommissionDTO.setOrderId(order.getId());
                userCommissionDTO.setUserId(order.getCommissionUserId());
                userCommissionDTO.setCatalog(UserCommissionFlowCatalog.C1000.getCode());
                redisTemplate.opsForList().leftPush(CacheKey.USER_COMMISSION, this.getJSON(userCommissionDTO));
            }
            orderService.modifyById(modifyOrder,orderCodeFlow);

            return new Response<>(OK, SUCCESS, order.getId());
        } catch (Exception e) {
            if (e instanceof SkuStockException) {
                SkuStockException skuStockException = (SkuStockException) e;
                OrderProductResponse orderProductResponse = new OrderProductResponse();
                orderProductResponse.setProductId(skuStockException.getGoodsId());
                orderProductResponse.setSpecValues(skuStockException.getSpecValues());
                orderProductResponse.setNumber(skuStockException.getStock());
                orderProductResponse.setCode("1001");
                return new Response<>(ERROR, skuStockException.getProductName() + "商品规格库存不足",orderProductResponse);
            }
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "我的订单查询")
    @RequestMapping(value = "/v1/order/my/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<OrderResponse>> myOrderQuery(@RequestBody OrderRequest request){
        try {
            UserToken userToken = this.getUserToken();

            PageResponse<OrderResponse> response = new PageResponse<>();
            OrderQuery orderQuery = new OrderQuery();
            orderQuery.setUserId(userToken.getId());
            orderQuery.setStatus(DataStatus.Y.getCode());

            // 根据查询类型设置条件
            this.setMyOrderQueryConditions(orderQuery, request.getMyOrderType());

            Integer total = orderService.count(orderQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            orderQuery.setStart(pager.getOffset());
            orderQuery.setLimit(pager.getLimit());
            List<Order> orders = orderService.find(orderQuery);
            List<OrderResponse> orderResponses = new ArrayList<>();

            if (!this.isEmpty(orders) && !orders.isEmpty()) {
                // 获取所有订单ID
                List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());

                // 查询订单商品
                OrderProductQuery orderProductQuery = new OrderProductQuery();
                orderProductQuery.setOrderIds(orderIds);
                orderProductQuery.setStatus(DataStatus.Y.getCode());
                List<OrderProduct> orderProducts = orderProductService.findAll(orderProductQuery);

                // 按订单ID分组订单商品
                Map<Long, List<OrderProduct>> orderProductMap = orderProducts.stream()
                    .collect(Collectors.groupingBy(OrderProduct::getOrderId));

                for (Order order : orders) {
                    OrderResponse orderResponse = new OrderResponse(order);

                    // 设置支付状态名称
                    orderResponse.setStageName(OrderStage.getName(order.getStage()));

                    // 获取该订单的商品列表
                    List<OrderProduct> currentOrderProducts = orderProductMap.get(order.getId());
                    if (currentOrderProducts != null && !currentOrderProducts.isEmpty()) {
                        // 计算商品总数量
                        int totalCount = currentOrderProducts.stream()
                            .mapToInt(OrderProduct::getNumber)
                            .sum();
                        orderResponse.setTotalProductCount(totalCount);

                        // 转换订单商品对象
                        List<OrderProductResponse> orderProductResponses = currentOrderProducts.stream()
                            .map(OrderProductResponse::new)
                            .collect(Collectors.toList());
                        orderResponse.setOrderProducts(orderProductResponses);
                    } else {
                        orderResponse.setTotalProductCount(0);
                        orderResponse.setOrderProducts(new ArrayList<>());
                    }

                    orderResponses.add(orderResponse);
                }
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(orderResponses.size());
            response.setList(orderResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "我的订单申请退费")
    @RequestMapping(value = "/v1/order/apply/refund/my/create", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> createMyApplyRefund(@RequestBody OrderRequest request) {
        try {
            Date serverTime = this.getServerTime();
            UserToken userToken = this.getUserToken();

            // 参数验证
            OrderValidator validator = new OrderValidator();
            if (!validator
                    .onId(request.getId())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            // 验证订单是否存在
            Order existingOrder = orderService.findById(request.getId());
            if (existingOrder == null) {
                return new Response<>(ERROR, "订单不存在");
            }

            //判断下单人是否为自己
            if (!existingOrder.getUserId().equals(userToken.getId())){
                return new Response<>(ERROR, "无权限操作");
            }

            // 验证订单状态是否可以操作
            if (!OrderStage.SUCCESS.getCode().equals(existingOrder.getStage())) {
                return new Response<>(ERROR, "只有已支付的订单才能进行收货操作");
            }


            // 更新订单提货状态
            Order order = new Order();
            order.setId(request.getId());
            order.setStage(OrderStage.PENDING_REFUND.getCode());
            order.setModifyTime(serverTime);

            orderService.modifyById(order);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 根据我的订单查询类型设置查询条件
     */
    private void setMyOrderQueryConditions(OrderQuery orderQuery, String myOrderType) {
        if (this.isEmpty(myOrderType)) {
            myOrderType = MyOrderType.ALL.getCode(); // 默认查询全部
        }

        MyOrderType type = MyOrderType.get(myOrderType);
        if (type == null) {
            return;
        }

        switch (type) {
            case WAIT_PAY:
                // 待付款：未支付状态
                orderQuery.setStage(OrderStage.NOTPAY.getCode());
                break;
            case WAIT_PICKUP:
                // 待提货：支付成功且未提货
                orderQuery.setStage(OrderStage.SUCCESS.getCode());
                orderQuery.setOperateStage(OrderOperateStage.C1.getCode());
                break;
            case REFUND:
                // 退款：包含所有退款相关状态
                List<String> refundStages = new ArrayList<>();
                refundStages.add(OrderStage.AUTO_REFUND_PROCESSING.getCode());
                refundStages.add(OrderStage.AUTO_REFUND_SUCCESS.getCode());
                refundStages.add(OrderStage.AUTO_REFUND_ABNORMAL.getCode());
                refundStages.add(OrderStage.REFUND_PROCESSING.getCode());
                refundStages.add(OrderStage.REFUND_SUCCESS.getCode());
                refundStages.add(OrderStage.REFUND_ABNORMAL.getCode());
                refundStages.add(OrderStage.PENDING_REFUND.getCode());
                orderQuery.setStages(refundStages);
                break;
            case ALL:
            default:
                // 全部：不设置特殊条件，查询所有状态
                break;
        }
    }
}
