package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.CourseRequest;
import com.api.bean.CourseResponse;
import com.api.bean.PageResponse;
import com.api.config.Token;
import com.api.validator.CourseValidator;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.Course;
import com.domain.complex.CourseQuery;
import com.service.CourseService;


@Tag(name = "课程表")
@RestController
public class CourseController extends BaseController {
    @Autowired
    private CourseService courseService;

    @Operation(summary = "课程分页查询")
    @RequestMapping(value = "/v1/course/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<CourseResponse>> query(@RequestBody CourseRequest request){
        try {
            PageResponse<CourseResponse> response = new PageResponse<>();
            CourseQuery courseQuery = new CourseQuery();
            courseQuery.setStatus(DataStatus.Y.getCode());
            courseQuery.setHome(request.getHome());
            courseQuery.setTitle(request.getTitle());
            courseQuery.setStage(request.getStage());
            Integer total = courseService.count(courseQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            courseQuery.setStart(pager.getOffset());
            courseQuery.setLimit(pager.getLimit());
            List<Course> courses = courseService.find(courseQuery);

            List<CourseResponse> courseResponses = new ArrayList<>();

            for (Course course : courses) {
                CourseResponse courseResponse = new CourseResponse(course);
                courseResponses.add(courseResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(courseResponses.size());
            response.setList(courseResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "课程查询")
    @RequestMapping(value = "/v1/course/all/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<List<CourseResponse>> queryAll(@RequestBody CourseRequest request){
        try {
            CourseQuery courseQuery = new CourseQuery();
            courseQuery.setStatus(DataStatus.Y.getCode());
            courseQuery.setHome(request.getHome());
            courseQuery.setTitle(request.getTitle());
            courseQuery.setStage(request.getStage());
            List<Course> courses = courseService.findAll(courseQuery);

            List<CourseResponse> courseResponses = new ArrayList<>();

            for (Course course : courses) {
                CourseResponse courseResponse = new CourseResponse(course);
                courseResponses.add(courseResponse);
            }

            return new Response<>(OK, SUCCESS, courseResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "课程id查询")
    @RequestMapping(value = "/v1/course/id/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<CourseResponse> queryById(@RequestBody CourseRequest request){
        try {
            CourseValidator validator = new CourseValidator();
            if (!validator
                    .onId(request.getId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            Course course = courseService.findById(request.getId());

            CourseResponse courseResponse = new CourseResponse(course);

            return new Response<>(OK, SUCCESS, courseResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "课程新增")
    @RequestMapping(value = "/v1/course/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> create(@RequestBody CourseRequest request){
        try {
            Date serverTime = this.getServerTime();
            CourseValidator validator = new CourseValidator();
            if (!validator
                    .onTitle(request.getTitle())
                    .onImgUrl(request.getImgUrl())
                    .onHome(request.getHome())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            Course course = new Course();
            course.setTitle(request.getTitle());
            course.setImgUrl(request.getImgUrl());
            course.setLength(request.getLength() != null ? request.getLength() : 0);
            course.setDetailImgUrl(request.getDetailImgUrl());
            course.setOriginalPrice(request.getOriginalPrice());
            course.setPrice(request.getPrice());
            course.setComment(request.getComment());
            course.setSalesVolume(request.getSalesVolume() != null ? request.getLength() : 0);
            course.setHome(request.getHome());
            course.setStage(request.getStage());
            course.setSort(request.getSort() != null ? request.getLength() : 0);
            course.setStatus(DataStatus.Y.getCode());
            course.setModifyTime(serverTime);
            course.setCreateTime(serverTime);
            courseService.create(course);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "课程修改")
    @RequestMapping(value = "/v1/course/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modify(@RequestBody CourseRequest request){
        try {
            Date serverTime = this.getServerTime();
            CourseValidator validator = new CourseValidator();
            if (!validator
                    .onId(request.getId())
                    .onTitle(request.getTitle())
                    .onImgUrl(request.getImgUrl())
                    .onHome(request.getHome())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            Course course = new Course();
            course.setId(request.getId());
            course.setTitle(request.getTitle());
            course.setImgUrl(request.getImgUrl());
            course.setLength(request.getLength() != null ? request.getLength() : 0);
            course.setDetailImgUrl(request.getDetailImgUrl());
            course.setOriginalPrice(request.getOriginalPrice());
            course.setPrice(request.getPrice());
            course.setComment(request.getComment());
            course.setSalesVolume(request.getSalesVolume() != null ? request.getLength() : 0);
            course.setHome(request.getHome());
            course.setStage(request.getStage());
            course.setSort(request.getSort() != null ? request.getLength() : 0);
            course.setModifyTime(serverTime);
            courseService.modifyById(course);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "课程删除")
    @RequestMapping(value = "/v1/course/remove",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> remove(@RequestBody CourseRequest request){
        try {
            Date serverTime = this.getServerTime();
            CourseValidator validator = new CourseValidator();
            if (!validator
                    .onId(request.getId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            Course course = new Course();
            course.setId(request.getId());
            course.setStatus(DataStatus.N.getCode());
            course.setModifyTime(serverTime);
            courseService.modifyById(course);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "课程上下架")
    @RequestMapping(value = "/v1/course/stage/modify",method = {RequestMethod.POST})
    @ResponseBody
    public Response<?> modifyStage(@RequestBody CourseRequest request){
        try {
            Date serverTime = this.getServerTime();
            CourseValidator validator = new CourseValidator();
            if (!validator
                    .onId(request.getId())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            Course course = new Course();
            course.setId(request.getId());
            course.setStage(request.getStage());
            course.setModifyTime(serverTime);
            courseService.modifyById(course);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
