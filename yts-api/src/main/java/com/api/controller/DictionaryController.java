package com.api.controller;

import java.util.Set;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.DictionaryRequest;
import com.api.bean.DictionaryResponse;
import com.api.config.Token;
import com.api.validator.DictionaryValidator;
import com.common.bean.Response;
import com.common.bean.UserToken;
import com.common.constant.CacheKey;
import com.common.constant.TemporaryStorageCatalog;

@Tag(name = "字典表")
@RestController
public class DictionaryController extends BaseController {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Operation(summary = "自定义表头、自定义查询条件 创建", description = "<P>作者：刘波</p>")
    @Parameters(value = {
            @Parameter(name = "temporaryStorageCatalog",description = "临时存储目录类型(C10：页面暂存、C11：自定义表头、C12：自定义查询条件)",required = true,in = ParameterIn.QUERY, schema = @Schema(implementation = String.class)),
            @Parameter(name = "menuName",description = "菜单名称",required = true,in = ParameterIn.QUERY, schema = @Schema(implementation = String.class)),
            @Parameter(name = "value",description = "值",required = false,in = ParameterIn.QUERY, schema = @Schema(implementation = String.class)),
    })
    @RequestMapping(value = "/v1/temporary/storage/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> createTemporaryStorage(@RequestBody DictionaryRequest request) {
        UserToken userToken = this.getUserToken();
        DictionaryValidator dictionaryValidator = new DictionaryValidator();
        if (dictionaryValidator.onTemporaryStorageCatalog(request.getTemporaryStorageCatalog()).onMenuName(request.getMenuName()).result()) {
            String cacheKey = CacheKey.MENU_DATA_TEMPORARY_STORAGE + userToken.getId() + request.getTemporaryStorageCatalog() + request.getMenuName();
            if (this.isEmpty(request.getValue())) {
                //如果接收的是 暂存类型 并且 value为空，则证明是删除暂存
                if (request.getTemporaryStorageCatalog().equals(TemporaryStorageCatalog.C10.getCode())) {
                    redisTemplate.delete(cacheKey);
                    return new Response<>(OK, SUCCESS);
                }
            }
            //暂存数据
            redisTemplate.opsForValue().set(cacheKey, request.getValue());
            return new Response<>(OK, SUCCESS);
        } else {
            return new Response<>(ERROR, dictionaryValidator.getErrorMessage());
        }
    }


    @Operation(summary = "页面数据 自定义表头、自定义查询条件查询", description = "<P>作者：刘波</p>")
    @Parameters(value = {
            @Parameter(name = "temporaryStorageCatalog",description = "临时存储目录类型(C10：页面暂存、C11：自定义表头、C12：自定义查询条件)",required = true,in = ParameterIn.QUERY, schema = @Schema(implementation = String.class)),
            @Parameter(name = "menuName",description = "菜单名称",required = true,in = ParameterIn.QUERY, schema = @Schema(implementation = String.class)),
    })
    @RequestMapping(value = "/v1/dictionary/temporary/storage/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> queryTemporaryStorage(@RequestBody DictionaryRequest request) {
        DictionaryResponse dictionaryResponse = new DictionaryResponse();
        UserToken userToken = this.getUserToken();
        DictionaryValidator dictionaryValidator = new DictionaryValidator();
        if(dictionaryValidator.onTemporaryStorageCatalog(request.getTemporaryStorageCatalog()).onMenuName(request.getMenuName()).result()){
            String cacheKey = CacheKey.MENU_DATA_TEMPORARY_STORAGE + userToken.getId() + request.getTemporaryStorageCatalog() + request.getMenuName();
            if(redisTemplate.hasKey(cacheKey)){
                String val = redisTemplate.opsForValue().get(cacheKey);
                dictionaryResponse.setValue(val);
                return new Response<>(OK,SUCCESS,dictionaryResponse);
            }
        }else {
            return new Response<>(ERROR,dictionaryValidator.getErrorMessage());
        }
        return new Response<>(OK,SUCCESS,dictionaryResponse);
    }


    @Operation(summary = "页面数据  自定义表头、自定义查询条件  删除", description = "<P>作者：刘波</p>")
    @RequestMapping(value = "/v1/dictionary/temporary/storage/delete",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> deleteRedis() {
        UserToken userToken = this.getUserToken();
        StringBuffer catchKey = new StringBuffer();
        String prex = catchKey.append(CacheKey.MENU_DATA_TEMPORARY_STORAGE).append(userToken.getId()).append("*").toString();
        Set<String> keys = redisTemplate.keys(prex);
        if(keys.size() > 0){
            redisTemplate.delete(keys);
        }
        return new Response<>(OK,SUCCESS);
    }

}
