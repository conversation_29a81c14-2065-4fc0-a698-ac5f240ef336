package com.api.controller;

import java.util.ArrayList;
import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.ProvinceRequest;
import com.api.bean.ProvinceResponse;
import com.api.config.Token;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.Province;
import com.domain.complex.ProvinceQuery;
import com.service.ProvinceService;


@Tag(name = "省份表")
@RestController
public class ProvinceController extends BaseController {
    @Autowired
    private ProvinceService provinceService;

    @Operation(summary = "省列表")
    @RequestMapping(value = "/v1/province/all/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<List<ProvinceResponse>> queryAll(@RequestBody ProvinceRequest request){
        try {
            ProvinceQuery provinceQuery = new ProvinceQuery();
            provinceQuery.setStatus(DataStatus.Y.getCode());
            List<Province> provinces = provinceService.findAll(provinceQuery);
            List<ProvinceResponse> provinceResponses = new ArrayList<>();
            for (Province province : provinces) {
                ProvinceResponse provinceResponse = new ProvinceResponse(province);
                provinceResponses.add(provinceResponse);
            }
            return new Response<>(OK, SUCCESS, provinceResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
