package com.api.controller;

import java.util.Date;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.ProductBrowseRequest;
import com.api.validator.ProductBrowseValidator;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.ProductBrowse;
import com.service.ProductBrowseService;


@Tag(name = "产品浏览表")
@RestController
public class ProductBrowseController extends BaseController {
    @Autowired
    private ProductBrowseService productBrowseService;

    @Operation(summary = "产品浏览新增")
    @RequestMapping(value = "/v1/product/browse/create",method = {RequestMethod.POST})
    @ResponseBody
    public Response<?> create(@RequestBody ProductBrowseRequest request){
        try {
            Date serverTime = this.getServerTime();
            ProductBrowseValidator validator = new ProductBrowseValidator();
            if (!validator
                    .onProductCatalog(request.getProductCatalog())
                    .onProductId(request.getProductId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            ProductBrowse productBrowse = new ProductBrowse();
            productBrowse.setProductCatalog(request.getProductCatalog());
            productBrowse.setProductId(request.getProductId());
            if (!this.isEmpty(this.getUserToken()) && !this.isEmpty(this.getUserToken().getId())) {
                productBrowse.setUserId(this.getUserToken().getId());
            }
            productBrowse.setStatus(DataStatus.Y.getCode());
            productBrowse.setModifyTime(serverTime);
            productBrowse.setCreateTime(serverTime);
            productBrowseService.create(productBrowse);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
