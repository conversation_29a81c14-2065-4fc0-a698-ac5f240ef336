package com.api.controller;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.UserAddressRequest;
import com.api.bean.UserAddressResponse;
import com.api.config.Token;
import com.api.validator.UserAddressValidator;
import com.common.bean.Response;
import com.common.bean.UserToken;
import com.common.constant.DataStatus;
import com.common.constant.PublicYesNo;
import com.domain.City;
import com.domain.District;
import com.domain.Province;
import com.domain.UserAddress;
import com.domain.complex.UserAddressQuery;
import com.service.CityService;
import com.service.DistrictService;
import com.service.ProvinceService;
import com.service.UserAddressService;


@Tag(name = "用户地址表")
@RestController
public class UserAddressController extends BaseController {
    @Autowired
    private UserAddressService userAddressService;
    @Autowired
    private ProvinceService provinceService;
    @Autowired
    private CityService cityService;
    @Autowired
    private DistrictService districtService;

    @Operation(summary = "查询自己收货地址")
    @RequestMapping(value = "/v1/user/address/my/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<List<UserAddressResponse>> queryMy(@RequestBody UserAddressRequest request){
        try {
            UserToken userToken = this.getUserToken();
            List<UserAddressResponse> userAddressResponseList = new ArrayList<>();

            UserAddressQuery userAddressQuery = new UserAddressQuery();
            userAddressQuery.setUserId(userToken.getId());
            userAddressQuery.setStatus(DataStatus.Y.getCode());
            List<UserAddress> userAddressList = userAddressService.findAll(userAddressQuery);

            if (this.isEmpty(userAddressList) || userAddressList.isEmpty()){
                return new Response<>(OK, SUCCESS, userAddressResponseList);
            }

            //按是否默认地址正序、id倒序排序
            userAddressList.sort(
                    Comparator.comparing(UserAddress::getDefaultAddress) // 正序
                            .thenComparing(UserAddress::getId, Comparator.reverseOrder()) // 倒序
            );
            List<Long> provinceIds = new ArrayList<>();
            List<Long> cityIds = new ArrayList<>();
            List<Long> districtIds = new ArrayList<>();
            Map<Long, Province> provinceMap = new HashMap<>();
            Map<Long, City> cityMap = new HashMap<>();
            Map<Long, District> districtMap = new HashMap<>();
            for (UserAddress userAddress : userAddressList) {
                if (userAddress.getProvinceId() != null) {
                    provinceIds.add(userAddress.getProvinceId());
                }
                if (userAddress.getCityId() != null) {
                    cityIds.add(userAddress.getCityId());
                }
                if (userAddress.getDistrictId() != null) {
                    districtIds.add(userAddress.getDistrictId());
                }
            }
            if (!provinceIds.isEmpty()){
                provinceMap = provinceService.findMapByIds(provinceIds);
            }
            if (!cityIds.isEmpty()){
                cityMap = cityService.findMapByIds(cityIds);
            }
            if (!districtIds.isEmpty()){
                districtMap = districtService.findMapByIds(districtIds);
            }

            for (UserAddress userAddress : userAddressList) {
                UserAddressResponse userAddressResponse = new UserAddressResponse(userAddress);
                if (provinceMap.containsKey(userAddress.getProvinceId())){
                    userAddressResponse.setProvinceName(provinceMap.get(userAddress.getProvinceId()).getName());
                }
                if (cityMap.containsKey(userAddress.getCityId())){
                    userAddressResponse.setCityName(cityMap.get(userAddress.getCityId()).getName());
                }
                if (districtMap.containsKey(userAddress.getDistrictId())){
                    userAddressResponse.setDistrictName(districtMap.get(userAddress.getDistrictId()).getName());
                }
                userAddressResponseList.add(userAddressResponse);
            }
            return new Response<>(OK, SUCCESS, userAddressResponseList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "添加自己收货地址")
    @RequestMapping(value = "/v1/user/address/my/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> createMy(@RequestBody UserAddressRequest request){
        try {
            Date serverTime = this.getServerTime();
            UserToken userToken = this.getUserToken();
            UserAddressValidator validator = new UserAddressValidator();
            if (!validator
                    .onDetailAddress(request.getDetailAddress())
                    .onName(request.getName())
                    .onMobile(request.getMobile())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            UserAddress userAddress = new UserAddress();
            userAddress.setUserId(userToken.getId());
            userAddress.setDetailAddress(request.getDetailAddress());
            userAddress.setName(request.getName());
            userAddress.setMobile(request.getMobile());
            userAddress.setProvinceId(request.getProvinceId());
            userAddress.setCityId(request.getCityId());
            userAddress.setDistrictId(request.getDistrictId());
            userAddress.setDefaultAddress(request.getDefaultAddress() != null ? request.getDefaultAddress() : PublicYesNo.N.getCode());
            userAddress.setStatus(DataStatus.Y.getCode());
            userAddress.setCreateTime(serverTime);
            userAddress.setModifyTime(serverTime);
            userAddressService.create(userAddress);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "编辑收货地址")
    @RequestMapping(value = "/v1/user/address/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modify(@RequestBody UserAddressRequest request){
        try {
            Date serverTime = this.getServerTime();
            UserToken userToken = this.getUserToken();
            UserAddressValidator validator = new UserAddressValidator();
            if (!validator
                    .onId(request.getId())
                    .onDetailAddress(request.getDetailAddress())
                    .onName(request.getName())
                    .onMobile(request.getMobile())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            UserAddress userAddress = new UserAddress();
            userAddress.setId(request.getId());
            userAddress.setDetailAddress(request.getDetailAddress());
            userAddress.setName(request.getName());
            userAddress.setMobile(request.getMobile());
            userAddress.setProvinceId(request.getProvinceId());
            userAddress.setCityId(request.getCityId());
            userAddress.setDistrictId(request.getDistrictId());
            userAddress.setDefaultAddress(request.getDefaultAddress() != null ? request.getDefaultAddress() : PublicYesNo.N.getCode());
            userAddress.setModifyTime(serverTime);

            //如果是为默认地址，需要吧其他地址设置为非默认地址
            List<UserAddress> modifyUserAddressList = new ArrayList<>();
            if (PublicYesNo.Y.getCode().equals(userAddress.getDefaultAddress())){
                UserAddressQuery userAddressQuery = new UserAddressQuery();
                userAddressQuery.setUserId(userToken.getId());
                userAddressQuery.setDefaultAddress(PublicYesNo.Y.getCode());
                List<UserAddress> userAddressList = userAddressService.findAll(userAddressQuery);
                for (UserAddress address : userAddressList) {
                    if (!address.getId().equals(userAddress.getId())){
                        UserAddress modifyUserAddress = new UserAddress();
                        modifyUserAddress.setId(address.getId());
                        modifyUserAddress.setDefaultAddress(PublicYesNo.N.getCode());
                        modifyUserAddress.setModifyTime(serverTime);
                        modifyUserAddressList.add(modifyUserAddress);
                    }
                }
            }
            userAddressService.modifyById(userAddress,modifyUserAddressList);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "编辑默认地址")
    @RequestMapping(value = "/v1/user/address/default/address/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modifyDefaultAddress(@RequestBody UserAddressRequest request){
        try {
            Date serverTime = this.getServerTime();
            UserToken userToken = this.getUserToken();
            UserAddressValidator validator = new UserAddressValidator();
            if (!validator
                    .onId(request.getId())
                    .onDefaultAddress(request.getDefaultAddress()).result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            UserAddress userAddress = new UserAddress();
            userAddress.setDefaultAddress(request.getDefaultAddress());
            userAddress.setModifyTime(serverTime);

            //如果是为默认地址，需要吧其他地址设置为非默认地址
            List<UserAddress> modifyUserAddressList = new ArrayList<>();
            if (PublicYesNo.Y.getCode().equals(userAddress.getDefaultAddress())){
                UserAddressQuery userAddressQuery = new UserAddressQuery();
                userAddressQuery.setUserId(userToken.getId());
                userAddressQuery.setDefaultAddress(PublicYesNo.Y.getCode());
                List<UserAddress> userAddressList = userAddressService.findAll(userAddressQuery);
                for (UserAddress address : userAddressList) {
                    if (!address.getId().equals(userAddress.getId())){
                        UserAddress modifyUserAddress = new UserAddress();
                        modifyUserAddress.setId(address.getId());
                        modifyUserAddress.setDefaultAddress(PublicYesNo.N.getCode());
                        modifyUserAddress.setModifyTime(serverTime);
                        modifyUserAddressList.add(modifyUserAddress);
                    }
                }
            }
            userAddressService.modifyById(userAddress,modifyUserAddressList);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "删除收货地址")
    @RequestMapping(value = "/v1/user/address/remove",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> remove(@RequestBody UserAddressRequest request){
        try {
            Date serverTime = this.getServerTime();
            UserAddressValidator validator = new UserAddressValidator();
            if (!validator
                    .onId(request.getId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            UserAddress userAddress = new UserAddress();
            userAddress.setId(request.getId());
            userAddress.setStatus(DataStatus.N.getCode());
            userAddress.setModifyTime(serverTime);
            userAddressService.modifyById(userAddress);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
