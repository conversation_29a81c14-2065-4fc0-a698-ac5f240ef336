package com.api.controller;

import java.util.ArrayList;
import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.DistrictRequest;
import com.api.bean.DistrictResponse;
import com.api.config.Token;
import com.api.validator.DistrictValidator;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.District;
import com.domain.complex.DistrictQuery;
import com.service.DistrictService;


@Tag(name = "区(县)表")
@RestController
public class DistrictController extends BaseController {
    @Autowired
    private DistrictService districtService;

    @Operation(summary = "根据市查区列表")
    @RequestMapping(value = "/v1/district/city/id/all/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<List<DistrictResponse>> queryAllByCityId(@RequestBody DistrictRequest request){
        try {
            DistrictValidator validator = new DistrictValidator();
            if (!validator
                    .onCityId(request.getCityId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            DistrictQuery districtQuery = new DistrictQuery();
            districtQuery.setCityId(request.getCityId());
            districtQuery.setStatus(DataStatus.Y.getCode());
            List<District> districts = districtService.findAll(districtQuery);
            List<DistrictResponse> districtResponses = new ArrayList<>();
            for (District district : districts) {
                DistrictResponse districtResponse = new DistrictResponse(district);
                districtResponses.add(districtResponse);
            }
            return new Response<>(OK, SUCCESS, districtResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
