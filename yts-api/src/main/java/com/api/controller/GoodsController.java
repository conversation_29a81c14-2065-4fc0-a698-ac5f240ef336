package com.api.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.GoodsRequest;
import com.api.bean.GoodsResponse;
import com.api.bean.GoodsSpecificationSkuRequest;
import com.api.bean.GoodsSpecificationSkuResponse;
import com.api.bean.GoodsSpecificationTypeRequest;
import com.api.bean.GoodsSpecificationTypeResponse;
import com.api.bean.GoodsSpecificationValueRequest;
import com.api.bean.GoodsSpecificationValueResponse;
import com.api.bean.PageResponse;
import com.api.config.Token;
import com.api.validator.GoodsSpecificationSkuValidator;
import com.api.validator.GoodsValidator;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.constant.GoodsSortType;
import com.common.util.DateUtil;
import com.domain.Goods;
import com.domain.GoodsSpecificationSku;
import com.domain.GoodsSpecificationType;
import com.domain.GoodsSpecificationValue;
import com.domain.GoodsType;
import com.domain.complex.GoodsQuery;
import com.domain.complex.GoodsSpecificationSkuQuery;
import com.domain.complex.GoodsSpecificationTypeQuery;
import com.domain.complex.GoodsSpecificationValueQuery;
import com.service.GoodsService;
import com.service.GoodsSpecificationSkuService;
import com.service.GoodsSpecificationTypeService;
import com.service.GoodsSpecificationValueService;
import com.service.GoodsTypeService;


@Tag(name = "商品表")
@RestController
public class GoodsController extends BaseController {
    @Autowired
    private GoodsService goodsService;
    @Autowired
    private GoodsTypeService goodsTypeService;
    @Autowired
    private GoodsSpecificationTypeService goodsSpecificationTypeService;
    @Autowired
    private GoodsSpecificationValueService goodsSpecificationValueService;
    @Autowired
    private GoodsSpecificationSkuService goodsSpecificationSkuService;

    @Operation(summary = "商品分页查询")
    @RequestMapping(value = "/v1/goods/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<PageResponse<GoodsResponse>> query(@RequestBody GoodsRequest request){
        try {
            PageResponse<GoodsResponse> response = new PageResponse<>();
            GoodsQuery goodsQuery = new GoodsQuery();
            goodsQuery.setStatus(DataStatus.Y.getCode());
            goodsQuery.setCatalog(request.getCatalog());
            goodsQuery.setGoodsTypeId(request.getGoodsTypeId());
            goodsQuery.setGoodsTypeIds(request.getGoodsTypeIds());
            goodsQuery.setStage(request.getStage());
            goodsQuery.setName(request.getName());
            goodsQuery.setMinPrice(request.getMinPrice());
            goodsQuery.setMaxPrice(request.getMaxPrice());
            goodsQuery.setMinRealSalesVolume(request.getMinRealSalesVolume());
            goodsQuery.setMaxRealSalesVolume(request.getMaxRealSalesVolume());
            if (this.isEmpty(request.getSortType()) || this.isEmpty(GoodsSortType.getSql(request.getSortType()))) {
                goodsQuery.setSortType(GoodsSortType.C4.getSql());
            }else {
                goodsQuery.setSortType(GoodsSortType.getSql(request.getSortType()));
            }
            if (!this.isEmpty(request.getMinCreateTime())) {
                goodsQuery.setMinCreateTime(DateUtil.parse(request.getMinCreateTime(),DATETIME_FORMAT));
            }
            if (!this.isEmpty(request.getMaxCreateTime())) {
                goodsQuery.setMaxCreateTime(DateUtil.parse(request.getMaxCreateTime(),DATETIME_FORMAT));
            }
            goodsQuery.setHome(request.getHome());
            if (this.isEmpty(request.getPage())){
                request.setPage(1);
            }
            if (this.isEmpty(request.getLimit())){
                request.setLimit(10);
            }
            Integer total = goodsService.count(goodsQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            goodsQuery.setStart(pager.getOffset());
            goodsQuery.setLimit(pager.getLimit());
            List<Goods> goodsList = goodsService.find(goodsQuery);

            List<GoodsResponse> goodsResponses = new ArrayList<>();

            Set<Long> goodsTypeIdSet = goodsList.stream().map(Goods::getGoodsTypeId).collect(Collectors.toSet());
            Map<Long, GoodsType> goodsTypeMap = new HashMap<>();
            if (!goodsTypeIdSet.isEmpty()){
                goodsTypeMap = goodsTypeService.findMapByIds(new ArrayList<>(goodsTypeIdSet));
            }

            Set<Long> goodsIdSet = goodsList.stream().map(Goods::getId).collect(Collectors.toSet());
            Map<Long, List<GoodsSpecificationType>> goodsSpecificationTypeMap = new HashMap<>();
            Map<Long, List<GoodsSpecificationValue>> goodsSpecificationValueMap = new HashMap<>();
            Map<Long, List<GoodsSpecificationSku>> goodsSpecificationSkuMap = new HashMap<>();
            if (!goodsIdSet.isEmpty()){
                GoodsSpecificationTypeQuery goodsSpecificationTypeQuery = new GoodsSpecificationTypeQuery();
                goodsSpecificationTypeQuery.setGoodsIds(new ArrayList<>(goodsIdSet));
                goodsSpecificationTypeQuery.setStatus(DataStatus.Y.getCode());
                List<GoodsSpecificationType> goodsSpecificationTypes = goodsSpecificationTypeService.findAll(goodsSpecificationTypeQuery);
                goodsSpecificationTypeMap = goodsSpecificationTypes.stream().collect(Collectors.groupingBy(GoodsSpecificationType::getGoodsId));

                List<Long> goodsSpecificationTypeIds = goodsSpecificationTypes.stream().map(GoodsSpecificationType::getId).collect(Collectors.toList());
                GoodsSpecificationValueQuery goodsSpecificationValueQuery = new GoodsSpecificationValueQuery();
                goodsSpecificationValueQuery.setGoodsIds(new ArrayList<>(goodsIdSet));
                goodsSpecificationValueQuery.setGoodsSpecificationTypeIds(goodsSpecificationTypeIds);
                goodsSpecificationValueQuery.setStatus(DataStatus.Y.getCode());
                List<GoodsSpecificationValue> goodsSpecificationValues = goodsSpecificationValueService.findAll(goodsSpecificationValueQuery);
                goodsSpecificationValueMap = goodsSpecificationValues.stream().filter(o -> !this.isEmpty(o.getGoodsSpecificationTypeId())).collect(Collectors.groupingBy(GoodsSpecificationValue::getGoodsSpecificationTypeId));

                GoodsSpecificationSkuQuery goodsSpecificationSkuQuery = new GoodsSpecificationSkuQuery();
                goodsSpecificationSkuQuery.setGoodsIds(new ArrayList<>(goodsIdSet));
                goodsSpecificationSkuQuery.setStatus(DataStatus.Y.getCode());
                List<GoodsSpecificationSku> goodsSpecificationSkus = goodsSpecificationSkuService.findAll(goodsSpecificationSkuQuery);
                goodsSpecificationSkuMap = goodsSpecificationSkus.stream().filter(o -> !this.isEmpty(o.getGoodsId())).collect(Collectors.groupingBy(GoodsSpecificationSku::getGoodsId));
            }
            for (Goods goods : goodsList) {
                GoodsResponse goodsResponse = new GoodsResponse(goods);
                if (goodsTypeMap.containsKey(goods.getGoodsTypeId())) {
                    goodsResponse.setGoodsTypeName(goodsTypeMap.get(goods.getGoodsTypeId()).getName());
                }
                List<GoodsSpecificationTypeResponse> goodsSpecificationTypeResponses = new ArrayList<>();
                if (goodsSpecificationTypeMap.containsKey(goods.getId())) {
                    for (GoodsSpecificationType goodsSpecificationType : goodsSpecificationTypeMap.get(goods.getId())) {
                        GoodsSpecificationTypeResponse goodsSpecificationTypeResponse = new GoodsSpecificationTypeResponse(goodsSpecificationType);
                        if (goodsSpecificationValueMap.containsKey(goodsSpecificationType.getId())) {
                            goodsSpecificationTypeResponse.setGoodsSpecificationValueResponses(goodsSpecificationValueMap.get(goodsSpecificationType.getId()).stream().map(GoodsSpecificationValueResponse::new).collect(Collectors.toList()));
                        }
                        goodsSpecificationTypeResponses.add(goodsSpecificationTypeResponse);
                    }
                }
                goodsResponse.setGoodsSpecificationTypeResponses(goodsSpecificationTypeResponses);
                //库存
                if (goodsSpecificationSkuMap.containsKey(goods.getId())) {
                    int inventory = goodsSpecificationSkuMap.get(goods.getId()).stream().filter(o -> !this.isEmpty(o.getStock())).mapToInt(GoodsSpecificationSku::getStock).sum();
                    goodsResponse.setInventory(inventory);
                }
                goodsResponses.add(goodsResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(goodsResponses.size());
            response.setList(goodsResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "商品查询")
    @RequestMapping(value = "/v1/goods/all/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<List<GoodsResponse>> queryAll(@RequestBody GoodsRequest request){
        try {
            GoodsQuery goodsQuery = new GoodsQuery();
            goodsQuery.setStatus(DataStatus.Y.getCode());
            goodsQuery.setCatalog(request.getCatalog());
            goodsQuery.setGoodsTypeId(request.getGoodsTypeId());
            goodsQuery.setGoodsTypeIds(request.getGoodsTypeIds());
            goodsQuery.setStage(request.getStage());
            goodsQuery.setName(request.getName());
            goodsQuery.setMinPrice(request.getMinPrice());
            goodsQuery.setMaxPrice(request.getMaxPrice());
            goodsQuery.setMinRealSalesVolume(request.getMinRealSalesVolume());
            goodsQuery.setMaxRealSalesVolume(request.getMaxRealSalesVolume());
            if (this.isEmpty(request.getSortType()) || this.isEmpty(GoodsSortType.getSql(request.getSortType()))) {
                goodsQuery.setSortType(GoodsSortType.C4.getSql());
            }else {
                goodsQuery.setSortType(GoodsSortType.getSql(request.getSortType()));
            }
            if (!this.isEmpty(request.getMinCreateTime())) {
                goodsQuery.setMinCreateTime(DateUtil.parse(request.getMinCreateTime(),DATETIME_FORMAT));
            }
            if (!this.isEmpty(request.getMaxCreateTime())) {
                goodsQuery.setMaxCreateTime(DateUtil.parse(request.getMaxCreateTime(),DATETIME_FORMAT));
            }
            goodsQuery.setHome(request.getHome());
            List<Goods> goodsList = goodsService.findAll(goodsQuery);

            List<GoodsResponse> goodsResponses = new ArrayList<>();

            Set<Long> goodsTypeIdSet = goodsList.stream().map(Goods::getGoodsTypeId).collect(Collectors.toSet());
            Map<Long, GoodsType> goodsTypeMap = new HashMap<>();
            if (!goodsTypeIdSet.isEmpty()){
                goodsTypeMap = goodsTypeService.findMapByIds(new ArrayList<>(goodsTypeIdSet));
            }

            Set<Long> goodsIdSet = goodsList.stream().map(Goods::getId).collect(Collectors.toSet());
            Map<Long, List<GoodsSpecificationType>> goodsSpecificationTypeMap = new HashMap<>();
            Map<Long, List<GoodsSpecificationValue>> goodsSpecificationValueMap = new HashMap<>();
            if (!goodsIdSet.isEmpty()){
                for (Long goodsId : goodsIdSet) {
                    GoodsSpecificationTypeQuery goodsSpecificationTypeQuery = new GoodsSpecificationTypeQuery();
                    goodsSpecificationTypeQuery.setGoodsId(goodsId);
                    goodsSpecificationTypeQuery.setStatus(DataStatus.Y.getCode());
                    List<GoodsSpecificationType> goodsSpecificationTypes = goodsSpecificationTypeService.findAll(goodsSpecificationTypeQuery);
                    goodsSpecificationTypeMap.put(goodsId,goodsSpecificationTypes);
                    if (!goodsSpecificationTypes.isEmpty()){
                        List<Long> goodsSpecificationTypeIds = goodsSpecificationTypes.stream().map(GoodsSpecificationType::getId).collect(Collectors.toList());
                        GoodsSpecificationValueQuery goodsSpecificationValueQuery = new GoodsSpecificationValueQuery();
                        goodsSpecificationValueQuery.setGoodsId(goodsId);
                        goodsSpecificationValueQuery.setGoodsSpecificationTypeIds(goodsSpecificationTypeIds);
                        goodsSpecificationValueQuery.setStatus(DataStatus.Y.getCode());
                        List<GoodsSpecificationValue> goodsSpecificationValues = goodsSpecificationValueService.findAll(goodsSpecificationValueQuery);
                        for (GoodsSpecificationValue goodsSpecificationValue : goodsSpecificationValues) {
                            if (!goodsSpecificationValueMap.containsKey(goodsSpecificationValue.getGoodsSpecificationTypeId())) {
                                goodsSpecificationValueMap.put(goodsSpecificationValue.getGoodsSpecificationTypeId(),new ArrayList<>());
                            }
                            goodsSpecificationValueMap.get(goodsSpecificationValue.getGoodsSpecificationTypeId()).add(goodsSpecificationValue);
                        }
                    }
                }
            }
            for (Goods goods : goodsList) {
                GoodsResponse goodsResponse = new GoodsResponse(goods);
                if (goodsTypeMap.containsKey(goods.getGoodsTypeId())) {
                    goodsResponse.setGoodsTypeName(goodsTypeMap.get(goods.getGoodsTypeId()).getName());
                }
                List<GoodsSpecificationTypeResponse> goodsSpecificationTypeResponses = new ArrayList<>();
                if (goodsSpecificationTypeMap.containsKey(goods.getId())) {
                    for (GoodsSpecificationType goodsSpecificationType : goodsSpecificationTypeMap.get(goods.getId())) {
                        GoodsSpecificationTypeResponse goodsSpecificationTypeResponse = new GoodsSpecificationTypeResponse(goodsSpecificationType);
                        if (goodsSpecificationValueMap.containsKey(goodsSpecificationType.getId())) {
                            List<GoodsSpecificationValue> goodsSpecificationValues = goodsSpecificationValueMap.get(goodsSpecificationType.getId());
                            List<GoodsSpecificationValueResponse> goodsSpecificationValueResponses = new ArrayList<>();
                            for (GoodsSpecificationValue goodsSpecificationValue : goodsSpecificationValues) {
                                GoodsSpecificationValueResponse goodsSpecificationValueResponse = new GoodsSpecificationValueResponse(goodsSpecificationValue);
                                goodsSpecificationValueResponse.setGoodsSpecificationTypeName(goodsSpecificationType.getName());
                                goodsSpecificationValueResponses.add(goodsSpecificationValueResponse);
                            }
                            goodsSpecificationTypeResponse.setGoodsSpecificationValueResponses(goodsSpecificationValueResponses);
                        }
                        goodsSpecificationTypeResponses.add(goodsSpecificationTypeResponse);
                    }
                }
                goodsResponse.setGoodsSpecificationTypeResponses(goodsSpecificationTypeResponses);
                goodsResponses.add(goodsResponse);
            }
            return new Response<>(OK, SUCCESS, goodsResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "商品id查询")
    @RequestMapping(value = "/v1/goods/id/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<GoodsResponse> queryById(@RequestBody GoodsRequest request){
        try {
            GoodsValidator validator = new GoodsValidator();
            if (!validator
                    .onId(request.getId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            Goods goods = goodsService.findById(request.getId());

            if (goods == null) {
                return new Response<>(ERROR, "商品不存在");
            }

            //查询商品规格类型
            List<GoodsSpecificationTypeResponse> goodsSpecificationTypeResponses = new ArrayList<>();
            GoodsSpecificationTypeQuery goodsSpecificationTypeQuery = new GoodsSpecificationTypeQuery();
            goodsSpecificationTypeQuery.setGoodsId(goods.getId());
            goodsSpecificationTypeQuery.setStatus(DataStatus.Y.getCode());
            List<GoodsSpecificationType> goodsSpecificationTypes = goodsSpecificationTypeService.findAll(goodsSpecificationTypeQuery);
            Map<Long, GoodsSpecificationType> goodsSpecificationTypeMap = goodsSpecificationTypes.stream().collect(Collectors.toMap(GoodsSpecificationType::getId, Function.identity()));

            //查询商品规格值
            GoodsSpecificationValueQuery goodsSpecificationValueQuery = new GoodsSpecificationValueQuery();
            goodsSpecificationValueQuery.setGoodsId(goods.getId());
            goodsSpecificationValueQuery.setStatus(DataStatus.Y.getCode());
            List<GoodsSpecificationValue> goodsSpecificationValues = goodsSpecificationValueService.findAll(goodsSpecificationValueQuery);

            //查询商品规格sku
            List<GoodsSpecificationSkuResponse> goodsSpecificationSkuResponses = new ArrayList<>();
            GoodsSpecificationSkuQuery goodsSpecificationSkuQuery = new GoodsSpecificationSkuQuery();
            goodsSpecificationSkuQuery.setGoodsId(goods.getId());
            goodsSpecificationSkuQuery.setStatus(DataStatus.Y.getCode());
            List<GoodsSpecificationSku> goodsSpecificationSkus = goodsSpecificationSkuService.findAll(goodsSpecificationSkuQuery);

            //商品规格值抓换为map，key为类型id
            Map<Long, List<GoodsSpecificationValueResponse>> goodsSpecificationValueMap = new HashMap<>();
            if (!goodsSpecificationValues.isEmpty()){
                for (GoodsSpecificationValue goodsSpecificationValue : goodsSpecificationValues) {
                    if (!goodsSpecificationValueMap.containsKey(goodsSpecificationValue.getGoodsSpecificationTypeId())) {
                        goodsSpecificationValueMap.put(goodsSpecificationValue.getGoodsSpecificationTypeId(),new ArrayList<>());
                    }
                    GoodsSpecificationValueResponse goodsSpecificationValueResponse = new GoodsSpecificationValueResponse(goodsSpecificationValue);
                    if (goodsSpecificationTypeMap.containsKey(goodsSpecificationValue.getGoodsSpecificationTypeId())) {
                        goodsSpecificationValueResponse.setGoodsSpecificationTypeName(goodsSpecificationTypeMap.get(goodsSpecificationValue.getGoodsSpecificationTypeId()).getName());
                    }
                    goodsSpecificationValueMap.get(goodsSpecificationValue.getGoodsSpecificationTypeId()).add(goodsSpecificationValueResponse);
                }
            }
            if (!goodsSpecificationTypes.isEmpty()){
                for (GoodsSpecificationType goodsSpecificationType : goodsSpecificationTypes) {
                    GoodsSpecificationTypeResponse goodsSpecificationTypeResponse = new GoodsSpecificationTypeResponse(goodsSpecificationType);
                    if (goodsSpecificationValueMap.containsKey(goodsSpecificationType.getId())) {
                        goodsSpecificationTypeResponse.setGoodsSpecificationValueResponses(goodsSpecificationValueMap.get(goodsSpecificationType.getId()));
                    }else {
                        goodsSpecificationTypeResponse.setGoodsSpecificationValueResponses(new ArrayList<>());
                    }
                    goodsSpecificationTypeResponses.add(goodsSpecificationTypeResponse);
                }
            }

            if (!goodsSpecificationSkus.isEmpty()){
                for (GoodsSpecificationSku goodsSpecificationSku : goodsSpecificationSkus) {
                    goodsSpecificationSkuResponses.add(new GoodsSpecificationSkuResponse(goodsSpecificationSku));
                }
            }

            GoodsResponse goodsResponse = new GoodsResponse(goods);
            GoodsType goodsType = goodsTypeService.findById(goods.getGoodsTypeId());
            if (goodsType != null) {
                goodsResponse.setGoodsTypeName(goodsType.getName());
            }

            goodsResponse.setGoodsSpecificationTypeResponses(goodsSpecificationTypeResponses);
            goodsResponse.setGoodsSpecificationSkuResponses(goodsSpecificationSkuResponses);

            return new Response<>(OK, SUCCESS, goodsResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    @Operation(summary = "商品新增")
    @RequestMapping(value = "/v1/goods/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> create(@RequestBody GoodsRequest request){
        try {
            Date serverTime = this.getServerTime();
            GoodsValidator validator = new GoodsValidator();
            if (!validator
                    .onName(request.getName())
                    .onUnit(request.getUnit())
                    .onImgUrl(request.getImgUrl())
                    .onCatalog(request.getCatalog())
                    .onOpenCommission(request.getOpenCommission())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            //判断规格类型是否为空
            if (this.isEmpty(request.getGoodsSpecificationTypeRequests()) || request.getGoodsSpecificationTypeRequests().isEmpty()) {
                return new Response<>(ERROR, "规格类型不能为空");
            }
            //判断规格sku是否为空
            if (this.isEmpty(request.getGoodsSpecificationSkuRequests()) || request.getGoodsSpecificationSkuRequests().isEmpty()) {
                return new Response<>(ERROR, "规格sku不能为空");
            }
            //团长佣金和销售老师佣金比例累加不能大于100
            if (this.isEmpty(request.getCommissionRatio())) {
                request.setCommissionRatio(BigDecimal.ZERO);
            }
            if (this.isEmpty(request.getSalesTeacherCommissionRatio())) {
                request.setSalesTeacherCommissionRatio(BigDecimal.ZERO);
            }
            if (request.getCommissionRatio().add(request.getSalesTeacherCommissionRatio()).compareTo(new BigDecimal("100")) > 0) {
                return new Response<>(ERROR, "团长佣金和销售老师佣金比例累加不能大于100");
            }

            Goods goods = new Goods();
            goods.setName(request.getName());
            goods.setUnit(request.getUnit());
            goods.setImgUrl(request.getImgUrl());
            goods.setCatalog(request.getCatalog());
            goods.setGoodsTypeId(request.getGoodsTypeId());
            goods.setBuyingPrice(request.getGoodsSpecificationSkuRequests().get(0).getBuyingPrice());
            goods.setOriginalPrice(request.getGoodsSpecificationSkuRequests().get(0).getOriginalPrice());
            goods.setPrice(request.getGoodsSpecificationSkuRequests().get(0).getPrice());
            goods.setGrossProfit(request.getGoodsSpecificationSkuRequests().get(0).getPrice().subtract(request.getGoodsSpecificationSkuRequests().get(0).getBuyingPrice()));
            goods.setOpenCommission(request.getOpenCommission());
            goods.setCommissionRatio(request.getCommissionRatio() != null ? request.getCommissionRatio() : BigDecimal.ZERO);
            goods.setSalesTeacherCommissionRatio(request.getSalesTeacherCommissionRatio() != null ? request.getSalesTeacherCommissionRatio() : BigDecimal.ZERO);
            goods.setDetailImgUrl(request.getDetailImgUrl());
            goods.setComment(request.getComment());
            goods.setSalesVolume(request.getSalesVolume() != null ? request.getSalesVolume() : 0);
            goods.setRealSalesVolume(0);
            goods.setHome(request.getHome());
            goods.setStage(request.getStage());
            goods.setStatus(DataStatus.Y.getCode());
            goods.setModifyTime(serverTime);
            goods.setCreateTime(serverTime);

            //构建规格类型实体分组map
            LinkedHashMap<GoodsSpecificationType,List<GoodsSpecificationValue>> goodsSpecificationTypeMap = new LinkedHashMap<>();
            List<String> typeNameList = new ArrayList<>();
            for (GoodsSpecificationTypeRequest goodsSpecificationTypeRequest : request.getGoodsSpecificationTypeRequests()) {
                if (this.isEmpty(goodsSpecificationTypeRequest.getName())){
                    return new Response<>(ERROR, "规格类型名称不能为空");
                }
                if (this.isEmpty(goodsSpecificationTypeRequest.getGoodsSpecificationValueRequests()) || goodsSpecificationTypeRequest.getGoodsSpecificationValueRequests().isEmpty()) {
                    return new Response<>(ERROR, "规格值不能为空");
                }
                if (typeNameList.contains(goodsSpecificationTypeRequest.getName())){
                    return new Response<>(ERROR, "规格类型名称不能重复");
                }
                typeNameList.add(goodsSpecificationTypeRequest.getName());
                GoodsSpecificationType goodsSpecificationType = new GoodsSpecificationType();
                goodsSpecificationType.setName(goodsSpecificationTypeRequest.getName());
                goodsSpecificationType.setStatus(DataStatus.Y.getCode());
                goodsSpecificationType.setModifyTime(serverTime);
                goodsSpecificationType.setCreateTime(serverTime);

                List<GoodsSpecificationValue> goodsSpecificationValues = new ArrayList<>();
                List<String> valueNameList = new ArrayList<>();
                for (GoodsSpecificationValueRequest goodsSpecificationValueRequest : goodsSpecificationTypeRequest.getGoodsSpecificationValueRequests()) {
                    if (this.isEmpty(goodsSpecificationValueRequest.getName())){
                        return new Response<>(ERROR, "规格值名称不能为空");
                    }
                    if (valueNameList.contains(goodsSpecificationValueRequest.getName())){
                        return new Response<>(ERROR, "规格值名称不能重复");
                    }
                    valueNameList.add(goodsSpecificationValueRequest.getName());
                    GoodsSpecificationValue goodsSpecificationValue = new GoodsSpecificationValue();
                    goodsSpecificationValue.setName(goodsSpecificationValueRequest.getName());
                    goodsSpecificationValue.setStatus(DataStatus.Y.getCode());
                    goodsSpecificationValue.setModifyTime(serverTime);
                    goodsSpecificationValue.setCreateTime(serverTime);
                    goodsSpecificationValues.add(goodsSpecificationValue);
                }
                goodsSpecificationTypeMap.put(goodsSpecificationType,goodsSpecificationValues);
            }

            List<GoodsSpecificationSku> goodsSpecificationSkus = new ArrayList<>();
            for (GoodsSpecificationSkuRequest goodsSpecificationSkuRequest : request.getGoodsSpecificationSkuRequests()) {
                GoodsSpecificationSkuValidator goodsSpecificationSkuValidator = new GoodsSpecificationSkuValidator();
                if (!goodsSpecificationSkuValidator
                        .onSpecValues(goodsSpecificationSkuRequest.getSpecValues())
                        .onPrice(goodsSpecificationSkuRequest.getPrice())
                        .onBuyingPrice(goodsSpecificationSkuRequest.getBuyingPrice())
                        .onOriginalPrice(goodsSpecificationSkuRequest.getOriginalPrice())
                        .onStock(goodsSpecificationSkuRequest.getStock())
                        .result()){
                    return new Response<>(ERROR, goodsSpecificationSkuValidator.getErrorMessage());
                }
                GoodsSpecificationSku goodsSpecificationSku = new GoodsSpecificationSku();
                goodsSpecificationSku.setSpecValues(goodsSpecificationSkuRequest.getSpecValues());
                goodsSpecificationSku.setImgUrl(goodsSpecificationSkuRequest.getImgUrl());
                goodsSpecificationSku.setPrice(goodsSpecificationSkuRequest.getPrice());
                goodsSpecificationSku.setBuyingPrice(goodsSpecificationSkuRequest.getBuyingPrice());
                goodsSpecificationSku.setOriginalPrice(goodsSpecificationSkuRequest.getOriginalPrice());
                goodsSpecificationSku.setGrossProfit(goodsSpecificationSku.getPrice().subtract(goodsSpecificationSku.getBuyingPrice()));
                goodsSpecificationSku.setStock(goodsSpecificationSkuRequest.getStock());
                goodsSpecificationSku.setStatus(DataStatus.Y.getCode());
                goodsSpecificationSku.setModifyTime(serverTime);
                goodsSpecificationSku.setCreateTime(serverTime);
                goodsSpecificationSkus.add(goodsSpecificationSku);
            }
            goodsService.create(goods,goodsSpecificationTypeMap,goodsSpecificationSkus);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "商品修改")
    @RequestMapping(value = "/v1/goods/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modify(@RequestBody GoodsRequest request){
        try {
            Date serverTime = this.getServerTime();
            GoodsValidator validator = new GoodsValidator();
            if (!validator
                    .onId(request.getId())
                    .onName(request.getName())
                    .onUnit(request.getUnit())
                    .onImgUrl(request.getImgUrl())
                    .onCatalog(request.getCatalog())
                    .onOpenCommission(request.getOpenCommission())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            //判断规格类型是否为空
            if (this.isEmpty(request.getGoodsSpecificationTypeRequests()) || request.getGoodsSpecificationTypeRequests().isEmpty()) {
                return new Response<>(ERROR, "规格类型不能为空");
            }
            //判断规格sku是否为空
            if (this.isEmpty(request.getGoodsSpecificationSkuRequests()) || request.getGoodsSpecificationSkuRequests().isEmpty()) {
                return new Response<>(ERROR, "规格sku不能为空");
            }
            //团长佣金和销售老师佣金比例累加不能大于100
            if (this.isEmpty(request.getCommissionRatio())) {
                request.setCommissionRatio(BigDecimal.ZERO);
            }
            if (this.isEmpty(request.getSalesTeacherCommissionRatio())) {
                request.setSalesTeacherCommissionRatio(BigDecimal.ZERO);
            }
            if (request.getCommissionRatio().add(request.getSalesTeacherCommissionRatio()).compareTo(new BigDecimal("100")) > 0) {
                return new Response<>(ERROR, "团长佣金和销售老师佣金比例累加不能大于100");
            }

            Goods goods = new Goods();
            goods.setId(request.getId());
            goods.setName(request.getName());
            goods.setUnit(request.getUnit());
            goods.setImgUrl(request.getImgUrl());
            goods.setCatalog(request.getCatalog());
            goods.setGoodsTypeId(request.getGoodsTypeId());
            goods.setBuyingPrice(request.getGoodsSpecificationSkuRequests().get(0).getBuyingPrice());
            goods.setOriginalPrice(request.getGoodsSpecificationSkuRequests().get(0).getOriginalPrice());
            goods.setPrice(request.getGoodsSpecificationSkuRequests().get(0).getPrice());
            goods.setGrossProfit(request.getGoodsSpecificationSkuRequests().get(0).getPrice().subtract(request.getGoodsSpecificationSkuRequests().get(0).getBuyingPrice()));
            goods.setOpenCommission(request.getOpenCommission());
            goods.setCommissionRatio(request.getCommissionRatio() != null ? request.getCommissionRatio() : BigDecimal.ZERO);
            goods.setSalesTeacherCommissionRatio(request.getSalesTeacherCommissionRatio() != null ? request.getSalesTeacherCommissionRatio() : BigDecimal.ZERO);
            goods.setDetailImgUrl(request.getDetailImgUrl());
            goods.setComment(request.getComment());
            goods.setSalesVolume(request.getSalesVolume() != null ? request.getSalesVolume() : 0);
            goods.setRealSalesVolume(0);
            goods.setHome(request.getHome());
            goods.setStage(request.getStage());
            goods.setModifyTime(serverTime);

            //构建规格类型实体分组map
            LinkedHashMap<GoodsSpecificationType,List<GoodsSpecificationValue>> goodsSpecificationTypeMap = new LinkedHashMap<>();
            List<String> typeNameList = new ArrayList<>();
            for (GoodsSpecificationTypeRequest goodsSpecificationTypeRequest : request.getGoodsSpecificationTypeRequests()) {
                if (this.isEmpty(goodsSpecificationTypeRequest.getName())){
                    return new Response<>(ERROR, "规格类型名称不能为空");
                }
                if (this.isEmpty(goodsSpecificationTypeRequest.getGoodsSpecificationValueRequests()) || goodsSpecificationTypeRequest.getGoodsSpecificationValueRequests().isEmpty()) {
                    return new Response<>(ERROR, "规格值不能为空");
                }
                if (typeNameList.contains(goodsSpecificationTypeRequest.getName())){
                    return new Response<>(ERROR, "规格类型名称不能重复");
                }
                typeNameList.add(goodsSpecificationTypeRequest.getName());
                GoodsSpecificationType goodsSpecificationType = new GoodsSpecificationType();
                goodsSpecificationType.setId(goodsSpecificationTypeRequest.getId());
                goodsSpecificationType.setGoodsId(goods.getId());
                goodsSpecificationType.setName(goodsSpecificationTypeRequest.getName());
                goodsSpecificationType.setModifyTime(serverTime);

                List<GoodsSpecificationValue> goodsSpecificationValues = new ArrayList<>();
                List<String> valueNameList = new ArrayList<>();
                for (GoodsSpecificationValueRequest goodsSpecificationValueRequest : goodsSpecificationTypeRequest.getGoodsSpecificationValueRequests()) {
                    if (this.isEmpty(goodsSpecificationValueRequest.getName())){
                        return new Response<>(ERROR, "规格值名称不能为空");
                    }
                    if (valueNameList.contains(goodsSpecificationValueRequest.getName())){
                        return new Response<>(ERROR, "规格值名称不能重复");
                    }
                    valueNameList.add(goodsSpecificationValueRequest.getName());
                    GoodsSpecificationValue goodsSpecificationValue = new GoodsSpecificationValue();
                    goodsSpecificationValue.setId(goodsSpecificationValueRequest.getId());
                    goodsSpecificationValue.setGoodsId(goods.getId());
                    goodsSpecificationValue.setGoodsSpecificationTypeId(goodsSpecificationType.getId());
                    goodsSpecificationValue.setName(goodsSpecificationValueRequest.getName());
                    goodsSpecificationValue.setModifyTime(serverTime);
                    goodsSpecificationValues.add(goodsSpecificationValue);
                }
                goodsSpecificationTypeMap.put(goodsSpecificationType,goodsSpecificationValues);
            }

            List<GoodsSpecificationSku> goodsSpecificationSkus = new ArrayList<>();
            for (GoodsSpecificationSkuRequest goodsSpecificationSkuRequest : request.getGoodsSpecificationSkuRequests()) {
                GoodsSpecificationSkuValidator goodsSpecificationSkuValidator = new GoodsSpecificationSkuValidator();
                if (!goodsSpecificationSkuValidator
                        .onSpecValues(goodsSpecificationSkuRequest.getSpecValues())
                        .onPrice(goodsSpecificationSkuRequest.getPrice())
                        .onBuyingPrice(goodsSpecificationSkuRequest.getBuyingPrice())
                        .onOriginalPrice(goodsSpecificationSkuRequest.getOriginalPrice())
                        .onStock(goodsSpecificationSkuRequest.getStock())
                        .result()){
                    return new Response<>(ERROR, goodsSpecificationSkuValidator.getErrorMessage());
                }
                GoodsSpecificationSku goodsSpecificationSku = new GoodsSpecificationSku();
                goodsSpecificationSku.setId(goodsSpecificationSkuRequest.getId());
                goodsSpecificationSku.setGoodsId(goods.getId());
                goodsSpecificationSku.setSpecValues(goodsSpecificationSkuRequest.getSpecValues());
                goodsSpecificationSku.setImgUrl(goodsSpecificationSkuRequest.getImgUrl());
                goodsSpecificationSku.setPrice(goodsSpecificationSkuRequest.getPrice());
                goodsSpecificationSku.setBuyingPrice(goodsSpecificationSkuRequest.getBuyingPrice());
                goodsSpecificationSku.setOriginalPrice(goodsSpecificationSkuRequest.getOriginalPrice());
                goodsSpecificationSku.setGrossProfit(goodsSpecificationSku.getPrice().subtract(goodsSpecificationSku.getBuyingPrice()));
                goodsSpecificationSku.setStock(goodsSpecificationSkuRequest.getStock());
                goodsSpecificationSku.setModifyTime(serverTime);
                goodsSpecificationSkus.add(goodsSpecificationSku);
            }

            goodsService.modify(goods,goodsSpecificationTypeMap,goodsSpecificationSkus);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "商品删除")
    @RequestMapping(value = "/v1/goods/remove",method = {RequestMethod.POST})
    @ResponseBody
    public Response<?> remove(@RequestBody GoodsRequest request){
        try {
            Date serverTime = this.getServerTime();
            GoodsValidator validator = new GoodsValidator();
            if (!validator
                    .onId(request.getId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            Goods goods = new Goods();
            goods.setId(request.getId());
            goods.setStatus(DataStatus.N.getCode());
            goods.setModifyTime(serverTime);

            //删除这个商品下类型、类型值
            GoodsSpecificationTypeQuery goodsSpecificationTypeQuery = new GoodsSpecificationTypeQuery();
            goodsSpecificationTypeQuery.setGoodsId(goods.getId());
            List<GoodsSpecificationType> goodsSpecificationTypes = goodsSpecificationTypeService.findAll(goodsSpecificationTypeQuery);
            goodsSpecificationTypes.forEach(goodsSpecificationType -> {
                goodsSpecificationType.setStatus(DataStatus.N.getCode());
                goodsSpecificationType.setModifyTime(serverTime);
            });

            GoodsSpecificationValueQuery goodsSpecificationValueQuery = new GoodsSpecificationValueQuery();
            goodsSpecificationValueQuery.setGoodsId(goods.getId());
            List<GoodsSpecificationValue> goodsSpecificationValues = goodsSpecificationValueService.findAll(goodsSpecificationValueQuery);
            goodsSpecificationValues.forEach(goodsSpecificationValue -> {
                goodsSpecificationValue.setStatus(DataStatus.N.getCode());
                goodsSpecificationValue.setModifyTime(serverTime);
            });

            GoodsSpecificationSkuQuery goodsSpecificationSkuQuery = new GoodsSpecificationSkuQuery();
            goodsSpecificationSkuQuery.setGoodsId(goods.getId());
            List<GoodsSpecificationSku> goodsSpecificationSkus = goodsSpecificationSkuService.findAll(goodsSpecificationSkuQuery);
            goodsSpecificationSkus.forEach(goodsSpecificationSku -> {
                goodsSpecificationSku.setStatus(DataStatus.N.getCode());
                goodsSpecificationSku.setModifyTime(serverTime);
            });

            goodsService.remove(goods,goodsSpecificationTypes,goodsSpecificationValues,goodsSpecificationSkus);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "商品上下架")
    @RequestMapping(value = "/v1/goods/stage/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modifyStage(@RequestBody GoodsRequest request){
        try {
            Date serverTime = this.getServerTime();
            GoodsValidator validator = new GoodsValidator();
            if (!validator
                    .onId(request.getId())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            Goods goods = new Goods();
            goods.setId(request.getId());
            goods.setStage(request.getStage());
            goods.setModifyTime(serverTime);
            goodsService.modifyById(goods);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
