package com.api.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.Lock;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.UserCommissionRequest;
import com.api.bean.UserCommissionResponse;
import com.api.config.Token;
import com.common.bean.Response;
import com.common.bean.UserToken;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.UserCommissionFlowCatalog;
import com.common.constant.UserCommissionFlowIncome;
import com.common.util.DateUtil;
import com.common.util.StringUtil;
import com.domain.User;
import com.domain.UserCommission;
import com.domain.UserCommissionCash;
import com.domain.UserCommissionCashFlow;
import com.domain.UserCommissionFlow;
import com.domain.complex.UserCommissionFlowQuery;
import com.domain.complex.UserCommissionQuery;
import com.service.UserCommissionFlowService;
import com.service.UserCommissionService;
import com.service.UserService;


@Tag(name = "用户佣金表")
@RestController
public class UserCommissionController extends BaseController {
    @Autowired
    private UserCommissionService userCommissionService;
    @Autowired
    private UserCommissionFlowService userCommissionFlowService;
    @Autowired
    private UserService userService;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    @Operation(summary = "单独用户全额提现")
    @RequestMapping(value = "/v1/user/commission/withdraw/full", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> withdrawFull(@RequestBody UserCommissionRequest request) {
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_COMMISSION, request.getUserId().toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "当前用户分佣产生变化，请稍后再试");
        }
        try {
            Date serverTime = this.getServerTime();
            UserToken userToken = this.getUserToken();

            // 参数验证
            if (this.isEmpty(request.getUserId())) {
                return new Response<>(ERROR, "用户ID不能为空");
            }

            // 验证用户是否存在
            User user = userService.findById(request.getUserId());
            if (user == null) {
                return new Response<>(ERROR, "用户不存在");
            }

            if (!DataStatus.Y.getCode().equals(user.getStatus())) {
                return new Response<>(ERROR, "用户状态异常，无法提现");
            }

            // 查询用户佣金信息
            UserCommissionQuery commissionQuery = new UserCommissionQuery();
            commissionQuery.setUserId(request.getUserId());
            commissionQuery.setStatus(DataStatus.Y.getCode());
            List<UserCommission> commissions = userCommissionService.findAll(commissionQuery);

            if (commissions.isEmpty()) {
                return new Response<>(ERROR, "用户佣金账户不存在");
            }

            UserCommission userCommission = commissions.get(0);

            // 验证是否有可提现余额
            if (userCommission.getAmount() == null || userCommission.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                return new Response<>(ERROR, "用户无可提现余额");
            }

            // 全额提现金额
            BigDecimal withdrawAmount = userCommission.getAmount();

            // 生成流水号
            String flowCode = this.getFlowCode();

            // 更新用户佣金余额为0
            UserCommission updateCommission = new UserCommission();
            updateCommission.setId(userCommission.getId());
            updateCommission.setAmount(BigDecimal.ZERO);
            updateCommission.setModifyTime(serverTime);

            // 创建提现流水记录
            UserCommissionFlow commissionFlow = new UserCommissionFlow();
            commissionFlow.setFlowCode(flowCode);
            commissionFlow.setUserId(request.getUserId());
            commissionFlow.setTransactionTime(serverTime);
            commissionFlow.setFlowAmount(BigDecimal.ZERO.subtract(withdrawAmount));
            commissionFlow.setAmount(userCommission.getAmount().subtract(withdrawAmount)); // 提现后余额为0
            commissionFlow.setCatalog(UserCommissionFlowCatalog.C1001.getCode()); // 提现流水
            commissionFlow.setIncome(UserCommissionFlowIncome.C1.getCode()); // 支出
            commissionFlow.setLabel("管理员操作全额提现");
            commissionFlow.setComment(request.getComment() != null ? request.getComment() : "管理员全额提现操作");
            commissionFlow.setStatus(DataStatus.Y.getCode());
            commissionFlow.setModifyTime(serverTime);
            commissionFlow.setCreateTime(serverTime);

            //记录用户佣金提现和提现流水tb_user_commission_cash、tb_user_commission_cash_flow
            UserCommissionFlowQuery userCommissionFlowQuery = new UserCommissionFlowQuery();
            userCommissionFlowQuery.setUserId(request.getUserId());
            userCommissionFlowQuery.setStatus(DataStatus.Y.getCode());
            List<UserCommissionFlow> userCommissionFlowList = userCommissionFlowService.findAll(userCommissionFlowQuery);
            //按id倒序,查询分佣流水、收入的记录，截止到上一次提现
            userCommissionFlowList.sort(Comparator.comparing(UserCommissionFlow::getId).reversed());
            Set<Long> cashOrderIdSet = new HashSet<>();
            for (UserCommissionFlow userCommissionFlow : userCommissionFlowList) {
                //如果是提现记录直接结束
                if (UserCommissionFlowCatalog.C1001.getCode().equals(userCommissionFlow.getCatalog())
                        && UserCommissionFlowIncome.C1.getCode().equals(userCommissionFlow.getIncome())) {
                    break;
                }
                cashOrderIdSet.add(userCommissionFlow.getOrderId());
            }

            UserCommissionCash userCommissionCash = new UserCommissionCash();
            userCommissionCash.setUserId(request.getUserId());
            userCommissionCash.setSumCommission(withdrawAmount);
            userCommissionCash.setOrderSize(cashOrderIdSet.size());
            userCommissionCash.setOptSysUserId(userToken.getId());
            userCommissionCash.setCreateTime(serverTime);
            userCommissionCash.setModifyTime(serverTime);
            userCommissionCash.setStatus(DataStatus.Y.getCode());

            List<UserCommissionCashFlow> userCommissionCashFlowList = new ArrayList<>();
            for (Long cashOrderId : cashOrderIdSet) {
                UserCommissionCashFlow cashFlow = new UserCommissionCashFlow();
                cashFlow.setUserId(request.getUserId());
                cashFlow.setOrderId(cashOrderId.intValue());
                cashFlow.setCreateTime(serverTime);
                cashFlow.setModifyTime(serverTime);
                cashFlow.setStatus(DataStatus.Y.getCode());
                userCommissionCashFlowList.add(cashFlow);
            }

            userCommissionFlowService.create(commissionFlow,updateCommission,userCommissionCash,userCommissionCashFlowList);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }finally {
            lock.unlock();
        }
    }

    @Operation(summary = "批量用户全额提现")
    @RequestMapping(value = "/v1/user/commission/withdraw/batch", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    @Transactional
    public Response<?> withdrawBatch(@RequestBody UserCommissionRequest request) {
        try {
            Date serverTime = this.getServerTime();
            UserToken userToken = this.getUserToken();

            // 参数验证
            if (this.isEmpty(request.getUserIds()) || request.getUserIds().isEmpty()) {
                return new Response<>(ERROR, "用户ID集合不能为空");
            }

            // 查询所有用户
            List<User> users = userService.findByIds(request.getUserIds());
            if (users.isEmpty()) {
                return new Response<>(ERROR, "未找到任何用户");
            }

            // 验证用户状态
            List<String> invalidUsers = new ArrayList<>();
            for (User user : users) {
                if (!DataStatus.Y.getCode().equals(user.getStatus())) {
                    invalidUsers.add(user.getName() + "(" + user.getMobile() + ")");
                }
            }

            if (!invalidUsers.isEmpty()) {
                return new Response<>(ERROR, "以下用户状态异常，无法提现：" + String.join(", ", invalidUsers));
            }

            // 查询所有用户的佣金信息
            UserCommissionQuery commissionQuery = new UserCommissionQuery();
            commissionQuery.setUserIds(request.getUserIds());
            commissionQuery.setStatus(DataStatus.Y.getCode());
            List<UserCommission> commissions = userCommissionService.findAll(commissionQuery);

            Map<Long, UserCommission> userIdCommissionMap = new HashMap<>();
            for (UserCommission commission : commissions) {
                userIdCommissionMap.put(commission.getUserId(), commission);
            }

            // 验证余额
            List<String> noBalanceUsers = new ArrayList<>();
            for (User user : users) {
                UserCommission userCommission = userIdCommissionMap.get(user.getId());

                if (userCommission == null) {
                    noBalanceUsers.add(user.getName() + "(佣金账户不存在)");
                } else if (userCommission.getAmount() == null ||
                          userCommission.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    noBalanceUsers.add(user.getName() + "(无可提现余额)");
                }
            }

            if (!noBalanceUsers.isEmpty()) {
                return new Response<>(ERROR, "以下用户无法提现：" + String.join(", ", noBalanceUsers));
            }

            // 执行批量提现
            List<String> noLockUsers = new ArrayList<>();

            BigDecimal totalWithdrawAmount = BigDecimal.ZERO;
            for (User user : users) {
                //获取当前用户锁
                Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_COMMISSION, user.getId().toString());
                if (!lock.tryLock()) {
                    noLockUsers.add(user.getName() + "(" + user.getMobile() + "):用户分佣产生变化,提现失败,请重新提现");
                    continue;
                }


                try {
                    UserCommission userCommission = userIdCommissionMap.get(user.getId());

                    // 全额提现金额
                    BigDecimal withdrawAmount = userCommission.getAmount();
                    totalWithdrawAmount = totalWithdrawAmount.add(withdrawAmount);

                    // 生成流水号
                    String flowCode = this.getFlowCode();

                    // 准备更新佣金余额为0
                    UserCommission updateCommission = new UserCommission();
                    updateCommission.setId(userCommission.getId());
                    updateCommission.setAmount(BigDecimal.ZERO);
                    updateCommission.setModifyTime(serverTime);

                    // 准备创建提现流水记录
                    UserCommissionFlow commissionFlow = new UserCommissionFlow();
                    commissionFlow.setFlowCode(flowCode);
                    commissionFlow.setUserId(user.getId());
                    commissionFlow.setTransactionTime(serverTime);
                    commissionFlow.setFlowAmount(BigDecimal.ZERO.subtract(withdrawAmount));
                    commissionFlow.setAmount(userCommission.getAmount().subtract(withdrawAmount)); // 提现后余额为0
                    commissionFlow.setCatalog(UserCommissionFlowCatalog.C1001.getCode()); // 提现流水
                    commissionFlow.setIncome(UserCommissionFlowIncome.C1.getCode()); // 支出
                    commissionFlow.setLabel("管理员操作全额提现");
                    commissionFlow.setComment(request.getComment() != null ? request.getComment() : "管理员操作全额提现");
                    commissionFlow.setStatus(DataStatus.Y.getCode());
                    commissionFlow.setModifyTime(serverTime);
                    commissionFlow.setCreateTime(serverTime);

                    //记录用户佣金提现和提现流水tb_user_commission_cash、tb_user_commission_cash_flow
                    UserCommissionFlowQuery userCommissionFlowQuery = new UserCommissionFlowQuery();
                    userCommissionFlowQuery.setUserId(request.getUserId());
                    userCommissionFlowQuery.setStatus(DataStatus.Y.getCode());
                    List<UserCommissionFlow> userCommissionFlowList = userCommissionFlowService.findAll(userCommissionFlowQuery);
                    //按id倒序,查询分佣流水、收入的记录，截止到上一次提现
                    userCommissionFlowList.sort(Comparator.comparing(UserCommissionFlow::getId).reversed());
                    Set<Long> cashOrderIdSet = new HashSet<>();
                    for (UserCommissionFlow userCommissionFlow : userCommissionFlowList) {
                        //如果是提现记录直接结束
                        if (UserCommissionFlowCatalog.C1001.getCode().equals(userCommissionFlow.getCatalog())
                                && UserCommissionFlowIncome.C1.getCode().equals(userCommissionFlow.getIncome())) {
                            break;
                        }
                        cashOrderIdSet.add(userCommissionFlow.getOrderId());
                    }

                    UserCommissionCash userCommissionCash = new UserCommissionCash();
                    userCommissionCash.setUserId(user.getId());
                    userCommissionCash.setSumCommission(withdrawAmount);
                    userCommissionCash.setOrderSize(cashOrderIdSet.size());
                    userCommissionCash.setOptSysUserId(userToken.getId());
                    userCommissionCash.setCreateTime(serverTime);
                    userCommissionCash.setModifyTime(serverTime);
                    userCommissionCash.setStatus(DataStatus.Y.getCode());

                    List<UserCommissionCashFlow> userCommissionCashFlowList = new ArrayList<>();
                    for (Long cashOrderId : cashOrderIdSet) {
                        UserCommissionCashFlow cashFlow = new UserCommissionCashFlow();
                        cashFlow.setUserId(user.getId());
                        cashFlow.setOrderId(cashOrderId.intValue());
                        cashFlow.setCreateTime(serverTime);
                        cashFlow.setModifyTime(serverTime);
                        cashFlow.setStatus(DataStatus.Y.getCode());
                        userCommissionCashFlowList.add(cashFlow);
                    }
                    userCommissionFlowService.create(commissionFlow,updateCommission,userCommissionCash,userCommissionCashFlowList);
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                } finally {
                    lock.unlock();
                }
            }

            if (!noLockUsers.isEmpty()) {
                return new Response<>(OK, "以下用户无法提现：" + String.join(", ", noLockUsers) + ", 其他用户提现成功");
            }
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }
}
