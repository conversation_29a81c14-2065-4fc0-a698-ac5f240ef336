package com.api.controller;

import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.GoodsRequest;
import com.api.bean.GoodsResponse;
import com.api.bean.GoodsSpecificationSkuRequest;
import com.api.bean.GoodsSpecificationSkuResponse;
import com.api.bean.PageResponse;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.GoodsSpecificationSku;
import com.domain.complex.GoodsSpecificationSkuQuery;
import com.service.GoodsSpecificationSkuService;


@Tag(name = "商品规格sku表")
@RestController
public class GoodsSpecificationSkuController extends BaseController {
    @Autowired
    private GoodsSpecificationSkuService goodsSpecificationSkuService;

    @Operation(summary = "根据商品编号、sku属性查询sku信息")
    @RequestMapping(value = "/v1/goods/specification/sku/goods/id/spec/values/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<GoodsSpecificationSkuResponse> queryByGoodsIdAndSpecValues(@RequestBody GoodsSpecificationSkuRequest request){
        try {
            //根据商品编号和SKU属性查询
            if (this.isEmpty(request.getGoodsId())){
                return new Response<>(ERROR, "商品编号不能为空");
            }
            if (this.isEmpty(request.getSpecValues())){
                return new Response<>(ERROR, "SKU属性不能为空");
            }
            GoodsSpecificationSkuQuery goodsSpecificationSkuQuery = new GoodsSpecificationSkuQuery();
            goodsSpecificationSkuQuery.setGoodsId(request.getGoodsId());
            goodsSpecificationSkuQuery.setSpecValues(request.getSpecValues());
            goodsSpecificationSkuQuery.setStatus(DataStatus.Y.getCode());
            List<GoodsSpecificationSku> goodsSpecificationSkus = goodsSpecificationSkuService.findAll(goodsSpecificationSkuQuery);

            if (this.isEmpty(goodsSpecificationSkus) || goodsSpecificationSkus.isEmpty()){
                return new Response<>(ERROR, "商品规格不存在");
            }

            GoodsSpecificationSkuResponse goodsSpecificationSkuResponse = new GoodsSpecificationSkuResponse(goodsSpecificationSkus.get(0));
            return new Response<>(OK, SUCCESS, goodsSpecificationSkuResponse);

        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
