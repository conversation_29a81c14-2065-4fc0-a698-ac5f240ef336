package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.PageResponse;
import com.api.bean.SysUserRequest;
import com.api.bean.SysUserResponse;
import com.api.config.Token;
import com.api.constant.App;
import com.api.validator.SysUserValidator;
import com.common.bean.JsonWebToken;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.constant.CacheKey;
import com.common.constant.DataStage;
import com.common.constant.DataStatus;
import com.common.constant.Device;
import com.common.constant.Platform;
import com.common.constant.TokenType;
import com.common.util.DateUtil;
import com.common.util.EncryptUtil;
import com.common.util.MD5Util;
import com.domain.SysUser;
import com.domain.complex.SysUserQuery;
import com.service.SysUserService;


@Tag(name = "系统用户表")
@RestController
public class SysUserController extends BaseController {
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private final int SYS_USER_LOGIN_LIMIT_MAX = 15;
    private final int SYS_USER_LOGIN_LIMIT_EXPIRE_TIME = 24;

    @Operation(summary = "系统用户登录（crm）")
    @RequestMapping(value = "/v1/sys/user/login",method = {RequestMethod.POST})
    @ResponseBody
    public Response<SysUserResponse> login(@RequestBody SysUserRequest request){
        SysUserResponse sysUserResponse = new SysUserResponse();
        try {
            SysUserValidator validator = new SysUserValidator();
            if (!validator.onUsername(request.getUsername()).onPassword(request.getPassword())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            SysUserQuery sysUserQuery = new SysUserQuery();
            sysUserQuery.setUsername(request.getUsername());
            sysUserQuery.setStatus(DataStatus.Y.getCode());
            List<SysUser> sysUserList = sysUserService.findAll(sysUserQuery);
            if (this.isEmpty(sysUserList) || sysUserList.isEmpty()) {
                return new Response<>(ERROR, "账号或密码错误");
            }
            SysUser sysUser = sysUserList.get(0);
            String key = this.append(CacheKey.SYS_USER_LOGIN_LIMIT, sysUser.getUsername());
            String limit = redisTemplate.opsForValue().get(key);

            //判断登录次数限制
            if (!this.isEmpty(limit) && Integer.parseInt(limit) >= this.SYS_USER_LOGIN_LIMIT_MAX) {
                return new Response<>(ERROR, "超过登录错误限制次数");
            }
            // 查看是否登录成功
            if (!MD5Util.toMD5(request.getPassword()).equals(sysUser.getPassword())){
                this.redisTemplate.opsForValue().increment(key, 1);
                this.redisTemplate.expire(key, SYS_USER_LOGIN_LIMIT_EXPIRE_TIME, TimeUnit.HOURS);
                return new Response<>(ERROR, "账号或密码错误");
            }
            if (DataStatus.N.getCode().equals(sysUser.getStatus())){
                return new Response<>(ERROR, "账号无效");
            }
            //判断一下是不是禁用状态
            if (DataStage.N.getCode().equals(sysUser.getStage())){
                return new Response<>(ERROR,"账号已经被禁用，请检查");
            }

            //返回
            sysUserResponse.setId(sysUser.getId());
            sysUserResponse.setName(sysUser.getName());
            sysUserResponse.setUsername(sysUser.getUsername());
            sysUserResponse.setMobile(sysUser.getMobile());
            sysUserResponse.setToken(this.getUserToken(sysUser));

            return new Response<>(OK, SUCCESS, sysUserResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "系统用户分页")
    @RequestMapping(value = "/v1/sys/user/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<SysUserResponse>> query(@RequestBody SysUserRequest request){
        try {
            PageResponse<SysUserResponse> response = new PageResponse<>();
            SysUserQuery sysUserQuery = new SysUserQuery();
            sysUserQuery.setName(request.getName());
            sysUserQuery.setUsername(request.getUsername());
            sysUserQuery.setMobile(request.getMobile());
            sysUserQuery.setStage(request.getStage());
            sysUserQuery.setStatus(DataStatus.Y.getCode());
            if (request.getMinCreateTime() != null) {
                sysUserQuery.setMinCreateTime(DateUtil.parse(request.getCreateTime(),DATETIME_FORMAT));
            }
            if (request.getMaxCreateTime() != null) {
                sysUserQuery.setMaxCreateTime(DateUtil.parse(request.getCreateTime(),DATETIME_FORMAT));
            }
            Integer total = sysUserService.count(sysUserQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            sysUserQuery.setStart(pager.getOffset());
            sysUserQuery.setLimit(pager.getLimit());
            List<SysUser> sysUsers = sysUserService.find(sysUserQuery);
            List<SysUserResponse> sysUserResponses = new ArrayList<>();
            for (SysUser sysUser : sysUsers) {
                SysUserResponse sysUserResponse = new SysUserResponse(sysUser);
                sysUserResponses.add(sysUserResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(sysUserResponses.size());
            response.setList(sysUserResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "系统用户id查询")
    @RequestMapping(value = "/v1/sys/user/id/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<SysUserResponse> queryById(@RequestBody SysUserRequest request){
        try {
            SysUserValidator validator = new SysUserValidator();
            if (!validator.onId(request.getId()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            SysUser sysUser = sysUserService.findById(request.getId());
            if (sysUser == null){
                return new Response<>(ERROR, "用户不存在");
            }

            SysUserResponse sysUserResponse = new SysUserResponse(sysUser);
            return new Response<>(OK, SUCCESS, sysUserResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "系统用户新增")
    @RequestMapping(value = "/v1/sys/user/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> create(@RequestBody SysUserRequest request){
        try {
            Date serverTime = this.getServerTime();
            SysUserValidator validator = new SysUserValidator();
            if (!validator
                    .onUsername(request.getUsername())
                    .onName(request.getName())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            //判断用户名是否已经存在
            SysUserQuery sysUserQuery = new SysUserQuery();
            sysUserQuery.setUsername(request.getUsername());
            sysUserQuery.setStatus(DataStatus.Y.getCode());
            List<SysUser> sysUserList = sysUserService.findAll(sysUserQuery);
            if (!this.isEmpty(sysUserList) && !sysUserList.isEmpty()) {
                return new Response<>(ERROR, "用户名已经存在");
            }
            //判断手机号是否已经存在
            sysUserQuery = new SysUserQuery();
            sysUserQuery.setMobile(request.getMobile());
            sysUserQuery.setStatus(DataStatus.Y.getCode());
            sysUserList = sysUserService.findAll(sysUserQuery);
            if (!this.isEmpty(sysUserList) && !sysUserList.isEmpty()) {
                return new Response<>(ERROR, "手机号已经存在");
            }

            SysUser sysUser = new SysUser();
            sysUser.setUsername(request.getUsername());
            sysUser.setName(request.getName());
            sysUser.setPassword(App.PASSWORD);
            sysUser.setMobile(request.getMobile());
            sysUser.setStage(request.getStage());
            sysUser.setStatus(DataStatus.Y.getCode());
            sysUser.setCreateTime(serverTime);
            sysUser.setModifyTime(serverTime);
            sysUserService.create(sysUser);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "系统用户修改")
    @RequestMapping(value = "/v1/sys/user/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modify(@RequestBody SysUserRequest request){
        try {
            Date serverTime = this.getServerTime();
            SysUserValidator validator = new SysUserValidator();
            if (!validator
                    .onId(request.getId())
                    .onUsername(request.getUsername())
                    .onName(request.getName())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            //判断用户名是否已经存在
            SysUserQuery sysUserQuery = new SysUserQuery();
            sysUserQuery.setUsername(request.getUsername());
            sysUserQuery.setStatus(DataStatus.Y.getCode());
            List<SysUser> sysUserList = sysUserService.findAll(sysUserQuery);
            if (!this.isEmpty(sysUserList) && !sysUserList.isEmpty()) {
                if (!sysUserList.get(0).getId().equals(request.getId())) {
                    return new Response<>(ERROR, "用户名已经存在");
                }
            }
            //判断手机号是否已经存在
            sysUserQuery = new SysUserQuery();
            sysUserQuery.setMobile(request.getMobile());
            sysUserQuery.setStatus(DataStatus.Y.getCode());
            sysUserList = sysUserService.findAll(sysUserQuery);
            if (!this.isEmpty(sysUserList) && !sysUserList.isEmpty()) {
                if (!sysUserList.get(0).getId().equals(request.getId())) {
                    return new Response<>(ERROR, "手机号已经存在");
                }
            }

            SysUser sysUser = new SysUser();
            sysUser.setId(request.getId());
            sysUser.setUsername(request.getUsername());
            sysUser.setName(request.getName());
            sysUser.setMobile(request.getMobile());
            sysUser.setStage(request.getStage());
            sysUser.setModifyTime(serverTime);
            sysUserService.modifyById(sysUser);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "系统用户禁用修改")
    @RequestMapping(value = "/v1/sys/user/stage/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modifyStage(@RequestBody SysUserRequest request){
        try {
            Date serverTime = this.getServerTime();
            SysUserValidator validator = new SysUserValidator();
            if (!validator
                    .onId(request.getId())
                    .onStage(request.getStage())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            if (this.getUserToken().getId() != 10000000L && request.getId() == 10000000L){
                return new Response<>(ERROR, "系统用户禁止修改");
            }

            SysUser sysUser = new SysUser();
            sysUser.setId(request.getId());
            sysUser.setStage(request.getStage());
            sysUser.setModifyTime(serverTime);
            sysUserService.modifyById(sysUser);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "系统用户删除")
    @RequestMapping(value = "/v1/sys/user/remove",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> remove(@RequestBody SysUserRequest request){
        try {
            Date serverTime = this.getServerTime();
            SysUserValidator validator = new SysUserValidator();
            if (!validator
                    .onId(request.getId())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            if (request.getId() == 10000000L){
                return new Response<>(ERROR, "系统用户不能删除");
            }

            SysUser sysUser = new SysUser();
            sysUser.setId(request.getId());
            sysUser.setStatus(DataStatus.N.getCode());
            sysUser.setModifyTime(serverTime);
            sysUserService.modifyById(sysUser);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "系统用户密码修改")
    @RequestMapping(value = "/v1/sys/user/password/modify",method = {RequestMethod.POST})
    @ResponseBody
    public Response<?> modifyPassword(@RequestBody SysUserRequest request){
        try {
            Date serverTime = this.getServerTime();
            SysUserValidator validator = new SysUserValidator();
            if (!validator
                    .onPassword(request.getPassword())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            SysUser sysUser = new SysUser();
            sysUser.setId(this.getUserToken().getId());
            sysUser.setPassword(MD5Util.toMD5(request.getPassword()));
            sysUser.setModifyTime(serverTime);
            sysUserService.modifyById(sysUser);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "系统用户密码重置")
    @RequestMapping(value = "/v1/sys/user/password/reset",method = {RequestMethod.POST})
    @ResponseBody
    public Response<?> resetPassword(@RequestBody SysUserRequest request){
        try {
            Date serverTime = this.getServerTime();
            SysUserValidator validator = new SysUserValidator();
            if (!validator
                    .onId(request.getId())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            SysUser sysUser = new SysUser();
            sysUser.setId(request.getId());
            sysUser.setPassword(App.PASSWORD);
            sysUser.setModifyTime(serverTime);
            sysUserService.modifyById(sysUser);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 获取用户凭证
     */
    private String getUserToken(SysUser userDTO) {
        Date date = this.getServerTime();
        JsonWebToken jsonWebToken = new JsonWebToken();
        jsonWebToken.setId(UUID.randomUUID().toString());
        jsonWebToken.setIssuedAt(date);
        jsonWebToken.setExpiration(DateUtil.add(date, App.TOKEN_EXPIRE_UNIT, App.TOKEN_EXPIRE_NUMBER));
        jsonWebToken.setIssuer(String.valueOf(userDTO.getId()));
        jsonWebToken.setSubject(userDTO.getUsername());
        // 用户信息
        jsonWebToken.setUserId(userDTO.getId());
        jsonWebToken.setUsername(userDTO.getUsername());
        jsonWebToken.setType(TokenType.S.name());
        String token = EncryptUtil.createJwt(jsonWebToken);
        Device device = Platform.DEVIVCE.get(this.httpServletRequest.getHeader(App.HTTP_HEADER_APP_PLATFORM));
        this.redisTemplate.opsForValue().set(CacheKey.USER_TOKEN + TokenType.S.name() + device.name() + userDTO.getId(), token);
        return token;
    }

}
