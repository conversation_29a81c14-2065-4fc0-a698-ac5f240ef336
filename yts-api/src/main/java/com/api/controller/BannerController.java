package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.BannerRequest;
import com.api.bean.BannerResponse;
import com.api.bean.PageResponse;
import com.api.config.Token;
import com.api.validator.BannerValidator;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.constant.BannerJumpCatalog;
import com.common.constant.DataStatus;
import com.domain.Banner;
import com.domain.Course;
import com.domain.Goods;
import com.domain.complex.BannerQuery;
import com.service.BannerService;
import com.service.CourseService;
import com.service.GoodsService;


@Tag(name = "轮播图表")
@RestController
public class BannerController extends BaseController {
    @Autowired
    private BannerService bannerService;
    @Autowired
    private GoodsService goodsService;
    @Autowired
    private CourseService courseService;

    @Operation(summary = "轮播图分页查询")
    @RequestMapping(value = "/v1/banner/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<BannerResponse>> query(@RequestBody BannerRequest request){
        try {
            PageResponse<BannerResponse> response = new PageResponse<>();
            BannerQuery bannerQuery = new BannerQuery();
            bannerQuery.setStatus(DataStatus.Y.getCode());
            bannerQuery.setStage(request.getStage());
            bannerQuery.setJump(request.getJump());
            bannerQuery.setJumpCatalog(request.getJumpCatalog());
            Integer total = bannerService.count(bannerQuery);
            Pager pager = new Pager(total,request.getPage(),request.getLimit());
            bannerQuery.setStart(pager.getOffset());
            bannerQuery.setLimit(pager.getLimit());
            List<Banner> banners = bannerService.find(bannerQuery);

            List<Long> jumpIds = banners.stream().map(Banner::getJumpId).collect(Collectors.toList());

            Map<Long, Goods> goodsMap = new HashMap<>();
            Map<Long, Course> courseMap = new HashMap<>();
            if (!this.isEmpty(jumpIds) && !jumpIds.isEmpty()) {
                //查询商品
                goodsMap = goodsService.findMapByIds(jumpIds);
                //查询课程
                courseMap = courseService.findMapByIds(jumpIds);
            }

            List<BannerResponse> bannerResponses = new ArrayList<>();

            for (Banner banner : banners) {
                BannerResponse bannerResponse = new BannerResponse(banner);
                if (BannerJumpCatalog.C0.getCode().equals(banner.getJumpCatalog())) {
                    if (goodsMap.containsKey(banner.getJumpId())) {
                        bannerResponse.setProductName(goodsMap.get(banner.getJumpId()).getName());
                    }
                } else if (BannerJumpCatalog.C1.getCode().equals(banner.getJumpCatalog())) {
                    if (courseMap.containsKey(banner.getJumpId())) {
                        bannerResponse.setProductName(courseMap.get(banner.getJumpId()).getTitle());
                    }
                }
                bannerResponses.add(bannerResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(bannerResponses.size());
            response.setList(bannerResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "轮播图查询")
    @RequestMapping(value = "/v1/banner/all/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<List<BannerResponse>> queryAll(@RequestBody BannerRequest request){
        try {
            BannerQuery bannerQuery = new BannerQuery();
            bannerQuery.setStatus(DataStatus.Y.getCode());
            bannerQuery.setStage(request.getStage());
            bannerQuery.setJump(request.getJump());
            bannerQuery.setJumpCatalog(request.getJumpCatalog());
            List<Banner> banners = bannerService.findAll(bannerQuery);

            List<Long> jumpIds = banners.stream().map(Banner::getJumpId).collect(Collectors.toList());

            Map<Long, Goods> goodsMap = new HashMap<>();
            Map<Long, Course> courseMap = new HashMap<>();
            if (!this.isEmpty(jumpIds) && !jumpIds.isEmpty()) {
                //查询商品
                goodsMap = goodsService.findMapByIds(jumpIds);
                //查询课程
                courseMap = courseService.findMapByIds(jumpIds);
            }

            List<BannerResponse> bannerResponses = new ArrayList<>();

            for (Banner banner : banners) {
                BannerResponse bannerResponse = new BannerResponse(banner);
                if (BannerJumpCatalog.C0.getCode().equals(banner.getJumpCatalog())) {
                    if (goodsMap.containsKey(banner.getJumpId())) {
                        bannerResponse.setProductName(goodsMap.get(banner.getJumpId()).getName());
                    }
                } else if (BannerJumpCatalog.C1.getCode().equals(banner.getJumpCatalog())) {
                    if (courseMap.containsKey(banner.getJumpId())) {
                        bannerResponse.setProductName(courseMap.get(banner.getJumpId()).getTitle());
                    }
                }
                bannerResponses.add(bannerResponse);
            }

            return new Response<>(OK, SUCCESS, bannerResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    @Operation(summary = "轮播图新增")
    @RequestMapping(value = "/v1/banner/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> create(@RequestBody BannerRequest request){
        try {
            Date serverTime = this.getServerTime();
            BannerValidator validator = new BannerValidator();
            if (!validator
                    .onImgUrl(request.getImgUrl())
                    .onJump(request.getJump())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            Banner banner = new Banner();
            banner.setImgUrl(request.getImgUrl());
            banner.setJump(request.getJump());
            banner.setJumpCatalog(request.getJumpCatalog());
            banner.setJumpId(request.getJumpId());
            banner.setStage(request.getStage());
            banner.setSort(request.getSort());
            banner.setStatus(DataStatus.Y.getCode());
            banner.setModifyTime(serverTime);
            banner.setCreateTime(serverTime);
            bannerService.create(banner);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "轮播图修改")
    @RequestMapping(value = "/v1/banner/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modify(@RequestBody BannerRequest request){
        try {
            Date serverTime = this.getServerTime();
            BannerValidator validator = new BannerValidator();
            if (!validator
                    .onId(request.getId())
                    .onImgUrl(request.getImgUrl())
                    .onJump(request.getJump())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            Banner banner = new Banner();
            banner.setId(request.getId());
            banner.setImgUrl(request.getImgUrl());
            banner.setJump(request.getJump());
            banner.setJumpCatalog(request.getJumpCatalog());
            banner.setJumpId(request.getJumpId());
            banner.setStage(request.getStage());
            banner.setSort(request.getSort());
            banner.setModifyTime(serverTime);
            bannerService.modifyById(banner);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "轮播图删除")
    @RequestMapping(value = "/v1/banner/remove",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> remove(@RequestBody BannerRequest request){
        try {
            Date serverTime = this.getServerTime();
            BannerValidator validator = new BannerValidator();
            if (!validator
                    .onId(request.getId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            Banner banner = new Banner();
            banner.setId(request.getId());
            banner.setStatus(DataStatus.N.getCode());
            banner.setModifyTime(serverTime);
            bannerService.modifyById(banner);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "轮播图上下架")
    @RequestMapping(value = "/v1/banner/stage/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modifyStage(@RequestBody BannerRequest request){
        try {
            Date serverTime = this.getServerTime();
            BannerValidator validator = new BannerValidator();
            if (!validator
                    .onId(request.getId())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            Banner banner = new Banner();
            banner.setId(request.getId());
            banner.setStage(request.getStage());
            banner.setModifyTime(serverTime);
            bannerService.modifyById(banner);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
