package com.api.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.OrderRequest;
import com.api.config.Token;
import com.api.config.WxPayConfig;
import com.api.validator.OrderValidator;
import com.common.bean.Response;
import com.common.bean.UserCommissionDTO;
import com.common.bean.UserToken;
import com.common.constant.App;
import com.common.constant.CacheKey;
import com.common.constant.DataStage;
import com.common.constant.DataStatus;
import com.common.constant.OrderCatalog;
import com.common.constant.OrderStage;
import com.common.constant.PayType;
import com.common.constant.UserCommissionFlowCatalog;
import com.common.constant.UserCommissionFlowIncome;
import com.common.constant.WxApiType;
import com.common.constant.WxNotifyType;
import com.common.constant.WxTradeState;
import com.common.util.DateUtil;
import com.common.util.WechatPay2ValidatorForRequest;
import com.domain.Course;
import com.domain.Goods;
import com.domain.GoodsSpecificationSku;
import com.domain.Order;
import com.domain.OrderCodeFlow;
import com.domain.OrderProduct;
import com.domain.OrderRefundInfo;
import com.domain.UserCommission;
import com.domain.UserCommissionCashFlow;
import com.domain.UserCommissionFlow;
import com.domain.complex.OrderCodeFlowQuery;
import com.domain.complex.OrderProductQuery;
import com.domain.complex.UserCommissionCashFlowQuery;
import com.domain.complex.UserCommissionCashQuery;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.service.CourseService;
import com.service.GoodsService;
import com.service.GoodsSpecificationSkuService;
import com.service.OrderCodeFlowService;
import com.service.OrderProductService;
import com.service.OrderService;
import com.service.UserCommissionCashFlowService;
import com.service.UserCommissionFlowService;
import com.service.UserCommissionService;
import com.wechat.pay.contrib.apache.httpclient.auth.Verifier;
import com.wechat.pay.contrib.apache.httpclient.util.AesUtil;


@Tag(name = "支付")
@RestController
public class WechatPayController extends BaseController {
    @Autowired
    private OrderProductService orderProductService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private GoodsService goodsService;
    @Autowired
    private GoodsSpecificationSkuService goodsSpecificationSkuService;
    @Autowired
    private CourseService courseService;
    @Autowired
    private Verifier verifier;
    @Autowired
    private WxPayConfig wxPayConfig;
    @Autowired
    private CloseableHttpClient wxPayClient;
    @Autowired
    private OrderCodeFlowService orderCodeFlowService;
    @Autowired
    private UserCommissionCashFlowService userCommissionCashFlowService;
    @Autowired
    private UserCommissionService userCommissionService;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    private final ReentrantLock lock = new ReentrantLock();

    @Operation(summary = "JSAPI/小程序下单")
    @RequestMapping(value = "/v1/wechat/pay/jsapi/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> createJsapi(@RequestBody OrderRequest request){
        Date datetime = this.getServerTime();

        if (this.isEmpty(request.getId())) {
            return new Response<>(ERROR, "订单编号为空，请检查");
        }
        Lock lock = getLock(redisTemplate, CacheKey.LOCK_ORDER, request.getId().toString());
        if (!lock.tryLock()){
            return new Response<>(ERROR, "请勿重复提交");
        }
        try {
            Order order = orderService.findById(request.getId());
            if (this.isEmpty(order) || DataStatus.N.getCode().equals(order.getStatus())){
                return new Response<>(ERROR, "未找到订单信息");
            }
            if (!OrderStage.NOTPAY.getCode().equals(order.getStage())){
                return new Response<>(ERROR, "订单状态不是未付款，请检查");
            }
            if (datetime.after(DateUtil.getFutureMinute(order.getCreateTime(),App.USER_ORDER_EXPIRE_TIME))){
                return new Response<>(ERROR,"订单超时未支付,已为您自动取消，无法支付");
            }
            if (this.isEmpty(order.getAmount()) || order.getAmount().compareTo(BigDecimal.ZERO) <= 0){
                return new Response<>(ERROR, "订单金额为空或非大于0，请检查");
            }
            OrderProductQuery orderProductQuery = new OrderProductQuery();
            orderProductQuery.setOrderId(order.getId());
            orderProductQuery.setStatus(DataStatus.Y.getCode());
            List<OrderProduct> orderProductList = orderProductService.findAll(orderProductQuery);
            if (this.isEmpty(orderProductList) || orderProductList.isEmpty()){
                return new Response<>(ERROR, "未找到订单商品信息");
            }
            List<Long> productIds = orderProductList.stream().map(OrderProduct::getProductId).collect(Collectors.toList());
            for (OrderProduct orderProduct : orderProductList) {
                if (this.isEmpty(orderProduct.getAmount()) || orderProduct.getAmount().compareTo(BigDecimal.ZERO) <= 0){
                    return new Response<>(ERROR, "订单商品金额小于等于0，请检查");
                }
            }
            if (OrderCatalog.C0.getCode().equals(order.getOrderCatalog())){
                List<Course> courseList = courseService.findByIds(productIds);
                if (this.isEmpty(courseList) || courseList.isEmpty()){
                    return new Response<>(ERROR, "未找到课程信息");
                }
                for (Course course : courseList) {
                    if (!DataStage.Y.getCode().equals(course.getStatus())){
                        return new Response<>(ERROR, course.getTitle() + "课程状态不正常");
                    }
                }
            }else if (OrderCatalog.C1.getCode().equals(order.getOrderCatalog())){
                List<Goods> goodsList = goodsService.findByIds(productIds);
                if (this.isEmpty(goodsList) || goodsList.isEmpty()){
                    return new Response<>(ERROR, "未找到商品信息");
                }
                for (Goods goods : goodsList) {
                    if (!DataStage.Y.getCode().equals(goods.getStatus())){
                        return new Response<>(ERROR, goods.getName() + "商品状态不正常");
                    }
                }
            }else {
                return new Response<>(ERROR, "订单类型错误");
            }

            if (!this.isEmpty(order.getPrepayId())) {
                return new Response<>(OK, SUCCESS,order.getPrepayId());
            }

            String orderCode = order.getCode();
            // 创建post请求
            HttpPost httpPost = new HttpPost(wxPayConfig.getDomain().concat(WxApiType.JSAPI_PAY.getType()));
            Map<String,Object> paramsMap = new HashMap<>();
            paramsMap.put("appid", wxPayConfig.getAppid());
            paramsMap.put("mchid", wxPayConfig.getMchId());
            paramsMap.put("description", orderCode);
            paramsMap.put("out_trade_no", orderCode);
            paramsMap.put("notify_url", wxPayConfig.getNotifyDomain().concat(WxNotifyType.NATIVE_NOTIFY.getType()));
            // 订单金额对象
            Map<String,Object> amountMap = new HashMap<>();
            amountMap.put("total", order.getAmount().multiply(BigDecimal.valueOf(100)).intValue());
            amountMap.put("currency", "CNY");
            paramsMap.put("amount", amountMap);

            String json = this.getJSON(paramsMap);

            StringEntity entity = new StringEntity(json, "utf-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            httpPost.setHeader("Accept", "application/json");

            try (CloseableHttpResponse nativePayResponse = wxPayClient.execute(httpPost)) {
                // 获取响应体并转为字符串和响应状态码
                String response = EntityUtils.toString(nativePayResponse.getEntity());
                int statusCode = nativePayResponse.getStatusLine().getStatusCode();
                if (statusCode == 200) {
                    // 处理成功
                    logger.info("成功, 返回结果 = {}", response);
                } else if (statusCode == 204) {
                    // 处理成功，无返回Body
                    logger.info("成功");
                } else {
                    logger.info("JSAPI下单失败,响应码 = {},返回结果 = {}", statusCode, response);
                    return new Response<>(ERROR, "下单失败,响应码 =" + statusCode);
                }
                // 响应结果 json字符串转对象
                Map<String, String> resultMap = this.getObject(response, new TypeReference<HashMap<String, String>>(){});
                String prepayId = resultMap.get("prepay_id");

                Order modifyOrder = new Order();
                modifyOrder.setId(order.getId());
                modifyOrder.setPrepayId(prepayId);
                modifyOrder.setModifyTime(datetime);

                OrderCodeFlow orderCodeFlowCreate = new OrderCodeFlow();
                orderCodeFlowCreate.setOrderCode(orderCode);
                orderCodeFlowCreate.setPaymentType(PayType.C1000.getCode());
                orderCodeFlowCreate.setPrepayId(prepayId);
                orderCodeFlowCreate.setTradeState(WxTradeState.NOTPAY.getCode());
                orderCodeFlowCreate.setStatus(DataStage.Y.getCode());
                orderCodeFlowCreate.setCreateTime(datetime);
                orderCodeFlowCreate.setModifyTime(datetime);
                orderService.modifyById(modifyOrder, orderCodeFlowCreate);

                return new Response<>(OK, SUCCESS, prepayId);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "JSAPI/小程序支付回调")
    @RequestMapping(value = "/v1/wechat/pay/jsapi/notify",method = {RequestMethod.POST})
    @ResponseBody
    public String jsapiNotify(HttpServletRequest request, HttpServletResponse response) {
        // 构造应答对象
        Map<String, String> map = new HashMap<>();
        try{
            // 1.处理通知参数
            String body = this.readData(request);
            Map<String, Object> bodyMap = this.getObject(body, new TypeReference<HashMap<String, Object>>(){});
            logger.info("支付通知的id:{}", bodyMap.get("id"));
            logger.info("支付通知的完整数据:{}", body);

            // 2.签名验证
            WechatPay2ValidatorForRequest validator
                    = new WechatPay2ValidatorForRequest(verifier, body, (String) bodyMap.get("id"));
            if (!validator.validate(request)) {
                logger.error("通知验签失败");
                response.setStatus(500);
                map.put("code", "ERROR");
                map.put("message", "通知验签失败");
                return this.getJSON(map);
            }
            logger.info("通知验签成功");

            // 3.处理订单 微信返回的通知数据是加密的
            this.processOrder(bodyMap);

            response.setStatus(200);
            map.put("code", "SUCCESS");
            map.put("message", "成功");
            return this.getJSON(map);
        }catch (Exception e) {
            logger.error(e.getMessage(), e);
            // 测试错误应答
            response.setStatus(500);
            map.put("code", "ERROR");
            map.put("message", "系统错误");
            return this.getJSON(map);
        }

    }

    @Operation(summary = "用户取消订单")
    @RequestMapping(value = "/v1/wechat/pay/order/cancel", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> cancelOrder(@RequestBody OrderRequest request) {
        try {
            Date serverTime = this.getServerTime();
            UserToken userToken = this.getUserToken();

            // 参数验证
            OrderValidator validator = new OrderValidator();
            if (!validator.onId(request.getId()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            // 查询订单
            Order order = orderService.findById(request.getId());
            if (order == null) {
                return new Response<>(ERROR, "订单不存在");
            }

            // 验证订单是否属于当前用户
            if (!order.getUserId().equals(userToken.getId())) {
                return new Response<>(ERROR, "无权限操作此订单");
            }

            // 验证订单状态是否可以取消
            if (!OrderStage.NOTPAY.getCode().equals(order.getStage())) {
                return new Response<>(ERROR, "只有未支付的订单才能取消，当前状态：" + OrderStage.getName(order.getStage()));
            }

            // 验证订单是否有效
            if (!DataStatus.Y.getCode().equals(order.getStatus())) {
                return new Response<>(ERROR, "订单状态无效，无法取消");
            }

            // 如果订单有prepayId，需要调用微信关单接口
            if (!this.isEmpty(order.getPrepayId()) && !this.isEmpty(order.getCode())) {
                try {
                    this.closeOrder(order.getCode());
                    logger.info("微信关单成功，订单号：{}", order.getCode());
                } catch (Exception e) {
                    logger.error("微信关单失败，订单号：{}，错误：{}", order.getCode(), e.getMessage());
                    // 微信关单失败不影响本地订单取消
                }
            }

            // 只有商品订单才需要返还库存
            List<GoodsSpecificationSku> goodsSpecificationSkus = new ArrayList<>();
            if (OrderCatalog.C1.getCode().equals(order.getOrderCatalog())) {
                // 查询订单商品信息，准备返还库存
                OrderProductQuery orderProductQuery = new OrderProductQuery();
                orderProductQuery.setOrderId(order.getId());
                orderProductQuery.setStatus(DataStatus.Y.getCode());
                List<OrderProduct> orderProductList = orderProductService.findAll(orderProductQuery);

                // 返还商品规格SKU库存
                for (OrderProduct orderProduct : orderProductList) {
                    // 返还到GoodsSpecificationSku表中
                    if (orderProduct.getSkuId() == null) {
                       continue;
                    }
                    GoodsSpecificationSku goodsSpecificationSku = goodsSpecificationSkuService.findById(orderProduct.getSkuId());
                    if (goodsSpecificationSku != null && goodsSpecificationSku.getStock() != null) {
                        GoodsSpecificationSku updateSku = new GoodsSpecificationSku();
                        updateSku.setId(goodsSpecificationSku.getId());
                        updateSku.setStock(goodsSpecificationSku.getStock() + orderProduct.getNumber());
                        updateSku.setModifyTime(serverTime);
                        goodsSpecificationSkus.add(updateSku);
                    }
                }
            }

            // 更新订单状态为用户已取消
            Order updateOrder = new Order();
            updateOrder.setId(order.getId());
            updateOrder.setStage(OrderStage.CANCEL.getCode());
            updateOrder.setModifyTime(serverTime);
            orderService.modifyById(updateOrder,goodsSpecificationSkus);

            return new Response<>(OK, "订单取消成功，库存已返还");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "订单线下手动退款")
    @RequestMapping(value = "/v1/wechat/pay/order/refund", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> refundOrder(@RequestBody OrderRequest request) {
        try {
            Date serverTime = this.getServerTime();

            // 参数验证
            OrderValidator validator = new OrderValidator();
            if (!validator.onId(request.getId()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            // 查询订单
            Order order = orderService.findById(request.getId());
            if (order == null || DataStatus.N.getCode().equals(order.getStatus())) {
                return new Response<>(ERROR, "订单不存在");
            }

            //只有待退款才可以退款
            if (!OrderStage.PENDING_REFUND.getCode().equals(order.getStage())) {
                return new Response<>(ERROR, "订单状态不是待退款，无法退款");
            }

            // 更新订单状态
            Order modifyOrder = new Order();
            modifyOrder.setId(order.getId());
            modifyOrder.setStage(OrderStage.REFUND_SUCCESS.getCode());
            modifyOrder.setModifyTime(serverTime);

            //判断退费订单是否有分佣人,如果有分佣人，需要判断此订单是否已经提现过，没有提现过需要添加退款扣除分佣记录
            UserCommissionFlow userCommissionFlow = null;
            UserCommission modifyUserCommission = null;
            if (order.getCommissionUserId() != null) {
                //放入缓存扣除佣金
                UserCommissionDTO userCommissionDTO = new UserCommissionDTO();
                userCommissionDTO.setOrderId(order.getId());
                userCommissionDTO.setUserId(order.getCommissionUserId());
                userCommissionDTO.setCatalog(UserCommissionFlowCatalog.C1002.getCode());
                redisTemplate.opsForList().leftPush(CacheKey.USER_COMMISSION, this.getJSON(userCommissionDTO));
            }

            orderService.modifyById(modifyOrder,userCommissionFlow,modifyUserCommission);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "订单线上自动退款")
    @RequestMapping(value = "/v1/wechat/pay/order/auto/refund", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> autoRefundOrder(@RequestBody OrderRequest request) {
        try {
            Date serverTime = this.getServerTime();

            // 参数验证
            OrderValidator validator = new OrderValidator();
            if (!validator.onId(request.getId()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            // 查询订单
            Order order = orderService.findById(request.getId());
            if (order == null || DataStatus.N.getCode().equals(order.getStatus())) {
                return new Response<>(ERROR, "订单不存在");
            }

            //只有待退款才可以退款
            if (!OrderStage.PENDING_REFUND.getCode().equals(order.getStage())) {
                return new Response<>(ERROR, "订单状态不是待退款，无法退款");
            }

            // 调用微信退款接口
            this.refund(order);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "退款回调")
    @RequestMapping(value = "/v1/wechat/pay/refunds/notify",method = {RequestMethod.POST})
    @ResponseBody
    public String refundsNotify(HttpServletRequest request, HttpServletResponse response) {

        // 1.日志处理
        logger.info("处理退款通知...");

        // 2.获取请求参数
        Map<String, String> map = new HashMap<>();
        try {
            // 2.1处理通知参数
            String data = this.readData(request);
            Map<String, Object> dataMap = this.getObject(data, new TypeReference<HashMap<String, Object>>(){});
            // 为了校验请求签名
            String requestId = (String) dataMap.get("id");
            // 验证签名
            WechatPay2ValidatorForRequest wechatPay2ValidatorForRequest = new WechatPay2ValidatorForRequest(verifier, data, requestId);
            if (!wechatPay2ValidatorForRequest.validate(request)) {
                logger.error("退款通知验签失败...");

                // 失败应答
                response.setStatus(500);
                map.put("code", "FAIL");
                map.put("message", "退款通知验签失败");
                return this.getJSON(map);
            }
            logger.info("退款通知验签成功...");
            // 3.处理退款订单
            this.processRefund(dataMap);
            // 成功应答
            response.setStatus(200);
            map.put("code", "SUCCESS");
            map.put("message", "成功");
            return this.getJSON(map);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            // 4.设置响应参数
            response.setStatus(500);
            map.put("code", "FAIL");
            map.put("message", "失败");
            return this.getJSON(map);
        }
    }

    public void processRefund(Map<String, Object> dataMap) throws Exception {
        // 1.日志记录、上可重入锁
        logger.info("处理退款订单...");

        // 2.转换响应中的密文
        String plainText = decryptFromResource(dataMap);
        // 将明文转换成map
        HashMap<String,String> plainTextMap = this.getObject(plainText, new TypeReference<HashMap<String, String>>(){});
        String orderNo = plainTextMap.get("out_trade_no");

        // 3.根据退款情况处理订单
        if (lock.tryLock()) {
            try {
                Order order = orderService.findByCode(orderNo);
                // 订单状态不是退款中，则直接返回
                if (!OrderStage.AUTO_REFUND_PROCESSING.getCode().equals(order.getStage())) {
                    return;
                }
                // 3.更新订单状态
                Order modifyOrder = new Order();
                modifyOrder.setId(order.getId());
                modifyOrder.setStage(OrderStage.AUTO_REFUND_SUCCESS.getCode());
                modifyOrder.setModifyTime(this.getServerTime());

                if (order.getCommissionUserId() != null) {
                    //放入缓存扣除佣金
                    UserCommissionDTO userCommissionDTO = new UserCommissionDTO();
                    userCommissionDTO.setOrderId(order.getId());
                    userCommissionDTO.setUserId(order.getCommissionUserId());
                    userCommissionDTO.setCatalog(UserCommissionFlowCatalog.C1002.getCode());
                    redisTemplate.opsForList().leftPush(CacheKey.USER_COMMISSION, this.getJSON(userCommissionDTO));
                }

                orderService.modifyById(modifyOrder,plainText);
            } finally {
                // 5.要主动释放锁
                lock.unlock();
            }
        }
    }

    public void refund(Order order) throws IOException {
        Date serverTime = this.getServerTime();
        logger.info("创建退款单记录");
        // 根据订单号创建退款单

        // 生成退款订单 根据订单号生成退款订单
        OrderRefundInfo orderRefundInfo = new OrderRefundInfo();
        orderRefundInfo.setOrderNo(order.getCode());//订单编号
        orderRefundInfo.setRefundNo(this.getFlowCode());//退款单编号
        orderRefundInfo.setTotalFee(order.getAmount().multiply(BigDecimal.valueOf(100)).intValue());//原订单金额(分)
        orderRefundInfo.setRefund(order.getAmount().multiply(BigDecimal.valueOf(100)).intValue());//退款金额(分)
        orderRefundInfo.setReason("自动退款");//退款原因
        orderRefundInfo.setPaymentType(PayType.C1000.getCode());
        orderRefundInfo.setStatus(DataStatus.Y.getCode());
        orderRefundInfo.setCreateTime(serverTime);
        orderRefundInfo.setModifyTime(serverTime);

        logger.info("调用微信退款接口");
        String url = wxPayConfig.getDomain().concat(WxApiType.DOMESTIC_REFUNDS.getType());
        HttpPost httpPost = new HttpPost(url);
        // 请求参数封装
        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("out_trade_no", order.getCode());//订单编号
        paramsMap.put("out_refund_no", orderRefundInfo.getRefundNo());//退款单编号
        paramsMap.put("reason","自动退款");//退款原因
        // 退款通知地址，退款也进行了回调通知，类似下单处理？
        paramsMap.put("notify_url", wxPayConfig.getNotifyDomain().concat(WxNotifyType.REFUND_NOTIFY.getType()));

        Map<String,Object> amountMap = new HashMap<>();
        amountMap.put("refund", orderRefundInfo.getRefund());//退款金额
        amountMap.put("total", orderRefundInfo.getTotalFee());//原订单金额
        amountMap.put("currency", "CNY");//退款币种
        paramsMap.put("amount", amountMap);

        //将参数转换成json字符串
        String jsonParams = this.getJSON(paramsMap);
        logger.info("请求参数:{}" , jsonParams);

        // 封装到请求中，并设置请求格式和响应格式
        StringEntity entity = new StringEntity(jsonParams, "utf-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        httpPost.setHeader("Accept", "application/json");

        // 发起退款请求，内部对请求做了签名，响应也验签了
        CloseableHttpResponse response = wxPayClient.execute(httpPost);

        // 解析响应
        try {
            String bodyAsString = EntityUtils.toString(response.getEntity());
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                logger.info("成功, 退款返回结果 = {}", bodyAsString);
            } else if (statusCode == 204) {
                logger.info("成功");
            } else {
                throw new RuntimeException("退款异常, 响应码 = " + statusCode+ ", 退款返回结果 = " + bodyAsString);
            }

            Map<String, String> resultMap = this.getObject(bodyAsString, new TypeReference<HashMap<String, String>>(){});

            // 设置要修改的字段
            orderRefundInfo.setRefundId(resultMap.get("refund_id"));//微信支付退款单号

            // 查询退款或申请退款中的返回参数
            if (resultMap.get("status") != null) {
                orderRefundInfo.setRefundStatus(resultMap.get("status"));
                orderRefundInfo.setContentReturn(bodyAsString);
            }

            // 退款回调中的回调参数
            if(resultMap.get("refund_status") != null){
                orderRefundInfo.setRefundStatus(resultMap.get("refund_status"));//退款状态
                orderRefundInfo.setContentNotify(bodyAsString);//将全部响应结果存入数据库的content字段
            }

            // 更新订单状态
            Order modifyOrder = new Order();
            modifyOrder.setId(order.getId());
            modifyOrder.setStage(OrderStage.AUTO_REFUND_PROCESSING.getCode());
            modifyOrder.setModifyTime(serverTime);
            orderService.modifyById(modifyOrder, orderRefundInfo);
        } finally {
            response.close();
        }
    }


    private void processOrder(Map<String, Object> bodyMap) throws GeneralSecurityException {
        logger.info("处理订单");
        Date serverTime = this.getServerTime();

        // 1.密文解密
        String plainText = this.decryptFromResource(bodyMap);

        // 2.转换明文 https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter4_4_5.shtml
        Map<String, Object> plainTextMap = this.getObject(plainText, new TypeReference<HashMap<String, Object>>(){});
        String orderNo = (String) plainTextMap.get("out_trade_no");

        //在对业务数据进行状态检查和处理之前，这里要使用数据锁进行并发控制，以避免函数重入导致的数据混乱
        //尝试获取锁成功之后才去处理数据，相比于同步锁，这里不会去等待，获取不到则直接返回
        if (lock.tryLock()) {
            try {
                // 处理重复通知 出于接口幂等性考虑（无论接口被调用多少次，产生的结果都是一致的）
                Order order = orderService.findByCode(orderNo);
                if (!OrderStage.NOTPAY.getCode().equals(order.getStage())) {
                    return;
                }

                // 3.更新订单状态
                Order modifyOrder = new Order();
                modifyOrder.setId(order.getId());
                modifyOrder.setStage(OrderStage.SUCCESS.getCode());
                modifyOrder.setModifyTime(serverTime);

                String transactionId = (String)plainTextMap.get("transaction_id");
                String tradeType = (String)plainTextMap.get("trade_type");
                String tradeState = (String)plainTextMap.get("trade_state");
                Map<String, Object> amount = this.objectToMap(plainTextMap.get("amount"), new TypeReference<Map<String, Object>>() {});
                Integer payerTotal = ((Double) amount.get("payer_total")).intValue();

                OrderCodeFlowQuery orderCodeFlowQuery = new OrderCodeFlowQuery();
                orderCodeFlowQuery.setPrepayId(transactionId);
                List<OrderCodeFlow> orderCodeFlowList = orderCodeFlowService.findAll(orderCodeFlowQuery);

                OrderCodeFlow orderCodeFlow = new OrderCodeFlow();
                if (!orderCodeFlowList.isEmpty()){
                    orderCodeFlow.setId(orderCodeFlowList.get(0).getId());
                }
                orderCodeFlow.setOrderCode(orderNo);
                orderCodeFlow.setPaymentType(PayType.C1000.getCode());
                orderCodeFlow.setPrepayId(transactionId);
                orderCodeFlow.setTradeType(tradeType);
                orderCodeFlow.setTradeState(tradeState);
                orderCodeFlow.setPayerTotal(payerTotal);
                orderCodeFlow.setContent(plainText);
                orderCodeFlow.setStatus(DataStage.Y.getCode());
                orderCodeFlow.setCreateTime(serverTime);
                orderCodeFlow.setModifyTime(serverTime);

                //判断订单是否有分佣人
                if (order.getCommissionUserId() != null) {
                    //放入缓存添加佣金
                    UserCommissionDTO userCommissionDTO = new UserCommissionDTO();
                    userCommissionDTO.setOrderId(order.getId());
                    userCommissionDTO.setUserId(order.getCommissionUserId());
                    userCommissionDTO.setCatalog(UserCommissionFlowCatalog.C1000.getCode());
                    redisTemplate.opsForList().leftPush(CacheKey.USER_COMMISSION, this.getJSON(userCommissionDTO));
                }
                // 4.记录支付日志
                orderService.modifyById(modifyOrder,orderCodeFlow);
            } finally {
                // 要主动释放锁
                lock.unlock();
            }
        }
    }

    /**
     * 对称解密
     * 为了保证安全性，微信支付在回调通知和平台证书下载接口中，对关键信息进行了AES-256-GCM加密。
     * 证书和回调报文使用的加密密钥为APIv3密钥，32字节 <a href="https://wechatpay-api.gitbook.io/wechatpay-api-v3/ren-zheng/api-v3-mi-yao">...</a>
     */
    private String decryptFromResource(Map<String, Object> bodyMap) throws GeneralSecurityException {
        logger.info("密文解密");
        // 获取通知数据中的resource，这部分有加密数据
        Map<String, String> resourceMap = this.objectToMap( bodyMap.get("resource"), new TypeReference<Map<String, String>>(){});
        // 数据密文
        String ciphertext = resourceMap.get("ciphertext");
        // 随机串
        String nonce = resourceMap.get("nonce");
        // 附加数据
        String associatedData = resourceMap.get("associated_data");

        logger.info("密文数据：{}", ciphertext);

        // 用APIv3密钥去解密
        AesUtil aesUtil = new AesUtil(wxPayConfig.getApiV3Key().getBytes(StandardCharsets.UTF_8));

        // 使用封装好的工具类去解密
        String plainText = aesUtil.decryptToString(
                associatedData.getBytes(StandardCharsets.UTF_8),
                nonce.getBytes(StandardCharsets.UTF_8),
                ciphertext);

        logger.info("明文：{}", plainText);
        return plainText;
    }


    /**
     * 关单接口调用
     * <a href="https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_3.shtml">...</a>
     * 以下情况需要调用关单接口：
     *    1、商户订单支付失败需要生成新单号重新发起支付，要对原订单号调用关单，避免重复支付；
     *    2、系统下单后，用户支付超时，系统退出不再受理，避免用户继续，请调用关单接口。
     */
    private void closeOrder(String orderNo) throws IOException {
        logger.info("关单接口的调用，订单号：{}", orderNo);
        // 创建远程请求对象
        String url = String.format(WxApiType.CLOSE_ORDER_BY_NO.getType(), orderNo);
        url = wxPayConfig.getDomain().concat(url);
        HttpPost httpPost = new HttpPost(url);

        // 组装json请求体
        Map<String, String> paramsMap = new HashMap<>();
        // 目前文档是有 服务商务号、子商户号，如果是 JSAPI则对得上
        paramsMap.put("mchid", wxPayConfig.getMchId());
        String jsonParams = this.getJSON(paramsMap);
        logger.info("请求参数：{}", jsonParams);

        // 将请求参数设置到请求对象中
        StringEntity entity = new StringEntity(jsonParams,"utf-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        httpPost.setHeader("Accept", "application/json");

        // 完成签名并执行请求
        CloseableHttpResponse response = wxPayClient.execute(httpPost);

        try {
            int statusCode = response.getStatusLine().getStatusCode();
            // 响应状态码
            if (statusCode == 200) {
                // 处理成功
                logger.info("成功200");
            } else if (statusCode == 204) {
                // 处理成功，无返回Body
                logger.info("成功204");
            } else {
                logger.info("Native下单失败,响应码 = {}", statusCode);
                throw new IOException("request failed");
            }
        } finally {
            response.close();
        }
    }
}
