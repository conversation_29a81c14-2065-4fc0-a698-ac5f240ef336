package com.api.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.ReportGoodsRequest;
import com.api.bean.ReportGoodsResponse;
import com.api.config.Token;
import com.api.constant.App;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.constant.GoodsSortType;
import com.common.constant.ReportGoodsSortType;
import com.common.util.DateUtil;
import com.domain.Goods;
import com.domain.ReportGoods;
import com.domain.complex.ReportGoodsQuery;
import com.service.GoodsService;
import com.service.ReportGoodsService;


@Tag(name = "统计商品表")
@RestController
public class ReportGoodsController extends BaseController {
    @Autowired
    private ReportGoodsService reportGoodsService;
    @Autowired
    private GoodsService goodsService;

    @Operation(summary = "统计商品概况查询")
    @RequestMapping(value = "/v1/report/goods/sum/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<ReportGoodsResponse> querySum(@RequestBody ReportGoodsRequest request){
        try {
            ReportGoodsResponse response = new ReportGoodsResponse();
            response.setBrowseNum(0L);
            response.setPayNum(0L);
            response.setPayAmount(BigDecimal.ZERO);
            response.setRefundNum(0L);
            response.setRefundAmount(BigDecimal.ZERO);

            ReportGoodsQuery reportGoodsQuery = new ReportGoodsQuery();
            if (!this.isEmpty(request.getMinDate())){
                reportGoodsQuery.setMinDate(DateUtil.parse(request.getMinDate(), DATE_FORMAT));
            }
            if (!this.isEmpty(request.getMaxDate())){
                reportGoodsQuery.setMaxDate(DateUtil.parse(request.getMaxDate(), DATE_FORMAT));
            }
            reportGoodsQuery.setStatus(DataStatus.Y.getCode());

            List<ReportGoods> reportGoodsList = reportGoodsService.findAll(reportGoodsQuery);

            for (ReportGoods reportGoods : reportGoodsList) {
                response.setBrowseNum(response.getBrowseNum() + reportGoods.getBrowseNum());
                response.setPayNum(response.getPayNum() + reportGoods.getPayNum());
                response.setPayAmount(response.getPayAmount().add(reportGoods.getPayAmount()));
                response.setRefundNum(response.getRefundNum() + reportGoods.getRefundNum());
                response.setRefundAmount(response.getRefundAmount().add(reportGoods.getRefundAmount()));
            }

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "统计商品天查询")
    @RequestMapping(value = "/v1/report/goods/day/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<ReportGoodsResponse> queryDay(@RequestBody ReportGoodsRequest request){
        try {
            ReportGoodsResponse response = new ReportGoodsResponse();
            response.setDates(new ArrayList<>());
            response.setReportGoodsResponses(new ArrayList<>());

            ReportGoodsQuery reportGoodsQuery = new ReportGoodsQuery();
            if (this.isEmpty(request.getMinDate()) || this.isEmpty(request.getMaxDate())){
                return new Response<>(ERROR, "请选择搜索时间");
            }
            reportGoodsQuery.setMinDate(DateUtil.parse(request.getMinDate(), DATE_FORMAT));
            reportGoodsQuery.setMaxDate(DateUtil.parse(request.getMaxDate(), DATE_FORMAT));
            reportGoodsQuery.setStatus(DataStatus.Y.getCode());

            List<ReportGoods> reportGoodsList = reportGoodsService.findAll(reportGoodsQuery);

            //按日期分组
            Map<String, List<ReportGoods>> reportGoodsMap = new HashMap<>();
            for (ReportGoods reportGoods : reportGoodsList) {
                String date = DateUtil.format(reportGoods.getDate(), DATE_FORMAT);
                if (!reportGoodsMap.containsKey(date)) {
                    reportGoodsMap.put(date, new ArrayList<>());
                }
                reportGoodsMap.get(date).add(reportGoods);
            }

            List<String> dates = DateUtil.getCalendar(request.getMinDate(), request.getMaxDate(), DATE_FORMAT);
            response.setDates(dates);

            List<ReportGoodsResponse> reportGoodsResponses = new ArrayList<>();
            for (String date : dates) {
                ReportGoodsResponse reportGoodsResponse = new ReportGoodsResponse();
                reportGoodsResponse.setBrowseNum(0L);
                reportGoodsResponse.setPayNum(0L);
                reportGoodsResponse.setPayAmount(BigDecimal.ZERO);
                reportGoodsResponse.setRefundNum(0L);
                reportGoodsResponse.setRefundAmount(BigDecimal.ZERO);
                if (reportGoodsMap.containsKey(date)) {
                    List<ReportGoods> dateReportGoodsList = reportGoodsMap.get(date);
                    for (ReportGoods reportGoods : dateReportGoodsList) {
                        reportGoodsResponse.setDate(date);
                        reportGoodsResponse.setBrowseNum(reportGoodsResponse.getBrowseNum() + reportGoods.getBrowseNum());
                        reportGoodsResponse.setPayNum(reportGoodsResponse.getPayNum() + reportGoods.getPayNum());
                        reportGoodsResponse.setPayAmount(reportGoodsResponse.getPayAmount().add(reportGoods.getPayAmount()));
                        reportGoodsResponse.setRefundNum(reportGoodsResponse.getRefundNum() + reportGoods.getRefundNum());
                        reportGoodsResponse.setRefundAmount(reportGoodsResponse.getRefundAmount().add(reportGoods.getRefundAmount()));
                    }
                }
                reportGoodsResponses.add(reportGoodsResponse);
            }
            response.setReportGoodsResponses(reportGoodsResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "统计商品查询")
    @RequestMapping(value = "/v1/report/goods/goods/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<List<ReportGoodsResponse>> queryGoods(@RequestBody ReportGoodsRequest request){
        try {
            ReportGoodsQuery reportGoodsQuery = new ReportGoodsQuery();
            if (this.isEmpty(request.getMinDate()) || this.isEmpty(request.getMaxDate())){
                return new Response<>(ERROR, "请选择搜索时间");
            }
            reportGoodsQuery.setMinDate(DateUtil.parse(request.getMinDate(), DATE_FORMAT));
            reportGoodsQuery.setMaxDate(DateUtil.parse(request.getMaxDate(), DATE_FORMAT));
            reportGoodsQuery.setStatus(DataStatus.Y.getCode());
            if (this.isEmpty(request.getSortType()) || this.isEmpty(GoodsSortType.getSql(request.getSortType()))) {
                reportGoodsQuery.setSortType(ReportGoodsSortType.C10.getSql());
            }else {
                reportGoodsQuery.setSortType(ReportGoodsSortType.getSql(request.getSortType()));
            }
            List<ReportGoods> reportGoodsList = reportGoodsService.findAll(reportGoodsQuery);

            Set<Long> goodsIdSet = reportGoodsList.stream().map(ReportGoods::getGoodsId).collect(Collectors.toSet());
            Map<Long, Goods> goodsMap = new HashMap<>();
            if (!goodsIdSet.isEmpty()){
                goodsMap = goodsService.findMapByIds(new ArrayList<>(goodsIdSet));
            }

            //按商品分组
            Map<Long, List<ReportGoods>> reportGoodsMap = reportGoodsList.stream().collect(Collectors.groupingBy(ReportGoods::getGoodsId));

            List<ReportGoodsResponse> reportGoodsResponses = new ArrayList<>();

            for (Long goodsId : reportGoodsMap.keySet()) {
                List<ReportGoods> reportGoods = reportGoodsMap.get(goodsId);
                ReportGoodsResponse reportGoodsResponse = new ReportGoodsResponse();
                if (goodsMap.containsKey(goodsId)){
                    reportGoodsResponse.setGoodsName(goodsMap.get(goodsId).getName());
                    if (!this.isEmpty(goodsMap.get(goodsId).getImgUrl())) {
                        reportGoodsResponse.setFirstImgUrl(goodsMap.get(goodsId).getImgUrl().split(App.COMMA)[0]);
                    }
                }
                reportGoodsResponse.setBrowseNum(0L);
                reportGoodsResponse.setPayNum(0L);
                reportGoodsResponse.setPayAmount(BigDecimal.ZERO);
                reportGoodsResponse.setRefundNum(0L);
                reportGoodsResponse.setRefundAmount(BigDecimal.ZERO);

                for (ReportGoods reportGood : reportGoods) {
                    reportGoodsResponse.setBrowseNum(reportGoodsResponse.getBrowseNum() + reportGood.getBrowseNum());
                    reportGoodsResponse.setPayNum(reportGoodsResponse.getPayNum() + reportGood.getPayNum());
                    reportGoodsResponse.setPayAmount(reportGoodsResponse.getPayAmount().add(reportGood.getPayAmount()));
                    reportGoodsResponse.setRefundNum(reportGoodsResponse.getRefundNum() + reportGood.getRefundNum());
                    reportGoodsResponse.setRefundAmount(reportGoodsResponse.getRefundAmount().add(reportGood.getRefundAmount()));
                }
                reportGoodsResponses.add(reportGoodsResponse);
            }
            
            return new Response<>(OK, SUCCESS, reportGoodsResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
