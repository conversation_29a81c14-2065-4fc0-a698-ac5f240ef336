package com.api.controller;

import java.util.ArrayList;
import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.CityRequest;
import com.api.bean.CityResponse;
import com.api.config.Token;
import com.api.validator.CityValidator;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.City;
import com.domain.complex.CityQuery;
import com.service.CityService;


@Tag(name = "城市表")
@RestController
public class CityController extends BaseController {
    @Autowired
    private CityService cityService;

    @Operation(summary = "根据省查市列表")
    @RequestMapping(value = "/v1/city/province/id/all/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<List<CityResponse>> queryAllByProvinceId(@RequestBody CityRequest request){
        try {
            CityValidator validator = new CityValidator();
            if (!validator
                    .onProvinceId(request.getProvinceId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            CityQuery cityQuery = new CityQuery();
            cityQuery.setProvinceId(request.getProvinceId());
            cityQuery.setStatus(DataStatus.Y.getCode());
            List<City> citys = cityService.findAll(cityQuery);
            List<CityResponse> cityResponses = new ArrayList<>();
            for (City city : citys) {
                CityResponse cityResponse = new CityResponse(city);
                cityResponses.add(cityResponse);
            }
            return new Response<>(OK, SUCCESS, cityResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
