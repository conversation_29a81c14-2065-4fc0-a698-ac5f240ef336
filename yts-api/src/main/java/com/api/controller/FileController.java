package com.api.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.Base64;
import java.util.Date;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.api.bean.FileRequest;
import com.api.constant.App;
import com.common.bean.ExportMessage;
import com.common.bean.ExportResponse;
import com.common.bean.Response;
import com.common.client.ALiOSSClient;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.ExportStage;
import com.service.FileService;

@Controller
public class FileController extends BaseController {

    @Value("${spring.file.location}")
    private String location;

	@Autowired
	private FileService fileService;

	@Autowired
	private RedisTemplate<String, String> redisTemplate;

    @RequestMapping(value = "/v1/file/upload", method = RequestMethod.POST)
	public @ResponseBody Response<?> upload(@RequestParam("file") MultipartFile file){
	    	com.domain.File f = new com.domain.File();
	    	if (!file.isEmpty()) {
	    		try {
	            	String originalFilename = file.getOriginalFilename();
	            	logger.info("file original name {}",originalFilename);
	            	logger.info("file size {}", file.getSize());
	            	logger.info("file content type {}", file.getContentType());
	            	// 生成目标文件
	            	File destFile = this.getDestFile(originalFilename);
	            	// 保存到磁盘
	            	file.transferTo(destFile);
                    String[] arr = originalFilename.split("\\" + App.DOT);
                    String fileType = arr[arr.length - 1];
                    // 保存到数据库
                    Date date = this.getServerTime();
                    f.setName(file.getOriginalFilename());
                    f.setType(file.getContentType());
                    f.setSize(file.getSize());
                    f.setUrl(destFile.getAbsolutePath().replace(location, ""));
                    // 存储到阿里云oss
                    ALiOSSClient client = new ALiOSSClient();
                    String fileName = destFile.getAbsolutePath().replace(location + "/", "");
                    String ossUrl = client.upload(fileName, new FileInputStream(destFile));
                    f.setOssUrl(ossUrl);
                    f.setStatus(DataStatus.Y.getCode());
                    f.setModifyTime(date);
                    f.setCreateTime(date);
                    fileService.create(f);
            } catch (Exception e) {
            		logger.error(e.getMessage(), e);
            		return new Response<String>(ERROR,FAILURE);
			}
        } else {
        		logger.info("file is empty");
        		return new Response<String>(ERROR,FAILURE);
        }
	    	//文件扩展名
	    	String fileName = null;
	    	String fileExtension = null;
	    	if(f.getUrl() != null){
	    		int pos = f.getUrl().lastIndexOf(App.DOT);
	    		fileName = f.getUrl().substring(0,pos);
	    		fileExtension = f.getUrl().substring(pos);
	    	}
		return new Response<String>(OK,SUCCESS,this.append(fileName,fileExtension));
	}

	@RequestMapping(value = "/v1/file/base64/image/upload", method = RequestMethod.POST)
	public @ResponseBody Response<?> uploadBase64Image(@RequestBody FileRequest request){
		try {
			File destFile = this.getDestFile(UUID.randomUUID().toString() + request.getType());
			Base64.Decoder decoder = Base64.getDecoder();
			// 去掉base64前缀 data:image/jpeg;base64,
			String data = request.getData().substring(request.getData().indexOf(",", 1) + 1, request.getData().length());
			byte[] b = decoder.decode(data);
			// 处理数据
			for (int i = 0; i < b.length; ++i) {
				if (b[i] < 0) {
					b[i] += 256;
				}
			}
			// 保存图片
			FileOutputStream out = new FileOutputStream(destFile);
			out.write(b);
			out.flush();
			out.close();
			// 写入成功返回文件路径
			// 保存到数据库
			com.domain.File f = new com.domain.File();
			Date date = this.getServerTime();
			f.setUrl(destFile.getAbsolutePath().replace(location, ""));
			// 存储到阿里云oss
			ALiOSSClient client = new ALiOSSClient();
			String fileName = destFile.getAbsolutePath().replace(location + "/", "");
			String ossUrl = client.upload(fileName, new FileInputStream(destFile));
			f.setOssUrl(ossUrl);
			f.setStatus(DataStatus.Y.getCode());
			f.setModifyTime(date);
			f.setCreateTime(date);
			fileService.create(f);
			return new Response<>(f.getUrl());
		}catch (Exception e){
			logger.error(e.getMessage(),e);
			return new Response<>(ERROR,FAILURE);
		}
	}

    @RequestMapping(value = "/v1/file/download")
	public String download(@ModelAttribute com.domain.File file){
		ALiOSSClient ossClient = new ALiOSSClient();
		String ossUrl = ossClient.generatePresignedUrl(file.getUrl().substring(1));
		return this.redirect(ossUrl);
	}


	@RequestMapping(value = "/v1/file/export/url/query",method = RequestMethod.POST)
	@ResponseBody
	public Response<?> query(@RequestBody ExportMessage request) {
		try{
			ExportResponse response = new ExportResponse();
			if (isEmpty(request.getCode())){
				return new Response<>(ERROR,"请输入下载编号");
			}
			String stage = redisTemplate.opsForValue().get(CacheKey.FILE_DOWNLOAD_URL + request.getCode());
			if (isEmpty(stage)){
				return new Response<>(ERROR,"未查到到相关下载信息");
			}
			response.setStage(stage);
			// 长度大于1 是文件地址
			if (stage.length() > 1){
				response.setStage(ExportStage.OK.getCode());
				response.setUrl(stage);
			}
			return new Response<>(response);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return new Response<Object>(ERROR, FAILURE);
		}
	}

}
