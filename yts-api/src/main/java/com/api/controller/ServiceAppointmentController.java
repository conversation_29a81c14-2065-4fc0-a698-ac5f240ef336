package com.api.controller;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.AppointmentResponse;
import com.api.bean.PageResponse;
import com.api.bean.ServiceAppointmentRequest;
import com.api.bean.ServiceAppointmentResponse;
import com.api.config.Token;
import com.api.constant.App;
import com.api.validator.ServiceAppointmentValidator;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.bean.UserToken;
import com.common.constant.DataStatus;
import com.common.constant.PublicContact;
import com.common.util.DateUtil;
import com.domain.Course;
import com.domain.CourseAppointment;
import com.domain.ServiceAppointment;
import com.domain.ServiceInfo;
import com.domain.User;
import com.domain.complex.CourseAppointmentQuery;
import com.domain.complex.ServiceAppointmentQuery;
import com.domain.complex.UserQuery;
import com.service.CourseAppointmentService;
import com.service.CourseService;
import com.service.ServiceAppointmentService;
import com.service.ServiceInfoService;
import com.service.UserService;


@Tag(name = "服务预约表")
@RestController
public class ServiceAppointmentController extends BaseController {
    @Autowired
    private ServiceAppointmentService serviceAppointmentService;
    @Autowired
    private ServiceInfoService serviceInfoService;
    @Autowired
    private UserService userService;
    @Autowired
    private CourseAppointmentService courseAppointmentService;
    @Autowired
    private CourseService courseService;

    @Operation(summary = "服务预约分页查询")
    @RequestMapping(value = "/v1/service/appointment/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<ServiceAppointmentResponse>> query(@RequestBody ServiceAppointmentRequest request){
        try {
            PageResponse<ServiceAppointmentResponse> response = new PageResponse<>();
            ServiceAppointmentQuery serviceAppointmentQuery = new ServiceAppointmentQuery();
            if (!this.isEmpty(request.getUserName())){
                UserQuery userQuery = new UserQuery();
                userQuery.setName(request.getUserName());
                List<User> userList = userService.findAll(userQuery);
                List<Long> userIdList = userList.stream().map(User::getId).collect(Collectors.toList());
                if (userIdList.isEmpty()){
                    response.setPageNum(request.getPage());
                    response.setPageSize(request.getLimit());
                    response.setTotal(0);
                    response.setSize(0);
                    response.setList(new ArrayList<>());
                    return new Response<>(OK, SUCCESS, response);
                }
                if (!this.isEmpty(serviceAppointmentQuery.getUserIds())){
                    serviceAppointmentQuery.getUserIds().retainAll(userIdList);
                    if (serviceAppointmentQuery.getUserIds().isEmpty()){
                        response.setPageNum(request.getPage());
                        response.setPageSize(request.getLimit());
                        response.setTotal(0);
                        response.setSize(0);
                        response.setList(new ArrayList<>());
                        return new Response<>(OK, SUCCESS, response);
                    }
                }else {
                    serviceAppointmentQuery.setUserIds(userIdList);
                }
            }
            serviceAppointmentQuery.setServiceId(request.getServiceId());
            serviceAppointmentQuery.setMobile(request.getMobile());
            serviceAppointmentQuery.setContact(request.getContact());
            serviceAppointmentQuery.setStatus(DataStatus.Y.getCode());
            Integer total = serviceAppointmentService.count(serviceAppointmentQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            serviceAppointmentQuery.setStart(pager.getOffset());
            serviceAppointmentQuery.setLimit(pager.getLimit());
            List<ServiceAppointment> serviceAppointments = serviceAppointmentService.find(serviceAppointmentQuery);

            List<Long> serviceIds = serviceAppointments.stream().map(ServiceAppointment::getServiceId).collect(Collectors.toList());
            Map<Long, ServiceInfo> serviceInfoMap = new HashMap<>();
            if (!serviceIds.isEmpty()){
                serviceInfoMap = serviceInfoService.findMapByIds(serviceIds);
            }

            List<Long> userIds = serviceAppointments.stream().map(ServiceAppointment::getUserId).collect(Collectors.toList());
            Map<Long, User> userMap = new HashMap<>();
            if (!userIds.isEmpty()){
                userMap = userService.findMapByIds(userIds);
            }

            List<ServiceAppointmentResponse> serviceAppointmentResponses = new ArrayList<>();

            for (ServiceAppointment serviceAppointment : serviceAppointments) {
                ServiceAppointmentResponse serviceAppointmentResponse = new ServiceAppointmentResponse(serviceAppointment);
                if (serviceInfoMap.containsKey(serviceAppointment.getServiceId())){
                    serviceAppointmentResponse.setServiceName(serviceInfoMap.get(serviceAppointment.getServiceId()).getName());
                }
                if (userMap.containsKey(serviceAppointment.getUserId())){
                    serviceAppointmentResponse.setUserName(userMap.get(serviceAppointment.getUserId()).getName());
                }
                serviceAppointmentResponses.add(serviceAppointmentResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(serviceAppointmentResponses.size());
            response.setList(serviceAppointmentResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "服务预约联系")
    @RequestMapping(value = "/v1/service/appointment/contact/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modifyContact(@RequestBody ServiceAppointmentRequest request){
        try {
            Date serverTime = this.getServerTime();
            ServiceAppointmentValidator validator = new ServiceAppointmentValidator();
            if (!validator
                    .onId(request.getId())
                    .onContact(request.getContact())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            ServiceAppointment serviceAppointment = new ServiceAppointment();
            serviceAppointment.setId(request.getId());
            serviceAppointment.setContact(request.getContact());
            serviceAppointment.setComment(request.getComment());
            serviceAppointment.setModifyTime(serverTime);
            serviceAppointmentService.modifyById(serviceAppointment);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "服务预约新增")
    @RequestMapping(value = "/v1/service/appointment/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> create(@RequestBody ServiceAppointmentRequest request){
        try {
            Date serverTime = this.getServerTime();
            ServiceAppointmentValidator validator = new ServiceAppointmentValidator();
            if (!validator
                    .onServiceId(request.getServiceId())
                    .onName(request.getName())
                    .onMobile(request.getMobile())
                    .onRemark(request.getRemark())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            ServiceAppointment serviceAppointment = new ServiceAppointment();
            serviceAppointment.setUserId(this.getUserToken().getId());
            serviceAppointment.setServiceId(request.getServiceId());
            serviceAppointment.setName(request.getName());
            serviceAppointment.setMobile(request.getMobile());
            serviceAppointment.setRemark(request.getRemark());
            serviceAppointment.setContact(PublicContact.N.getCode());
            serviceAppointment.setStatus(DataStatus.Y.getCode());
            serviceAppointment.setModifyTime(serverTime);
            serviceAppointment.setCreateTime(serverTime);
            serviceAppointmentService.create(serviceAppointment);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "我的预约列表")
    @RequestMapping(value = "/v1/service/appointment/course/my/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<List<AppointmentResponse>> queryMyServiceAndCourse(@RequestBody ServiceAppointmentRequest request){
        try {
            UserToken userToken = this.getUserToken();
            List<AppointmentResponse> appointmentResponses = new ArrayList<>();

            // 1. 查询服务预约
            ServiceAppointmentQuery serviceAppointmentQuery = new ServiceAppointmentQuery();
            serviceAppointmentQuery.setUserId(userToken.getId());
            serviceAppointmentQuery.setStatus(DataStatus.Y.getCode());
            List<ServiceAppointment> serviceAppointments = serviceAppointmentService.findAll(serviceAppointmentQuery);

            // 批量查询服务信息
            List<Long> serviceIds = serviceAppointments.stream().map(ServiceAppointment::getServiceId).collect(Collectors.toList());
            Map<Long, ServiceInfo> serviceInfoMap = new HashMap<>();
            if (!serviceIds.isEmpty()) {
                serviceInfoMap = serviceInfoService.findMapByIds(serviceIds);
            }

            // 转换服务预约数据
            for (ServiceAppointment serviceAppointment : serviceAppointments) {
                AppointmentResponse appointmentResponse = new AppointmentResponse();
                appointmentResponse.setId(serviceAppointment.getId());
                appointmentResponse.setUserId(serviceAppointment.getUserId());
                appointmentResponse.setName(serviceAppointment.getName());
                appointmentResponse.setMobile(serviceAppointment.getMobile());
                appointmentResponse.setRemark(serviceAppointment.getRemark());
                appointmentResponse.setContact(serviceAppointment.getContact());
                appointmentResponse.setContactName(PublicContact.getName(serviceAppointment.getContact()));
                appointmentResponse.setComment(serviceAppointment.getComment());
                appointmentResponse.setStatus(serviceAppointment.getStatus());
                appointmentResponse.setDataCatalog("0"); // 0:服务
                appointmentResponse.setCreateTime(DateUtil.format(serviceAppointment.getCreateTime(), DATETIME_FORMAT));
                appointmentResponse.setModifyTime(DateUtil.format(serviceAppointment.getModifyTime(), DATETIME_FORMAT));

                // 设置预约名称（服务名称）
                if (serviceInfoMap.containsKey(serviceAppointment.getServiceId())) {
                    appointmentResponse.setAppointmentName(serviceInfoMap.get(serviceAppointment.getServiceId()).getName());
                    if (!this.isEmpty(serviceInfoMap.get(serviceAppointment.getServiceId()).getCoverUrl())) {
                        appointmentResponse.setImgUrl(serviceInfoMap.get(serviceAppointment.getServiceId()).getCoverUrl().split(App.COMMA)[0]);
                    }
                }

                appointmentResponses.add(appointmentResponse);
            }

            // 2. 查询课程预约
            CourseAppointmentQuery courseAppointmentQuery = new CourseAppointmentQuery();
            courseAppointmentQuery.setUserId(userToken.getId());
            courseAppointmentQuery.setStatus(DataStatus.Y.getCode());
            List<CourseAppointment> courseAppointments = courseAppointmentService.findAll(courseAppointmentQuery);

            // 批量查询课程信息
            List<Long> courseIds = courseAppointments.stream().map(CourseAppointment::getCourseId).collect(Collectors.toList());
            Map<Long, Course> courseMap = new HashMap<>();
            if (!courseIds.isEmpty()) {
                courseMap = courseService.findMapByIds(courseIds);
            }

            // 转换课程预约数据
            for (CourseAppointment courseAppointment : courseAppointments) {
                AppointmentResponse appointmentResponse = new AppointmentResponse();
                appointmentResponse.setId(courseAppointment.getId());
                appointmentResponse.setUserId(courseAppointment.getUserId());
                appointmentResponse.setMobile(courseAppointment.getMobile());
                appointmentResponse.setRemark(courseAppointment.getRemark());
                appointmentResponse.setContact(courseAppointment.getContact());
                appointmentResponse.setContactName(PublicContact.getName(courseAppointment.getContact()));
                appointmentResponse.setComment(courseAppointment.getComment());
                appointmentResponse.setStatus(courseAppointment.getStatus());
                appointmentResponse.setDataCatalog("1"); // 1:课程
                appointmentResponse.setCreateTime(DateUtil.format(courseAppointment.getCreateTime(), DATETIME_FORMAT));
                appointmentResponse.setModifyTime(DateUtil.format(courseAppointment.getModifyTime(), DATETIME_FORMAT));

                // 设置预约名称（课程标题）
                if (courseMap.containsKey(courseAppointment.getCourseId())) {
                    appointmentResponse.setAppointmentName(courseMap.get(courseAppointment.getCourseId()).getTitle());
                    if (!this.isEmpty(courseMap.get(courseAppointment.getCourseId()).getImgUrl())) {
                        appointmentResponse.setImgUrl(courseMap.get(courseAppointment.getCourseId()).getImgUrl().split(App.COMMA)[0]);
                    }
                }

                appointmentResponses.add(appointmentResponse);
            }

            appointmentResponses.sort(Comparator.comparing(AppointmentResponse::getCreateTime).reversed());

            return new Response<>(OK, SUCCESS, appointmentResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
