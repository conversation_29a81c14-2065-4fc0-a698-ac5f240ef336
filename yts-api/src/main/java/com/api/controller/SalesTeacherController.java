package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.PageResponse;
import com.api.bean.SalesTeacherRequest;
import com.api.bean.SalesTeacherResponse;
import com.api.config.Token;
import com.api.validator.SalesTeacherValidator;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.util.DateUtil;
import com.domain.SalesTeacher;
import com.domain.complex.SalesTeacherQuery;
import com.service.SalesTeacherService;


@Tag(name = "销售老师表")
@RestController
public class SalesTeacherController extends BaseController {
    @Autowired
    private SalesTeacherService salesTeacherService;

    @Operation(summary = "销售老师分页查询")
    @RequestMapping(value = "/v1/sales/teacher/query", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<SalesTeacherResponse>> query(@RequestBody SalesTeacherRequest request) {
        try {
            PageResponse<SalesTeacherResponse> response = new PageResponse<>();
            SalesTeacherQuery salesTeacherQuery = new SalesTeacherQuery();
            salesTeacherQuery.setName(request.getName());
            salesTeacherQuery.setGender(request.getGender());
            salesTeacherQuery.setMobile(request.getMobile());
            salesTeacherQuery.setStage(request.getStage());
            salesTeacherQuery.setStatus(DataStatus.Y.getCode());
            if (request.getMinCreateTime() != null) {
                salesTeacherQuery.setMinCreateTime(DateUtil.parse(request.getMinCreateTime(), DATETIME_FORMAT));
            }
            if (request.getMaxCreateTime() != null) {
                salesTeacherQuery.setMaxCreateTime(DateUtil.parse(request.getMaxCreateTime(), DATETIME_FORMAT));
            }
            Integer total = salesTeacherService.count(salesTeacherQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            salesTeacherQuery.setStart(pager.getOffset());
            salesTeacherQuery.setLimit(pager.getLimit());

            List<SalesTeacher> salesTeachers = salesTeacherService.find(salesTeacherQuery);
            List<SalesTeacherResponse> salesTeacherResponses = new ArrayList<>();
            for (SalesTeacher salesTeacher : salesTeachers) {
                SalesTeacherResponse salesTeacherResponse = new SalesTeacherResponse(salesTeacher);
                salesTeacherResponses.add(salesTeacherResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(salesTeacherResponses.size());
            response.setList(salesTeacherResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "销售老师id查询")
    @RequestMapping(value = "/v1/sales/teacher/id/query", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<SalesTeacherResponse> queryById(@RequestBody SalesTeacherRequest request) {
        try {
            SalesTeacherValidator validator = new SalesTeacherValidator();
            if (!validator.onId(request.getId()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            SalesTeacher salesTeacher = salesTeacherService.findById(request.getId());
            if (salesTeacher == null) {
                return new Response<>(ERROR, "销售老师不存在");
            }

            SalesTeacherResponse salesTeacherResponse = new SalesTeacherResponse(salesTeacher);
            return new Response<>(OK, SUCCESS, salesTeacherResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }
    @Operation(summary = "销售老师新增")
    @RequestMapping(value = "/v1/sales/teacher/create", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> create(@RequestBody SalesTeacherRequest request) {
        try {
            Date serverTime = this.getServerTime();
            SalesTeacherValidator validator = new SalesTeacherValidator();
            if (!validator
                    .onName(request.getName())
                    .onGender(request.getGender())
                    .onMobile(request.getMobile())
                    .onStage(request.getStage())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            SalesTeacher salesTeacher = new SalesTeacher();
            salesTeacher.setName(request.getName());
            salesTeacher.setGender(request.getGender());
            salesTeacher.setMobile(request.getMobile());
            salesTeacher.setStage(request.getStage());
            salesTeacher.setStatus(DataStatus.Y.getCode());
            salesTeacher.setModifyTime(serverTime);
            salesTeacher.setCreateTime(serverTime);

            salesTeacherService.create(salesTeacher);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "销售老师修改")
    @RequestMapping(value = "/v1/sales/teacher/modify", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modify(@RequestBody SalesTeacherRequest request) {
        try {
            Date serverTime = this.getServerTime();
            SalesTeacherValidator validator = new SalesTeacherValidator();
            if (!validator
                    .onId(request.getId())
                    .onName(request.getName())
                    .onGender(request.getGender())
                    .onMobile(request.getMobile())
                    .onStage(request.getStage())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            SalesTeacher existingSalesTeacher = salesTeacherService.findById(request.getId());
            if (existingSalesTeacher == null) {
                return new Response<>(ERROR, "销售老师不存在");
            }

            SalesTeacher salesTeacher = new SalesTeacher();
            salesTeacher.setId(request.getId());
            salesTeacher.setName(request.getName());
            salesTeacher.setGender(request.getGender());
            salesTeacher.setMobile(request.getMobile());
            salesTeacher.setStage(request.getStage());
            salesTeacher.setModifyTime(serverTime);

            salesTeacherService.modifyById(salesTeacher);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "销售老师删除")
    @RequestMapping(value = "/v1/sales/teacher/remove", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> remove(@RequestBody SalesTeacherRequest request) {
        try {
            Date serverTime = this.getServerTime();
            SalesTeacherValidator validator = new SalesTeacherValidator();
            if (!validator.onId(request.getId()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            SalesTeacher existingSalesTeacher = salesTeacherService.findById(request.getId());
            if (existingSalesTeacher == null) {
                return new Response<>(ERROR, "销售老师不存在");
            }

            SalesTeacher salesTeacher = new SalesTeacher();
            salesTeacher.setId(request.getId());
            salesTeacher.setStatus(DataStatus.N.getCode());
            salesTeacher.setModifyTime(serverTime);

            salesTeacherService.modifyById(salesTeacher);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "销售老师列表查询")
    @RequestMapping(value = "/v1/sales/teacher/all/query", method = {RequestMethod.POST})
    @ResponseBody
    public Response<List<SalesTeacherResponse>> queryAll(@RequestBody SalesTeacherRequest request) {
        try {
            SalesTeacherQuery salesTeacherQuery = new SalesTeacherQuery();
            salesTeacherQuery.setName(request.getName());
            salesTeacherQuery.setGender(request.getGender());
            salesTeacherQuery.setMobile(request.getMobile());
            salesTeacherQuery.setStage(request.getStage());
            salesTeacherQuery.setStatus(DataStatus.Y.getCode());

            List<SalesTeacher> salesTeachers = salesTeacherService.findAll(salesTeacherQuery);
            List<SalesTeacherResponse> salesTeacherResponses = new ArrayList<>();
            for (SalesTeacher salesTeacher : salesTeachers) {
                SalesTeacherResponse salesTeacherResponse = new SalesTeacherResponse(salesTeacher);
                salesTeacherResponses.add(salesTeacherResponse);
            }

            return new Response<>(OK, SUCCESS, salesTeacherResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "销售老师启用禁用")
    @RequestMapping(value = "/v1/sales/teacher/stage/modify", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modifyStage(@RequestBody SalesTeacherRequest request) {
        try {
            Date serverTime = this.getServerTime();
            SalesTeacherValidator validator = new SalesTeacherValidator();
            if (!validator
                    .onId(request.getId())
                    .onStage(request.getStage())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            SalesTeacher existingSalesTeacher = salesTeacherService.findById(request.getId());
            if (existingSalesTeacher == null) {
                return new Response<>(ERROR, "销售老师不存在");
            }

            SalesTeacher salesTeacher = new SalesTeacher();
            salesTeacher.setId(request.getId());
            salesTeacher.setStage(request.getStage());
            salesTeacher.setModifyTime(serverTime);

            salesTeacherService.modifyById(salesTeacher);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}