package com.api.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.PageResponse;
import com.api.bean.UserTeamRequest;
import com.api.bean.UserTeamResponse;
import com.api.bean.UserRequest;
import com.api.bean.UserResponse;
import com.api.config.Token;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.bean.UserToken;
import com.common.constant.DataStatus;
import com.common.constant.OrderStage;
import com.domain.Order;
import com.domain.User;
import com.domain.UserTeam;
import com.domain.complex.OrderQuery;
import com.domain.complex.UserQuery;
import com.domain.complex.UserTeamQuery;
import com.service.OrderService;
import com.service.UserService;
import com.service.UserTeamService;


@Tag(name = "用户团队表")
@RestController
public class UserTeamController extends BaseController {
    @Autowired
    private UserTeamService userTeamService;
    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;

    @Operation(summary = "用户团队创建(扫码1000 邀请码1001)")
    @RequestMapping(value = "/v1/user/team/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> create(@RequestBody UserTeamRequest request){
        try {
            Date serverTime = this.getServerTime();
            UserToken userToken = this.getUserToken();
            if (this.isEmpty(request.getInvitationCode())) {
                return new Response<>(ERROR, "邀请码不能为空");
            }
            //查询邀请码是否有效
            UserQuery userQuery = new UserQuery();
            userQuery.setInvitationCode(request.getInvitationCode());
            userQuery.setStatus(DataStatus.Y.getCode());
            List<User> users = userService.findAll(userQuery);
            if (users.isEmpty()) {
                return new Response<>(ERROR, "邀请码无效");
            }

            //判断是否本身
            if (users.get(0).getId().equals(userToken.getId())) {
                return new Response<>(ERROR, "不能成为自己队员");
            }

            //查看当前登录人是否已经是别人队员
            UserTeamQuery userTeamQuery = new UserTeamQuery();
            userTeamQuery.setTeamMemberUserId(userToken.getId());
            userTeamQuery.setStatus(DataStatus.Y.getCode());
            List<UserTeam> userTeamList = userTeamService.findAll(userTeamQuery);
            if (!userTeamList.isEmpty()) {
                return new Response<>(ERROR, "已经是别人队员，不能再次绑定");
            }

            UserTeam userTeam = new UserTeam();
            userTeam.setCaptainUserId(userToken.getId());
            userTeam.setTeamMemberUserId(users.get(0).getId());
            userTeam.setBindingCatalog(request.getBindingCatalog());
            userTeam.setStatus(DataStatus.Y.getCode());
            userTeam.setCreateTime(serverTime);
            userTeam.setModifyTime(serverTime);
            userTeamService.create(userTeam);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "查询自己的团队")
    @RequestMapping(value = "/v1/user/team/query", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<UserTeamResponse>> queryMyTeam(@RequestBody UserTeamRequest request) {
        try {
            // 获取当前用户token信息
            UserToken userToken = this.getUserToken();
            if (userToken == null || userToken.getId() == null) {
                return new Response<>(ERROR, "用户未登录");
            }

            PageResponse<UserTeamResponse> response = new PageResponse<>();
            Long captainUserId = userToken.getId();

            // 验证用户是否存在且状态正常
            User user = userService.findById(captainUserId);
            if (user == null) {
                return new Response<>(ERROR, "用户不存在");
            }

            if (!DataStatus.Y.getCode().equals(user.getStatus())) {
                return new Response<>(ERROR, "用户状态异常");
            }

            // 构建团队查询条件
            UserTeamQuery userTeamQuery = new UserTeamQuery();
            userTeamQuery.setCaptainUserId(captainUserId);
            userTeamQuery.setStatus(DataStatus.Y.getCode());

            // 根据关键字搜索用户，获取符合条件的用户ID列表
            if (!isEmpty(request.getKeyword())) {
                String keyword = request.getKeyword().trim();

                // 分别按姓名和手机号搜索
                List<User> searchUsers = new ArrayList<>();

                // 按姓名搜索
                UserQuery nameQuery = new UserQuery();
                nameQuery.setName(keyword);
                nameQuery.setStatus(DataStatus.Y.getCode());
                List<User> nameUsers = userService.findAll(nameQuery);
                searchUsers.addAll(nameUsers);

                // 按手机号搜索
                UserQuery mobileQuery = new UserQuery();
                mobileQuery.setMobile(keyword);
                mobileQuery.setStatus(DataStatus.Y.getCode());
                List<User> mobileUsers = userService.findAll(mobileQuery);
                searchUsers.addAll(mobileUsers);

                // 去重并获取用户ID
                List<Long> searchUserIds = searchUsers.stream()
                    .map(User::getId)
                    .distinct()
                    .collect(Collectors.toList());

                // 如果搜索结果为空，直接返回空结果
                if (searchUserIds.isEmpty()) {
                    response.setPageNum(request.getPage());
                    response.setPageSize(request.getLimit());
                    response.setTotal(0);
                    response.setSize(0);
                    response.setList(new ArrayList<>());
                    return new Response<>(OK, SUCCESS, response);
                }
                userTeamQuery.setTeamMemberUserIds(searchUserIds);
            }

            // 查询团队成员（使用分页）
            Integer total = userTeamService.count(userTeamQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            userTeamQuery.setStart(pager.getOffset());
            userTeamQuery.setLimit(pager.getLimit());
            List<UserTeam> userTeams = userTeamService.find(userTeamQuery);

            // 获取当前页团队成员的用户ID
            List<Long> teamMemberUserIds = userTeams.stream()
                .map(UserTeam::getTeamMemberUserId)
                .collect(Collectors.toList());

            // 查询团队成员的用户信息
            Map<Long, User> userMap = userService.findMapByIds(teamMemberUserIds);

            // 查询每个团队成员的佣金信息
            List<UserTeamResponse> responseList = new ArrayList<>();
            for (UserTeam userTeam : userTeams) {
                User teamMemberUser = userMap.get(userTeam.getTeamMemberUserId());
                if (teamMemberUser != null) {
                    UserTeamResponse teamResponse = new UserTeamResponse(userTeam);

                    // 设置用户基本信息
                    teamResponse.setTeamMemberUserName(teamMemberUser.getName());
                    teamResponse.setMobile(getMobile(teamMemberUser.getMobile())); // 手机号中间四位加密

                    // 查询该团队成员的佣金订单
                    OrderQuery orderQuery = new OrderQuery();
                    List<String> stages = new ArrayList<>();
                    stages.add(OrderStage.SUCCESS.getCode());
                    stages.add(OrderStage.AUTO_REFUND_PROCESSING.getCode());
                    stages.add(OrderStage.AUTO_REFUND_SUCCESS.getCode());
                    stages.add(OrderStage.AUTO_REFUND_ABNORMAL.getCode());
                    stages.add(OrderStage.REFUND_PROCESSING.getCode());
                    stages.add(OrderStage.REFUND_SUCCESS.getCode());
                    stages.add(OrderStage.REFUND_ABNORMAL.getCode());
                    stages.add(OrderStage.PENDING_REFUND.getCode());
                    orderQuery.setStages(stages);
                    orderQuery.setCommissionUserId(userTeam.getTeamMemberUserId());
                    orderQuery.setStatus(DataStatus.Y.getCode());
                    List<Order> memberOrders = orderService.findAll(orderQuery);

                    // 计算已分佣金额和订单数量
                    BigDecimal totalCommission = memberOrders.stream()
                        .map(Order::getSumCommission)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                    teamResponse.setTeamMemberUserSumCommission(totalCommission);
                    teamResponse.setTeamMemberUserCommissionOrderCount(new BigDecimal(memberOrders.size()));

                    responseList.add(teamResponse);
                }
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(responseList.size());
            response.setList(responseList);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
