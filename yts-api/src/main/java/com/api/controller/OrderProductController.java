package com.api.controller;

import java.util.ArrayList;
import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.OrderProductRequest;
import com.api.bean.OrderProductResponse;
import com.api.bean.OrderRequest;
import com.api.bean.OrderResponse;
import com.api.config.Token;
import com.api.validator.OrderProductValidator;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.Order;
import com.domain.OrderProduct;
import com.domain.complex.OrderProductQuery;
import com.domain.complex.OrderQuery;
import com.service.OrderProductService;


@Tag(name = "订单产品表")
@RestController
public class OrderProductController extends BaseController {
    @Autowired
    private OrderProductService orderProductService;

    @Operation(summary = "订单id查询全部")
    @RequestMapping(value = "/v1/order/product/order/id/all/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<List<OrderProductResponse>> queryAllByOrderId(@RequestBody OrderProductRequest request){
        try {
            OrderProductValidator validator = new OrderProductValidator();
            if (!validator.onOrderId(request.getOrderId()).result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            OrderProductQuery orderProductQuery = new OrderProductQuery();
            orderProductQuery.setOrderId(request.getOrderId());
            orderProductQuery.setStatus(DataStatus.Y.getCode());
            List<OrderProduct> orderProductList = orderProductService.findAll(orderProductQuery);

            List<OrderProductResponse> orderProductResponses = new ArrayList<>();

            for (OrderProduct orderProduct : orderProductList) {
                OrderProductResponse orderProductResponse = new OrderProductResponse(orderProduct);
                orderProductResponses.add(orderProductResponse);
            }

            return new Response<>(OK, SUCCESS, orderProductResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
