package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.GoodsTypeRequest;
import com.api.bean.GoodsTypeResponse;
import com.api.bean.PageResponse;
import com.api.config.Token;
import com.api.validator.GoodsTypeValidator;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.constant.GoodsCatalog;
import com.domain.GoodsType;
import com.domain.complex.GoodsTypeQuery;
import com.service.GoodsTypeService;


@Tag(name = "商品分类表")
@RestController
public class GoodsTypeController extends BaseController {
    @Autowired
    private GoodsTypeService goodsTypeService;

    @Operation(summary = "商品分类分页查询")
    @RequestMapping(value = "/v1/goods/type/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<GoodsTypeResponse>> query(@RequestBody GoodsTypeRequest request){
        try {
            PageResponse<GoodsTypeResponse> response = new PageResponse<>();
            GoodsTypeQuery goodsTypeQuery = new GoodsTypeQuery();
            goodsTypeQuery.setStatus(DataStatus.Y.getCode());
            goodsTypeQuery.setName(request.getName());
            goodsTypeQuery.setStage(request.getStage());
            Integer total = goodsTypeService.count(goodsTypeQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            goodsTypeQuery.setStart(pager.getOffset());
            goodsTypeQuery.setLimit(pager.getLimit());
            List<GoodsType> goodsTypes = goodsTypeService.find(goodsTypeQuery);

            List<GoodsTypeResponse> goodsTypeResponses = new ArrayList<>();

            for (GoodsType goodsType : goodsTypes) {
                GoodsTypeResponse goodsTypeResponse = new GoodsTypeResponse(goodsType);
                goodsTypeResponses.add(goodsTypeResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(goodsTypeResponses.size());
            response.setList(goodsTypeResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "商品分类查询")
    @RequestMapping(value = "/v1/goods/type/all/query",method = {RequestMethod.POST})
    @ResponseBody
    public Response<List<GoodsTypeResponse>> queryAll(@RequestBody GoodsTypeRequest request){
        try {
            GoodsTypeQuery goodsTypeQuery = new GoodsTypeQuery();
            goodsTypeQuery.setStatus(DataStatus.Y.getCode());
            goodsTypeQuery.setName(request.getName());
            goodsTypeQuery.setCatalog(GoodsCatalog.C1.getCode());
            goodsTypeQuery.setStage(request.getStage());
            List<GoodsType> goodsTypes = goodsTypeService.findAll(goodsTypeQuery);

            List<GoodsTypeResponse> goodsTypeResponses = new ArrayList<>();

            for (GoodsType goodsType : goodsTypes) {
                GoodsTypeResponse goodsTypeResponse = new GoodsTypeResponse(goodsType);
                goodsTypeResponses.add(goodsTypeResponse);
            }

            return new Response<>(OK, SUCCESS, goodsTypeResponses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    @Operation(summary = "商品分类新增")
    @RequestMapping(value = "/v1/goods/type/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> create(@RequestBody GoodsTypeRequest request){
        try {
            Date serverTime = this.getServerTime();
            GoodsTypeValidator validator = new GoodsTypeValidator();
            if (!validator
                    .onName(request.getName())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            GoodsType goodsType = new GoodsType();
            goodsType.setCatalog(GoodsCatalog.C1.getCode());
            goodsType.setName(request.getName());
            goodsType.setStage(request.getStage());
            goodsType.setSort(request.getSort() != null? request.getSort() : 0);
            goodsType.setStatus(DataStatus.Y.getCode());
            goodsType.setModifyTime(serverTime);
            goodsType.setCreateTime(serverTime);
            goodsTypeService.create(goodsType);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "商品分类修改")
    @RequestMapping(value = "/v1/goods/type/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modify(@RequestBody GoodsTypeRequest request){
        try {
            Date serverTime = this.getServerTime();
            GoodsTypeValidator validator = new GoodsTypeValidator();
            if (!validator
                    .onId(request.getId())
                    .onName(request.getName())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            GoodsType goodsType = new GoodsType();
            goodsType.setId(request.getId());
            goodsType.setName(request.getName());
            goodsType.setStage(request.getStage());
            goodsType.setSort(request.getSort() != null? request.getSort() : 0);
            goodsType.setModifyTime(serverTime);
            goodsTypeService.modifyById(goodsType);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "商品分类删除")
    @RequestMapping(value = "/v1/goods/type/remove",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> remove(@RequestBody GoodsTypeRequest request){
        try {
            Date serverTime = this.getServerTime();
            GoodsTypeValidator validator = new GoodsTypeValidator();
            if (!validator
                    .onId(request.getId())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            GoodsType goodsType = new GoodsType();
            goodsType.setId(request.getId());
            goodsType.setStatus(DataStatus.N.getCode());
            goodsType.setModifyTime(serverTime);
            goodsTypeService.modifyById(goodsType);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "商品分类上下架")
    @RequestMapping(value = "/v1/goods/type/stage/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modifyStage(@RequestBody GoodsTypeRequest request){
        try {
            Date serverTime = this.getServerTime();
            GoodsTypeValidator validator = new GoodsTypeValidator();
            if (!validator
                    .onId(request.getId())
                    .onStage(request.getStage())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            GoodsType goodsType = new GoodsType();
            goodsType.setId(request.getId());
            goodsType.setStage(request.getStage());
            goodsType.setModifyTime(serverTime);
            goodsTypeService.modifyById(goodsType);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
