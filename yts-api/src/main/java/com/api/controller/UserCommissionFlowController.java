package com.api.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.OrderResponse;
import com.api.bean.PageResponse;
import com.api.bean.UserCommissionFlowRequest;
import com.api.bean.UserCommissionFlowResponse;
import com.api.config.Token;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.bean.UserToken;
import com.common.constant.DataStatus;
import com.common.constant.OrderCatalog;
import com.common.constant.UserCommissionFlowCatalog;
import com.common.constant.UserCommissionFlowIncome;
import com.common.constant.UserCommissionFlowPayChannel;
import com.common.util.DateUtil;
import com.domain.Order;
import com.domain.OrderProduct;
import com.domain.User;
import com.domain.UserCommissionFlow;
import com.domain.complex.OrderProductQuery;
import com.domain.complex.UserCommissionFlowQuery;
import com.service.OrderProductService;
import com.service.OrderService;
import com.service.UserCommissionFlowService;
import com.service.UserService;


@Tag(name = "用户佣金明细表")
@RestController
public class UserCommissionFlowController extends BaseController {
    @Autowired
    private UserCommissionFlowService userCommissionFlowService;
    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderProductService orderProductService;

    @Operation(summary = "查询自身佣金明细列表")
    @RequestMapping(value = "/v1/user/commission/flow/my/query", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<UserCommissionFlowResponse>> queryMyCommissionFlow(@RequestBody UserCommissionFlowRequest request) {
        try {
            // 获取当前用户token信息
            UserToken userToken = this.getUserToken();
            PageResponse<UserCommissionFlowResponse> responses = new PageResponse<>();

            Long userId = userToken.getId();

            // 验证用户是否存在且状态正常
            User user = userService.findById(userId);
            if (user == null) {
                return new Response<>(ERROR, "用户不存在");
            }

            if (!DataStatus.Y.getCode().equals(user.getStatus())) {
                return new Response<>(ERROR, "用户状态异常");
            }

            // 构建查询条件
            UserCommissionFlowQuery query = new UserCommissionFlowQuery();
            query.setUserId(userId);
            query.setStatus(DataStatus.Y.getCode());
            query.setCatalogs(Arrays.asList(UserCommissionFlowCatalog.C1000.getCode(),UserCommissionFlowCatalog.C1002.getCode()));

            // 设置时间范围过滤
            if (!isEmpty(request.getMinTransactionTime())) {
                query.setMinTransactionTime(DateUtil.parse(request.getMinTransactionTime(), DATE_FORMAT));
            }
            if (!isEmpty(request.getMaxTransactionTime())) {
                query.setMaxCreateTime(DateUtil.parse(request.getMaxTransactionTime(), DATE_FORMAT));
            }

            // 查询佣金明细（使用分页）
            Integer total = userCommissionFlowService.count(query);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            query.setStart(pager.getOffset());
            query.setLimit(pager.getLimit());
            List<UserCommissionFlow> flows = userCommissionFlowService.find(query);

            // 获取关联的订单ID
            List<Long> orderIds = flows.stream()
                .map(UserCommissionFlow::getOrderId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

            // 批量查询订单信息
            Map<Long, Order> orderMap = new HashMap<>();
            Map<Long,User> userMap = new HashMap<>();
            Map<Long,List<OrderProduct>> orderIdGroupOrderProductMap = new HashMap<>();

            if (!this.isEmpty(orderIds) && !orderIds.isEmpty()) {
                List<Order> orders = orderService.findByIds(orderIds);
                for (Order order : orders) {
                    orderMap.put(order.getId(), order);
                }
                List<Long> userIds = orders.stream().map(Order::getUserId).collect(Collectors.toList());
                userMap = userService.findMapByIds(userIds);
                OrderProductQuery orderProductQuery = new OrderProductQuery();
                orderProductQuery.setStatus(DataStatus.Y.getCode());
                orderProductQuery.setOrderIds(orderIds);
                List<OrderProduct> orderProducts = orderProductService.findAll(orderProductQuery);
                for (OrderProduct orderProduct : orderProducts) {
                    if (!orderIdGroupOrderProductMap.containsKey(orderProduct.getOrderId())){
                        orderIdGroupOrderProductMap.put(orderProduct.getOrderId(),new ArrayList<>());
                    }
                    orderIdGroupOrderProductMap.get(orderProduct.getOrderId()).add(orderProduct);
                }
            }

            // 构建响应数据
            List<UserCommissionFlowResponse> responseList = new ArrayList<>();
            for (UserCommissionFlow flow : flows) {
                UserCommissionFlowResponse response = new UserCommissionFlowResponse(flow);

                // 设置分类名称
                response.setCatalogName(UserCommissionFlowCatalog.getName(flow.getCatalog()));

                // 设置收支类型名称
                response.setIncomeName(UserCommissionFlowIncome.getName(flow.getIncome()));

                // 设置支付方式名称
                response.setPayChannelName(UserCommissionFlowPayChannel.getName(flow.getPayChannel()));

                // 设置订单信息（如果有的话）
                if (orderMap.containsKey(flow.getOrderId())) {
                    Order order = orderMap.get(flow.getOrderId());
                    OrderResponse orderResponse = new OrderResponse(order);
                    if (userMap.containsKey(order.getUserId())){
                        orderResponse.setUserName(userMap.get(order.getUserId()).getName());
                        orderResponse.setMobile(getMobile(userMap.get(order.getUserId()).getMobile()));
                    }
                    List<String> productNames = new ArrayList<>();
                    if (orderIdGroupOrderProductMap.containsKey(order.getId())){
                        List<OrderProduct> orderProducts = orderIdGroupOrderProductMap.get(order.getId());
                        for (OrderProduct orderProduct : orderProducts) {
                            if (OrderCatalog.C0.getCode().equals(orderProduct.getProductCatalog())){
                                productNames.add(orderProduct.getProductName());
                            }else if (OrderCatalog.C1.getCode().equals(orderProduct.getProductCatalog())){
                                String productName = orderProduct.getProductName();
                                //如果产品名称长度为大于7个字符，则截取前7个字符+省略号
                                if (!this.isEmpty(productName) && productName.length() > 7){
                                    productName = productName.substring(0, 7) + "...";
                                }
                                String goodsName = productName +
                                        "x" +
                                        orderProduct.getNumber();
                                productNames.add(goodsName);
                            }
                        }
                    }
                    orderResponse.setProductNames(productNames);
                    response.setOrderResponse(orderResponse);
                }

                responseList.add(response);
            }

            responses.setPageNum(request.getPage());
            responses.setPageSize(request.getLimit());
            responses.setTotal(total);
            responses.setSize(responseList.size());
            responses.setList(responseList);

            return new Response<>(OK, SUCCESS, responses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    @Operation(summary = "查询自身佣金明细统计列表")
    @RequestMapping(value = "/v1/user/commission/flow/report/my/query", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<UserCommissionFlowResponse> queryMyCommissionFlowReport(@RequestBody UserCommissionFlowRequest request) {
        try {
            // 获取当前用户token信息
            UserToken userToken = this.getUserToken();
            UserCommissionFlowResponse response = new UserCommissionFlowResponse();

            Long userId = userToken.getId();

            // 验证用户是否存在且状态正常
            User user = userService.findById(userId);
            if (user == null) {
                return new Response<>(ERROR, "用户不存在");
            }

            if (!DataStatus.Y.getCode().equals(user.getStatus())) {
                return new Response<>(ERROR, "用户状态异常");
            }

            // 构建查询条件
            UserCommissionFlowQuery query = new UserCommissionFlowQuery();
            query.setUserId(userId);
            query.setStatus(DataStatus.Y.getCode());
            query.setCatalogs(Arrays.asList(UserCommissionFlowCatalog.C1000.getCode(),UserCommissionFlowCatalog.C1002.getCode()));

            // 设置时间范围过滤
            if (!isEmpty(request.getMinTransactionTime())) {
                query.setMinTransactionTime(DateUtil.parse(request.getMinTransactionTime(), DATE_FORMAT));
            }
            if (!isEmpty(request.getMaxTransactionTime())) {
                query.setMaxCreateTime(DateUtil.parse(request.getMaxTransactionTime(), DATE_FORMAT));
            }

            List<UserCommissionFlow> flows = userCommissionFlowService.findAll(query);

            // 获取关联的订单ID
            Set<Long> orderIds = flows.stream()
                    .map(UserCommissionFlow::getOrderId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            BigDecimal sumAmount = flows.stream().map(UserCommissionFlow::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            response.setSumAmount(sumAmount);
            response.setSumOrderCount(orderIds.size());

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    @Operation(summary = "查询自身提现明细列表")
    @RequestMapping(value = "/v1/user/commission/cash/my/query", method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<UserCommissionFlowResponse>> queryMyCashFlow(@RequestBody UserCommissionFlowRequest request) {
        try {
            // 获取当前用户token信息
            UserToken userToken = this.getUserToken();
            if (userToken == null || userToken.getId() == null) {
                return new Response<>(ERROR, "用户未登录");
            }

            PageResponse<UserCommissionFlowResponse> responses = new PageResponse<>();

            Long userId = userToken.getId();

            // 验证用户是否存在且状态正常
            User user = userService.findById(userId);
            if (user == null) {
                return new Response<>(ERROR, "用户不存在");
            }

            if (!DataStatus.Y.getCode().equals(user.getStatus())) {
                return new Response<>(ERROR, "用户状态异常");
            }

            // 构建查询条件 - 专门查询提现流水
            UserCommissionFlowQuery query = new UserCommissionFlowQuery();
            query.setUserId(userId);
            query.setStatus(DataStatus.Y.getCode());
            query.setCatalog(UserCommissionFlowCatalog.C1001.getCode()); // 提现流水
            query.setIncome(UserCommissionFlowIncome.C1.getCode()); // 支出

            // 设置时间范围过滤
            if (!isEmpty(request.getMinCreateTime())) {
                Date minCreateTime = DateUtil.parse(request.getMinCreateTime(), DATETIME_FORMAT);
                query.setMinCreateTime(minCreateTime);
            }
            if (!isEmpty(request.getMaxCreateTime())) {
                Date maxCreateTime = DateUtil.parse(request.getMaxCreateTime(), DATETIME_FORMAT);
                query.setMaxCreateTime(maxCreateTime);
            }

            // 设置交易时间范围过滤
            if (!isEmpty(request.getMinTransactionTime())) {
                Date minTransactionTime = DateUtil.parse(request.getMinTransactionTime(), DATETIME_FORMAT);
                query.setMinTransactionTime(minTransactionTime);
            }
            if (!isEmpty(request.getMaxTransactionTime())) {
                Date maxTransactionTime = DateUtil.parse(request.getMaxTransactionTime(), DATETIME_FORMAT);
                query.setMaxTransactionTime(maxTransactionTime);
            }

            // 查询提现明细（使用分页）
            Integer total = userCommissionFlowService.count(query);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            query.setStart(pager.getOffset());
            query.setLimit(pager.getLimit());
            List<UserCommissionFlow> flows = userCommissionFlowService.find(query);

            // 构建响应数据
            List<UserCommissionFlowResponse> responseList = new ArrayList<>();
            for (UserCommissionFlow flow : flows) {
                UserCommissionFlowResponse response = new UserCommissionFlowResponse(flow);

                // 设置分类名称
                response.setCatalogName(UserCommissionFlowCatalog.getName(flow.getCatalog()));

                // 设置收支类型名称
                response.setIncomeName(UserCommissionFlowIncome.getName(flow.getIncome()));

                // 设置支付方式名称
                response.setPayChannelName(UserCommissionFlowPayChannel.getName(flow.getPayChannel()));

                responseList.add(response);
            }

            responses.setPageNum(request.getPage());
            responses.setPageSize(request.getLimit());
            responses.setTotal(total);
            responses.setSize(responseList.size());
            responses.setList(responseList);

            return new Response<>(OK, SUCCESS, responses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
