package com.api.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import com.service.GoodsSpecificationValueService;


@Tag(name = "商品规格值表")
@RestController
public class GoodsSpecificationValueController extends BaseController {
    @Autowired
    private GoodsSpecificationValueService goodsSpecificationValueService;



}
