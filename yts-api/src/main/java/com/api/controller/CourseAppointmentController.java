package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.BannerResponse;
import com.api.bean.CourseAppointmentRequest;
import com.api.bean.CourseAppointmentResponse;
import com.api.bean.PageResponse;
import com.api.config.Token;
import com.api.validator.CourseAppointmentValidator;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.constant.PublicContact;
import com.domain.Course;
import com.domain.CourseAppointment;
import com.domain.ServiceAppointment;
import com.domain.ServiceInfo;
import com.domain.User;
import com.domain.complex.CourseAppointmentQuery;
import com.domain.complex.UserQuery;
import com.service.CourseAppointmentService;
import com.service.CourseService;
import com.service.UserService;


@Tag(name = "课程预约表")
@RestController
public class CourseAppointmentController extends BaseController {
    @Autowired
    private CourseAppointmentService courseAppointmentService;
    @Autowired
    private UserService userService;
    @Autowired
    private CourseService courseService;

    @Operation(summary = "课程预约分页查询")
    @RequestMapping(value = "/v1/course/appointment/query",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<PageResponse<CourseAppointmentResponse>> query(@RequestBody CourseAppointmentRequest request){
        try {
            PageResponse<CourseAppointmentResponse> response = new PageResponse<>();
            CourseAppointmentQuery courseAppointmentQuery = new CourseAppointmentQuery();
            if (!this.isEmpty(request.getUserName())){
                UserQuery userQuery = new UserQuery();
                userQuery.setName(request.getUserName());
                List<User> userList = userService.findAll(userQuery);
                List<Long> userIdList = userList.stream().map(User::getId).collect(Collectors.toList());
                if (userIdList.isEmpty()){
                    response.setPageNum(request.getPage());
                    response.setPageSize(request.getLimit());
                    response.setTotal(0);
                    response.setSize(0);
                    response.setList(new ArrayList<>());
                    return new Response<>(OK, SUCCESS, response);
                }
                if (!this.isEmpty(courseAppointmentQuery.getUserIds())){
                    courseAppointmentQuery.getUserIds().retainAll(userIdList);
                    if (courseAppointmentQuery.getUserIds().isEmpty()){
                        response.setPageNum(request.getPage());
                        response.setPageSize(request.getLimit());
                        response.setTotal(0);
                        response.setSize(0);
                        response.setList(new ArrayList<>());
                        return new Response<>(OK, SUCCESS, response);
                    }
                }else {
                    courseAppointmentQuery.setUserIds(userIdList);
                }
            }
            courseAppointmentQuery.setCourseId(request.getCourseId());
            courseAppointmentQuery.setMobile(request.getMobile());
            courseAppointmentQuery.setContact(request.getContact());
            courseAppointmentQuery.setStatus(DataStatus.Y.getCode());
            Integer total = courseAppointmentService.count(courseAppointmentQuery);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            courseAppointmentQuery.setStart(pager.getOffset());
            courseAppointmentQuery.setLimit(pager.getLimit());
            List<CourseAppointment> courseAppointments = courseAppointmentService.find(courseAppointmentQuery);

            List<Long> serviceIds = courseAppointments.stream().map(CourseAppointment::getCourseId).collect(Collectors.toList());
            Map<Long, Course> courseMap = new HashMap<>();
            if (!serviceIds.isEmpty()){
                courseMap = courseService.findMapByIds(serviceIds);
            }

            List<Long> userIds = courseAppointments.stream().map(CourseAppointment::getUserId).collect(Collectors.toList());
            Map<Long, User> userMap = new HashMap<>();
            if (!userIds.isEmpty()){
                userMap = userService.findMapByIds(userIds);
            }

            List<CourseAppointmentResponse> courseAppointmentResponses = new ArrayList<>();

            for (CourseAppointment courseAppointment : courseAppointments) {
                CourseAppointmentResponse courseAppointmentResponse = new CourseAppointmentResponse(courseAppointment);
                if (courseMap.containsKey(courseAppointment.getCourseId())){
                    courseAppointmentResponse.setCourseName(courseMap.get(courseAppointment.getCourseId()).getTitle());
                }
                if (userMap.containsKey(courseAppointment.getUserId())){
                    courseAppointmentResponse.setUserName(userMap.get(courseAppointment.getUserId()).getName());
                }
                courseAppointmentResponses.add(courseAppointmentResponse);
            }

            response.setPageNum(request.getPage());
            response.setPageSize(request.getLimit());
            response.setTotal(total);
            response.setSize(courseAppointmentResponses.size());
            response.setList(courseAppointmentResponses);

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @Operation(summary = "课程预约联系")
    @RequestMapping(value = "/v1/course/appointment/contact/modify",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> modifyContact(@RequestBody CourseAppointmentRequest request){
        try {
            Date serverTime = this.getServerTime();
            CourseAppointmentValidator validator = new CourseAppointmentValidator();
            if (!validator
                    .onId(request.getId())
                    .onContact(request.getContact())
                    .onComment(request.getComment())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            CourseAppointment courseAppointment = new CourseAppointment();
            courseAppointment.setId(request.getId());
            courseAppointment.setContact(request.getContact());
            courseAppointment.setComment(request.getComment());
            courseAppointment.setModifyTime(serverTime);
            courseAppointmentService.modifyById(courseAppointment);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }
    
    @Operation(summary = "课程预约新增")
    @RequestMapping(value = "/v1/course/appointment/create",method = {RequestMethod.POST})
    @ResponseBody
    @Token
    public Response<?> create(@RequestBody CourseAppointmentRequest request){
        try {
            Date serverTime = this.getServerTime();
            CourseAppointmentValidator validator = new CourseAppointmentValidator();
            if (!validator
                    .onCourseId(request.getCourseId())
                    .onMobile(request.getMobile())
                    .onRemark(request.getRemark())
                    .result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            CourseAppointment courseAppointment = new CourseAppointment();
            courseAppointment.setUserId(request.getUserId());
            courseAppointment.setCourseId(request.getCourseId());
            courseAppointment.setMobile(request.getMobile());
            courseAppointment.setRemark(request.getRemark());
            courseAppointment.setContact(PublicContact.N.getCode());
            courseAppointment.setStatus(DataStatus.Y.getCode());
            courseAppointment.setModifyTime(serverTime);
            courseAppointment.setCreateTime(serverTime);
            courseAppointmentService.create(courseAppointment);

            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
