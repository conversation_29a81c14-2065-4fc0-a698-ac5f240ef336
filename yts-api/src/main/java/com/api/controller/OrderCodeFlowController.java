package com.api.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import com.service.OrderCodeFlowService;


@Tag(name = "订单号流水表")
@RestController
public class OrderCodeFlowController extends BaseController {
    @Autowired
    private OrderCodeFlowService orderCodeFlowService;



}
