package com.api.bean;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.constant.GoodsCatalog;
import com.common.util.DateUtil;
import com.domain.Goods;

/**
 * 商品表返回值对象
 *
 * @date 2025-07-27 11:42:53
 */
public class GoodsResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "商品名称")
	private String name;

	@Schema(description = "商品单位")
	private String unit;

	@Schema(description = "首个商品封面")
	private String firstImgUrl;

	@Schema(description = "商品封面")
	private String imgUrl;

	@Schema(description = "商品类型")
	private String catalog;

	@Schema(description = "商品类型名称")
	private String catalogName;

	@Schema(description = "商品分类编号")
	private Long goodsTypeId;

	@Schema(description = "商品分类名称")
	private String goodsTypeName;

	@Schema(description = "成本价")
	private BigDecimal buyingPrice;

	@Schema(description = "划线价")
	private BigDecimal originalPrice;

	@Schema(description = "售价")
	private BigDecimal price;

	@Schema(description = "毛利(售价-成本价)")
	private BigDecimal grossProfit;

	@Schema(description = "是否分佣(0:是 1:否)")
	private String openCommission;

	@Schema(description = "团长佣金比例(%)")
	private BigDecimal commissionRatio;

	@Schema(description = "销售老师佣金比例(%)")
	private BigDecimal salesTeacherCommissionRatio;

	@Schema(description = "商品详情图片")
	private String detailImgUrl;

	@Schema(description = "描述")
	private String comment;

	@Schema(description = "虚拟销量")
	private Integer salesVolume;

	@Schema(description = "真实销量")
	private Integer realSalesVolume;

	@Schema(description = "是否首页展示(0:展示 1:不展示)")
	private String home;

	@Schema(description = "上架状态(0:上架 1:下架)")
	private String stage;

	@Schema(description = "库存")
	private Integer inventory;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "商品规格类型表返回值对象集合")
	private List<GoodsSpecificationTypeResponse> goodsSpecificationTypeResponses;

	@Schema(description = "商品规格sku表返回值对象集合")
	private List<GoodsSpecificationSkuResponse> goodsSpecificationSkuResponses;

	public GoodsResponse(){}

	public GoodsResponse(Goods goods){
		this.id = goods.getId();
		this.name = goods.getName();
		this.unit = goods.getUnit();
		this.firstImgUrl = goods.getImgUrl() != null ? goods.getImgUrl().split(App.COMMA)[0] : null;
		this.imgUrl = goods.getImgUrl();
		this.catalog = goods.getCatalog();
		this.catalogName = GoodsCatalog.getName(goods.getCatalog());
		this.goodsTypeId = goods.getGoodsTypeId();
		this.buyingPrice = goods.getBuyingPrice();
		this.originalPrice = goods.getOriginalPrice();
		this.price = goods.getPrice();
		this.grossProfit = goods.getGrossProfit();
		this.openCommission = goods.getOpenCommission();
		this.commissionRatio = goods.getCommissionRatio();
		this.salesTeacherCommissionRatio = goods.getSalesTeacherCommissionRatio();
		this.detailImgUrl = goods.getDetailImgUrl();
		this.comment = goods.getComment();
		this.salesVolume = goods.getSalesVolume();
		this.realSalesVolume = goods.getRealSalesVolume();
		this.home = goods.getHome();
		this.stage = goods.getStage();
		this.status = goods.getStatus();
		if (goods.getModifyTime() != null){
			this.modifyTime = DateUtil.format(goods.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (goods.getCreateTime() != null){
			this.createTime = DateUtil.format(goods.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public List<GoodsSpecificationTypeResponse> getGoodsSpecificationTypeResponses() {
		return goodsSpecificationTypeResponses;
	}

	public void setGoodsSpecificationTypeResponses(List<GoodsSpecificationTypeResponse> goodsSpecificationTypeResponses) {
		this.goodsSpecificationTypeResponses = goodsSpecificationTypeResponses;
	}

	public List<GoodsSpecificationSkuResponse> getGoodsSpecificationSkuResponses() {
		return goodsSpecificationSkuResponses;
	}

	public void setGoodsSpecificationSkuResponses(List<GoodsSpecificationSkuResponse> goodsSpecificationSkuResponses) {
		this.goodsSpecificationSkuResponses = goodsSpecificationSkuResponses;
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getUnit (){
		return unit;
	}

	public void setUnit (String unit) {
		this.unit = unit;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public Long getGoodsTypeId (){
		return goodsTypeId;
	}

	public void setGoodsTypeId (Long goodsTypeId) {
		this.goodsTypeId = goodsTypeId;
	}

	public BigDecimal getBuyingPrice (){
		return buyingPrice;
	}

	public void setBuyingPrice (BigDecimal buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public BigDecimal getOriginalPrice (){
		return originalPrice;
	}

	public void setOriginalPrice (BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice (){
		return price;
	}

	public void setPrice (BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getGrossProfit (){
		return grossProfit;
	}

	public void setGrossProfit (BigDecimal grossProfit) {
		this.grossProfit = grossProfit;
	}

	public String getOpenCommission (){
		return openCommission;
	}

	public void setOpenCommission (String openCommission) {
		this.openCommission = openCommission;
	}

	public BigDecimal getCommissionRatio (){
		return commissionRatio;
	}

	public void setCommissionRatio (BigDecimal commissionRatio) {
		this.commissionRatio = commissionRatio;
	}

	public BigDecimal getSalesTeacherCommissionRatio (){
		return salesTeacherCommissionRatio;
	}

	public void setSalesTeacherCommissionRatio (BigDecimal salesTeacherCommissionRatio) {
		this.salesTeacherCommissionRatio = salesTeacherCommissionRatio;
	}

	public String getDetailImgUrl (){
		return detailImgUrl;
	}

	public void setDetailImgUrl (String detailImgUrl) {
		this.detailImgUrl = detailImgUrl;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public Integer getSalesVolume (){
		return salesVolume;
	}

	public void setSalesVolume (Integer salesVolume) {
		this.salesVolume = salesVolume;
	}

	public Integer getRealSalesVolume (){
		return realSalesVolume;
	}

	public void setRealSalesVolume (Integer realSalesVolume) {
		this.realSalesVolume = realSalesVolume;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getCatalogName() {
		return catalogName;
	}

	public void setCatalogName(String catalogName) {
		this.catalogName = catalogName;
	}

	public String getGoodsTypeName() {
		return goodsTypeName;
	}

	public void setGoodsTypeName(String goodsTypeName) {
		this.goodsTypeName = goodsTypeName;
	}

	public String getFirstImgUrl() {
		return firstImgUrl;
	}

	public void setFirstImgUrl(String firstImgUrl) {
		this.firstImgUrl = firstImgUrl;
	}

	public String getHome() {
		return home;
	}

	public void setHome(String home) {
		this.home = home;
	}

	public Integer getInventory() {
		return inventory;
	}

	public void setInventory(Integer inventory) {
		this.inventory = inventory;
	}
}
