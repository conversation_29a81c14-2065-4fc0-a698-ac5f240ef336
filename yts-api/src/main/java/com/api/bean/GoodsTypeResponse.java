package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.GoodsType;

/**
 * 商品分类表返回值对象
 *
 * @date 2025-07-20 10:32:17
 */
public class GoodsTypeResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "商品类型")
	private String catalog;

	@Schema(description = "分类名称")
	private String name;

	@Schema(description = "排序字段")
	private Integer sort;

	@Schema(description = "上架状态(0:上架 1:下架)")
	private String stage;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public GoodsTypeResponse(){}

	public GoodsTypeResponse(GoodsType goodsType){
		this.id = goodsType.getId();
		this.catalog = goodsType.getCatalog();
		this.name = goodsType.getName();
		this.sort = goodsType.getSort();
		this.stage = goodsType.getStage();
		this.status = goodsType.getStatus();
		if (goodsType.getModifyTime() != null){
			this.modifyTime = DateUtil.format(goodsType.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (goodsType.getCreateTime() != null){
			this.createTime = DateUtil.format(goodsType.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public Integer getSort (){
		return sort;
	}

	public void setSort (Integer sort) {
		this.sort = sort;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
