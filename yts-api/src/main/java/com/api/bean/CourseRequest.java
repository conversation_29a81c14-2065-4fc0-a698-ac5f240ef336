package com.api.bean;

import java.math.BigDecimal;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 课程表请求对象
 *
 * @date 2025-07-19 10:58:28
 */
public class CourseRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "课程标题")
	private String title;

	@Schema(description = "课程封面")
	private String imgUrl;

	@Schema(description = "课程时长")
	private Integer length;

	@Schema(description = "课程详情图片")
	private String detailImgUrl;

	@Schema(description = "原价")
	private BigDecimal originalPrice;

	@Schema(description = "价格")
	private BigDecimal price;

	@Schema(description = "描述")
	private String comment;

	@Schema(description = "虚拟销量")
	private String salesVolume;

	@Schema(description = "是否首页展示(0:展示 1:不展示)")
	private String home;

	@Schema(description = "排序字段")
	private Integer sort;

	@Schema(description = "上架状态(0:上架 1:下架)")
	private String stage;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public String getTitle (){
		return title;
	}

	public void setTitle (String title) {
		this.title = title;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public Integer getLength (){
		return length;
	}

	public void setLength (Integer length) {
		this.length = length;
	}

	public String getDetailImgUrl (){
		return detailImgUrl;
	}

	public void setDetailImgUrl (String detailImgUrl) {
		this.detailImgUrl = detailImgUrl;
	}

	public BigDecimal getOriginalPrice (){
		return originalPrice;
	}

	public void setOriginalPrice (BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice (){
		return price;
	}

	public void setPrice (BigDecimal price) {
		this.price = price;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public String getSalesVolume (){
		return salesVolume;
	}

	public void setSalesVolume (String salesVolume) {
		this.salesVolume = salesVolume;
	}

	public String getHome (){
		return home;
	}

	public void setHome (String home) {
		this.home = home;
	}

	public Integer getSort (){
		return sort;
	}

	public void setSort (Integer sort) {
		this.sort = sort;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

}
