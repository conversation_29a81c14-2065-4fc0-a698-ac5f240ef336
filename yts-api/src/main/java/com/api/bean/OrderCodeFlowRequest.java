package com.api.bean;

import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 订单号流水表请求对象
 *
 * @date 2025-07-23 20:14:44
 */
public class OrderCodeFlowRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "订单号")
	private String orderCode;

	@Schema(description = "预支付交易会话标识")
	private String prepayId;

	@Schema(description = "预支付交易会话标识集合")
	private List<String> prepayIds;

	@Schema(description = "支付类型(1000:微信小程序 1001:支付宝)")
	private String paymentType;

	@Schema(description = "交易类型")
	private String tradeType;

	@Schema(description = "交易状态")
	private String tradeState;

	@Schema(description = "支付金额(分)")
	private Integer payerTotal;

	@Schema(description = "通知参数")
	private String content;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public String getOrderCode (){
		return orderCode;
	}

	public void setOrderCode (String orderCode) {
		this.orderCode = orderCode;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public List<String> getPrepayIds (){
		return prepayIds;
	}

	public void setPrepayIds (List<String> prepayIds) {
		this.prepayIds = prepayIds;
	}

	public String getPaymentType (){
		return paymentType;
	}

	public void setPaymentType (String paymentType) {
		this.paymentType = paymentType;
	}

	public String getTradeType (){
		return tradeType;
	}

	public void setTradeType (String tradeType) {
		this.tradeType = tradeType;
	}

	public String getTradeState (){
		return tradeState;
	}

	public void setTradeState (String tradeState) {
		this.tradeState = tradeState;
	}

	public Integer getPayerTotal (){
		return payerTotal;
	}

	public void setPayerTotal (Integer payerTotal) {
		this.payerTotal = payerTotal;
	}

	public String getContent (){
		return content;
	}

	public void setContent (String content) {
		this.content = content;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

}
