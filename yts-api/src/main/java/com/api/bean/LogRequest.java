package com.api.bean;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 系统用户日志表请求对象
 *
 * @date 2025-07-20 18:59:08
 */
public class LogRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "平台")
	private String platform;

	@Schema(description = "版本")
	private String version;

	@Schema(description = "用户类型")
	private String type;

	@Schema(description = "用户编号")
	private Long userId;

	@Schema(description = "用户编号集合")
	private List<Long> userIds;

	@Schema(description = "IP")
	private String ip;

	@Schema(description = "地址")
	private String url;

	@Schema(description = "请求编号")
	private String requestId;

	@Schema(description = "请求编号集合")
	private List<String> requestIds;

	@Schema(description = "开始时间")
	private String startTime;

	@Schema(description = "最小开始时间")
	private String minStartTime;

	@Schema(description = "最大开始时间")
	private String maxStartTime;

	@Schema(description = "结束时间")
	private String endTime;

	@Schema(description = "最小结束时间")
	private String minEndTime;

	@Schema(description = "最大结束时间")
	private String maxEndTime;

	@Schema(description = "执行时长")
	private Integer duration;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public String getPlatform (){
		return platform;
	}

	public void setPlatform (String platform) {
		this.platform = platform;
	}

	public String getVersion (){
		return version;
	}

	public void setVersion (String version) {
		this.version = version;
	}

	public String getType (){
		return type;
	}

	public void setType (String type) {
		this.type = type;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public List<Long> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Long> userIds) {
		this.userIds = userIds;
	}

	public String getIp (){
		return ip;
	}

	public void setIp (String ip) {
		this.ip = ip;
	}

	public String getUrl (){
		return url;
	}

	public void setUrl (String url) {
		this.url = url;
	}

	public String getRequestId (){
		return requestId;
	}

	public void setRequestId (String requestId) {
		this.requestId = requestId;
	}

	public List<String> getRequestIds (){
		return requestIds;
	}

	public void setRequestIds (List<String> requestIds) {
		this.requestIds = requestIds;
	}

	public String getStartTime (){
		return startTime;
	}

	public void setStartTime (String startTime) {
		this.startTime = startTime;
	}

	public String getMinStartTime (){
		return minStartTime;
	}

	public void setMinStartTime (String minStartTime) {
		this.minStartTime = minStartTime;
	}

	public String getMaxStartTime (){
		return maxStartTime;
	}

	public void setMaxStartTime (String maxStartTime) {
		this.maxStartTime = maxStartTime;
	}

	public String getEndTime (){
		return endTime;
	}

	public void setEndTime (String endTime) {
		this.endTime = endTime;
	}

	public String getMinEndTime (){
		return minEndTime;
	}

	public void setMinEndTime (String minEndTime) {
		this.minEndTime = minEndTime;
	}

	public String getMaxEndTime (){
		return maxEndTime;
	}

	public void setMaxEndTime (String maxEndTime) {
		this.maxEndTime = maxEndTime;
	}

	public Integer getDuration (){
		return duration;
	}

	public void setDuration (Integer duration) {
		this.duration = duration;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

}
