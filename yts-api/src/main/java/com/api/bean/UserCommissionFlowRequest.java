package com.api.bean;

import java.math.BigDecimal;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户佣金明细表请求对象
 *
 * @date 2025-07-27 11:32:54
 */
public class UserCommissionFlowRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "流水号")
	private String flowCode;

	@Schema(description = "用户编号")
	private Long userId;

	@Schema(description = "用户编号集合")
	private List<Long> userIds;

	@Schema(description = "订单ID")
	private Long orderId;

	@Schema(description = "订单ID集合")
	private List<Long> orderIds;

	@Schema(description = "订单号")
	private String orderCode;

	@Schema(description = "预支付交易会话标识")
	private String prepayId;

	@Schema(description = "预支付交易会话标识集合")
	private List<String> prepayIds;

	@Schema(description = "交易时间")
	private String transactionTime;

	@Schema(description = "最小交易时间")
	private String minTransactionTime;

	@Schema(description = "最大交易时间")
	private String maxTransactionTime;

	@Schema(description = "流转金额")
	private BigDecimal flowAmount;


	@Schema(description = "总金额")
	private BigDecimal amount;

	@Schema(description = "分类(1000:分佣流水)")
	private String catalog;

	@Schema(description = "支付方式(1000:微信小程序支付)")
	private String payChannel;

	@Schema(description = "收支出(0:收入 1:支出)")
	private String income;

	@Schema(description = "标签")
	private String label;

	@Schema(description = "备注")
	private String comment;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public String getFlowCode (){
		return flowCode;
	}

	public void setFlowCode (String flowCode) {
		this.flowCode = flowCode;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public List<Long> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Long> userIds) {
		this.userIds = userIds;
	}

	public Long getOrderId (){
		return orderId;
	}

	public void setOrderId (Long orderId) {
		this.orderId = orderId;
	}

	public List<Long> getOrderIds (){
		return orderIds;
	}

	public void setOrderIds (List<Long> orderIds) {
		this.orderIds = orderIds;
	}

	public String getOrderCode (){
		return orderCode;
	}

	public void setOrderCode (String orderCode) {
		this.orderCode = orderCode;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public List<String> getPrepayIds (){
		return prepayIds;
	}

	public void setPrepayIds (List<String> prepayIds) {
		this.prepayIds = prepayIds;
	}

	public String getTransactionTime (){
		return transactionTime;
	}

	public void setTransactionTime (String transactionTime) {
		this.transactionTime = transactionTime;
	}

	public String getMinTransactionTime (){
		return minTransactionTime;
	}

	public void setMinTransactionTime (String minTransactionTime) {
		this.minTransactionTime = minTransactionTime;
	}

	public String getMaxTransactionTime (){
		return maxTransactionTime;
	}

	public void setMaxTransactionTime (String maxTransactionTime) {
		this.maxTransactionTime = maxTransactionTime;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public String getPayChannel (){
		return payChannel;
	}

	public void setPayChannel (String payChannel) {
		this.payChannel = payChannel;
	}

	public String getIncome (){
		return income;
	}

	public void setIncome (String income) {
		this.income = income;
	}

	public String getLabel (){
		return label;
	}

	public void setLabel (String label) {
		this.label = label;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public BigDecimal getFlowAmount() {
		return flowAmount;
	}

	public void setFlowAmount(BigDecimal flowAmount) {
		this.flowAmount = flowAmount;
	}
}
