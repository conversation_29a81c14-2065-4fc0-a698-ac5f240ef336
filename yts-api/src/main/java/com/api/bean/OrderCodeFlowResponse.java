package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.OrderCodeFlow;

/**
 * 订单号流水表返回值对象
 *
 * @date 2025-07-23 20:14:44
 */
public class OrderCodeFlowResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "订单号")
	private String orderCode;

	@Schema(description = "预支付交易会话标识")
	private String prepayId;

	@Schema(description = "支付类型(1000:微信小程序 1001:支付宝)")
	private String paymentType;

	@Schema(description = "交易类型")
	private String tradeType;

	@Schema(description = "交易状态")
	private String tradeState;

	@Schema(description = "支付金额(分)")
	private Integer payerTotal;

	@Schema(description = "通知参数")
	private String content;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public OrderCodeFlowResponse(){}

	public OrderCodeFlowResponse(OrderCodeFlow orderCodeFlow){
		this.id = orderCodeFlow.getId();
		this.orderCode = orderCodeFlow.getOrderCode();
		this.prepayId = orderCodeFlow.getPrepayId();
		this.paymentType = orderCodeFlow.getPaymentType();
		this.tradeType = orderCodeFlow.getTradeType();
		this.tradeState = orderCodeFlow.getTradeState();
		this.payerTotal = orderCodeFlow.getPayerTotal();
		this.content = orderCodeFlow.getContent();
		this.status = orderCodeFlow.getStatus();
		if (orderCodeFlow.getModifyTime() != null){
			this.modifyTime = DateUtil.format(orderCodeFlow.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (orderCodeFlow.getCreateTime() != null){
			this.createTime = DateUtil.format(orderCodeFlow.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getOrderCode (){
		return orderCode;
	}

	public void setOrderCode (String orderCode) {
		this.orderCode = orderCode;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public String getPaymentType (){
		return paymentType;
	}

	public void setPaymentType (String paymentType) {
		this.paymentType = paymentType;
	}

	public String getTradeType (){
		return tradeType;
	}

	public void setTradeType (String tradeType) {
		this.tradeType = tradeType;
	}

	public String getTradeState (){
		return tradeState;
	}

	public void setTradeState (String tradeState) {
		this.tradeState = tradeState;
	}

	public Integer getPayerTotal (){
		return payerTotal;
	}

	public void setPayerTotal (Integer payerTotal) {
		this.payerTotal = payerTotal;
	}

	public String getContent (){
		return content;
	}

	public void setContent (String content) {
		this.content = content;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
