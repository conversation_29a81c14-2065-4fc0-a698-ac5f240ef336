package com.api.bean;

import java.math.BigDecimal;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 订单表请求对象
 *
 * @date 2025-07-24 23:22:31
 */
public class OrderRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "订单号")
	private String code;

	@Schema(description = "预支付交易会话标识")
	private String prepayId;

	@Schema(description = "预支付交易会话标识集合")
	private List<String> prepayIds;

	@Schema(description = "用户id")
	private Long userId;

	@Schema(description = "用户id集合")
	private List<Long> userIds;

	@Schema(description = "用户类型")
	private Long userCatalog;

	@Schema(description = "订单类型(0:课程 1:商品)")
	private String orderCatalog;

	@Schema(description = "选择类型(0:自选 1:帮选)")
	private String selectCatalog;

	@Schema(description = "实付金额")
	private BigDecimal amount;

	@Schema(description = "退款金额")
	private BigDecimal refundAmount;

	@Schema(description = "团长总佣金")
	private BigDecimal sumCommission;

	@Schema(description = "团长佣金人")
	private Long commissionUserId;

	@Schema(description = "团长佣金人集合")
	private List<Long> commissionUserIds;

	@Schema(description = "销售老师佣金金额")
	private BigDecimal sumSalesTeacherCommission;

	@Schema(description = "销售老师ID")
	private Long salesTeacherId;

	@Schema(description = "销售老师ID集合")
	private List<Long> salesTeacherIds;

	@Schema(description = "支付状态")
	private String stage;

	@Schema(description = "支付状态集合")
	private List<String> stages;

	@Schema(description = "提货状态(0:已提货 1:未提货)")
	private String operateStage;

	@Schema(description = "配送方式(0:自提 1:派送)")
	private String deliveryCatalog;

	@Schema(description = "预约时间")
	private String reservationTime;

	@Schema(description = "最小预约时间")
	private String minReservationTime;

	@Schema(description = "最大预约时间")
	private String maxReservationTime;

	@Schema(description = "用户地址编号")
	private Long userAddressId;

	@Schema(description = "用户地址编号集合")
	private List<Long> userAddressIds;

	@Schema(description = "地址-省份编号")
	private Long addressProvinceId;

	@Schema(description = "地址-城市编号")
	private Long addressCityId;

	@Schema(description = "地址-区县编号")
	private Long addressDistrictId;

	@Schema(description = "地址-详细地址")
	private String addressDetailAddress;

	@Schema(description = "地址-姓名")
	private String addressName;

	@Schema(description = "地址-手机号")
	private String addressMobile;

	@Schema(description = "支付时间")
	private String payTime;

	@Schema(description = "最小支付时间")
	private String minPayTime;

	@Schema(description = "最大支付时间")
	private String maxPayTime;

	@Schema(description = "备注")
	private String comment;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;

	@Schema(description = "排序字段")
	private String sortType;

	@Schema(description = "订单产品请求对象集合")
	private List<OrderProductRequest> orderProductRequestList;

	@Schema(description = "用户名称")
	private String userName;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "商品类型")
	private String goodsCatalog;

	@Schema(description = "我的订单查询类型(0:全部 1:待付款 2:待提货 3:退款)")
	private String myOrderType;

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public String getCode (){
		return code;
	}

	public void setCode (String code) {
		this.code = code;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public List<String> getPrepayIds (){
		return prepayIds;
	}

	public void setPrepayIds (List<String> prepayIds) {
		this.prepayIds = prepayIds;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public List<Long> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Long> userIds) {
		this.userIds = userIds;
	}

	public Long getUserCatalog (){
		return userCatalog;
	}

	public void setUserCatalog (Long userCatalog) {
		this.userCatalog = userCatalog;
	}

	public String getOrderCatalog (){
		return orderCatalog;
	}

	public void setOrderCatalog (String orderCatalog) {
		this.orderCatalog = orderCatalog;
	}

	public String getSelectCatalog (){
		return selectCatalog;
	}

	public void setSelectCatalog (String selectCatalog) {
		this.selectCatalog = selectCatalog;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getRefundAmount (){
		return refundAmount;
	}

	public void setRefundAmount (BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public BigDecimal getSumCommission (){
		return sumCommission;
	}

	public void setSumCommission (BigDecimal sumCommission) {
		this.sumCommission = sumCommission;
	}

	public Long getCommissionUserId (){
		return commissionUserId;
	}

	public void setCommissionUserId (Long commissionUserId) {
		this.commissionUserId = commissionUserId;
	}

	public List<Long> getCommissionUserIds (){
		return commissionUserIds;
	}

	public void setCommissionUserIds (List<Long> commissionUserIds) {
		this.commissionUserIds = commissionUserIds;
	}

	public BigDecimal getSumSalesTeacherCommission (){
		return sumSalesTeacherCommission;
	}

	public void setSumSalesTeacherCommission (BigDecimal sumSalesTeacherCommission) {
		this.sumSalesTeacherCommission = sumSalesTeacherCommission;
	}

	public Long getSalesTeacherId (){
		return salesTeacherId;
	}

	public void setSalesTeacherId (Long salesTeacherId) {
		this.salesTeacherId = salesTeacherId;
	}

	public List<Long> getSalesTeacherIds (){
		return salesTeacherIds;
	}

	public void setSalesTeacherIds (List<Long> salesTeacherIds) {
		this.salesTeacherIds = salesTeacherIds;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public String getOperateStage (){
		return operateStage;
	}

	public void setOperateStage (String operateStage) {
		this.operateStage = operateStage;
	}

	public String getDeliveryCatalog (){
		return deliveryCatalog;
	}

	public void setDeliveryCatalog (String deliveryCatalog) {
		this.deliveryCatalog = deliveryCatalog;
	}

	public String getReservationTime (){
		return reservationTime;
	}

	public void setReservationTime (String reservationTime) {
		this.reservationTime = reservationTime;
	}

	public String getMinReservationTime (){
		return minReservationTime;
	}

	public void setMinReservationTime (String minReservationTime) {
		this.minReservationTime = minReservationTime;
	}

	public String getMaxReservationTime (){
		return maxReservationTime;
	}

	public void setMaxReservationTime (String maxReservationTime) {
		this.maxReservationTime = maxReservationTime;
	}

	public Long getUserAddressId (){
		return userAddressId;
	}

	public void setUserAddressId (Long userAddressId) {
		this.userAddressId = userAddressId;
	}

	public List<Long> getUserAddressIds (){
		return userAddressIds;
	}

	public void setUserAddressIds (List<Long> userAddressIds) {
		this.userAddressIds = userAddressIds;
	}

	public String getPayTime (){
		return payTime;
	}

	public void setPayTime (String payTime) {
		this.payTime = payTime;
	}

	public String getMinPayTime (){
		return minPayTime;
	}

	public void setMinPayTime (String minPayTime) {
		this.minPayTime = minPayTime;
	}

	public String getMaxPayTime (){
		return maxPayTime;
	}

	public void setMaxPayTime (String maxPayTime) {
		this.maxPayTime = maxPayTime;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getSortType() {
		return sortType;
	}

	public void setSortType(String sortType) {
		this.sortType = sortType;
	}

	public List<OrderProductRequest> getOrderProductRequestList() {
		return orderProductRequestList;
	}

	public void setOrderProductRequestList(List<OrderProductRequest> orderProductRequestList) {
		this.orderProductRequestList = orderProductRequestList;
	}

	public Long getAddressProvinceId() {
		return addressProvinceId;
	}

	public void setAddressProvinceId(Long addressProvinceId) {
		this.addressProvinceId = addressProvinceId;
	}

	public Long getAddressCityId() {
		return addressCityId;
	}

	public void setAddressCityId(Long addressCityId) {
		this.addressCityId = addressCityId;
	}

	public Long getAddressDistrictId() {
		return addressDistrictId;
	}

	public void setAddressDistrictId(Long addressDistrictId) {
		this.addressDistrictId = addressDistrictId;
	}

	public String getAddressDetailAddress() {
		return addressDetailAddress;
	}

	public void setAddressDetailAddress(String addressDetailAddress) {
		this.addressDetailAddress = addressDetailAddress;
	}

	public String getAddressName() {
		return addressName;
	}

	public void setAddressName(String addressName) {
		this.addressName = addressName;
	}

	public String getAddressMobile() {
		return addressMobile;
	}

	public void setAddressMobile(String addressMobile) {
		this.addressMobile = addressMobile;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getGoodsCatalog() {
		return goodsCatalog;
	}

	public void setGoodsCatalog(String goodsCatalog) {
		this.goodsCatalog = goodsCatalog;
	}

	public String getMyOrderType() {
		return myOrderType;
	}

	public void setMyOrderType(String myOrderType) {
		this.myOrderType = myOrderType;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public List<String> getStages() {
		return stages;
	}

	public void setStages(List<String> stages) {
		this.stages = stages;
	}
}
