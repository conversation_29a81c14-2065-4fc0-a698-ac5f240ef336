package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.File;

/**
 * 文件表返回值对象
 *
 * @date 2025-07-16 21:03:32
 */
public class FileResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "名称")
	private String name;

	@Schema(description = "类型")
	private String type;

	@Schema(description = "大小")
	private Long size;

	@Schema(description = "位置")
	private String url;

	@Schema(description = "oss位置")
	private String ossUrl;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public FileResponse(){}

	public FileResponse(File file){
		this.id = file.getId();
		this.name = file.getName();
		this.type = file.getType();
		this.size = file.getSize();
		this.url = file.getUrl();
		this.ossUrl = file.getOssUrl();
		this.status = file.getStatus();
		if (file.getModifyTime() != null){
			this.modifyTime = DateUtil.format(file.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (file.getCreateTime() != null){
			this.createTime = DateUtil.format(file.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getType (){
		return type;
	}

	public void setType (String type) {
		this.type = type;
	}

	public Long getSize (){
		return size;
	}

	public void setSize (Long size) {
		this.size = size;
	}

	public String getUrl (){
		return url;
	}

	public void setUrl (String url) {
		this.url = url;
	}

	public String getOssUrl (){
		return ossUrl;
	}

	public void setOssUrl (String ossUrl) {
		this.ossUrl = ossUrl;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
