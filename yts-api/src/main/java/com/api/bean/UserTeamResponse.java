package com.api.bean;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.constant.UserTeamBindingCatalog;
import com.common.util.DateUtil;
import com.domain.UserTeam;

/**
 * 用户团队表返回值对象
 *
 * @date 2025-07-19 10:58:27
 */
public class UserTeamResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "队长编号")
	private Long captainUserId;

	@Schema(description = "队员编号")
	private Long teamMemberUserId;

	@Schema(description = "队员名称")
	private String teamMemberUserName;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "队员已分佣")
	private BigDecimal teamMemberUserSumCommission;

	@Schema(description = "队员已分佣订单数量")
	private BigDecimal teamMemberUserCommissionOrderCount;

	@Schema(description = "绑定类型")
	private String bindingCatalog;

	@Schema(description = "绑定类型名称")
	private String bindingCatalogName;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public UserTeamResponse(){}

	public UserTeamResponse(UserTeam userTeam){
		this.id = userTeam.getId();
		this.captainUserId = userTeam.getCaptainUserId();
		this.teamMemberUserId = userTeam.getTeamMemberUserId();
		this.bindingCatalog = userTeam.getBindingCatalog();
		this.bindingCatalogName = UserTeamBindingCatalog.getName(userTeam.getBindingCatalog());
		this.status = userTeam.getStatus();
		if (userTeam.getModifyTime() != null){
			this.modifyTime = DateUtil.format(userTeam.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (userTeam.getCreateTime() != null){
			this.createTime = DateUtil.format(userTeam.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public Long getCaptainUserId (){
		return captainUserId;
	}

	public void setCaptainUserId (Long captainUserId) {
		this.captainUserId = captainUserId;
	}

	public Long getTeamMemberUserId (){
		return teamMemberUserId;
	}

	public void setTeamMemberUserId (Long teamMemberUserId) {
		this.teamMemberUserId = teamMemberUserId;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getTeamMemberUserName() {
		return teamMemberUserName;
	}

	public void setTeamMemberUserName(String teamMemberUserName) {
		this.teamMemberUserName = teamMemberUserName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public BigDecimal getTeamMemberUserSumCommission() {
		return teamMemberUserSumCommission;
	}

	public void setTeamMemberUserSumCommission(BigDecimal teamMemberUserSumCommission) {
		this.teamMemberUserSumCommission = teamMemberUserSumCommission;
	}

	public BigDecimal getTeamMemberUserCommissionOrderCount() {
		return teamMemberUserCommissionOrderCount;
	}

	public void setTeamMemberUserCommissionOrderCount(BigDecimal teamMemberUserCommissionOrderCount) {
		this.teamMemberUserCommissionOrderCount = teamMemberUserCommissionOrderCount;
	}

	public String getBindingCatalog() {
		return bindingCatalog;
	}

	public void setBindingCatalog(String bindingCatalog) {
		this.bindingCatalog = bindingCatalog;
	}

	public String getBindingCatalogName() {
		return bindingCatalogName;
	}

	public void setBindingCatalogName(String bindingCatalogName) {
		this.bindingCatalogName = bindingCatalogName;
	}
}
