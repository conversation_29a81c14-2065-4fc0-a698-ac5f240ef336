package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.common.bean.Bean;

/**
 * 预约表返回值对象
 *
 * @date 2025-07-19 10:58:27
 */
public class AppointmentResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "用户id")
	private Long userId;

	@Schema(description = "用户名称")
	private String userName;

	@Schema(description = "预约名称")
	private String appointmentName;

	@Schema(description = "图片")
	private String imgUrl;

	@Schema(description = "姓名")
	private String name;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "备注")
	private String remark;

	@Schema(description = "是否联系(0:已联系 1:未联系)")
	private String contact;

	@Schema(description = "是否联系名称(0:已联系 1:未联系)")
	private String contactName;

	@Schema(description = "描述")
	private String comment;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "数据类型(0:服务 1:课程)")
	private String dataCatalog;

	public String getDataCatalog() {
		return dataCatalog;
	}

	public void setDataCatalog(String dataCatalog) {
		this.dataCatalog = dataCatalog;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getAppointmentName() {
		return appointmentName;
	}

	public void setAppointmentName(String appointmentName) {
		this.appointmentName = appointmentName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}
}
