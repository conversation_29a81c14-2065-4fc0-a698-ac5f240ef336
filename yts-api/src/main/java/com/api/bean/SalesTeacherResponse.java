package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.constant.PublicStage;
import com.common.util.DateUtil;
import com.domain.SalesTeacher;

/**
 * 销售老师表返回值对象
 *
 * @date 2025-07-24 23:22:31
 */
public class SalesTeacherResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "姓名")
	private String name;

	@Schema(description = "性别(0:男 1:女)")
	private String gender;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "启用/禁用(0:启用 1:禁用)")
	private String stage;

	@Schema(description = "启用/禁用名称")
	private String stageName;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public SalesTeacherResponse(){}

	public SalesTeacherResponse(SalesTeacher salesTeacher){
		this.id = salesTeacher.getId();
		this.name = salesTeacher.getName();
		this.gender = salesTeacher.getGender();
		this.mobile = salesTeacher.getMobile();
		this.stage = salesTeacher.getStage();
		this.stageName = PublicStage.getName(salesTeacher.getStage());
		this.status = salesTeacher.getStatus();
		if (salesTeacher.getModifyTime() != null){
			this.modifyTime = DateUtil.format(salesTeacher.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (salesTeacher.getCreateTime() != null){
			this.createTime = DateUtil.format(salesTeacher.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getGender (){
		return gender;
	}

	public void setGender (String gender) {
		this.gender = gender;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getStage() {
		return stage;
	}

	public void setStage(String stage) {
		this.stage = stage;
	}

	public String getStageName() {
		return stageName;
	}

	public void setStageName(String stageName) {
		this.stageName = stageName;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
