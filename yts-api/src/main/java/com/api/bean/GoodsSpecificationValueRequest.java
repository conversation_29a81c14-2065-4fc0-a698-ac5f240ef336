package com.api.bean;

import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 商品规格值表请求对象
 *
 * @date 2025-07-27 21:26:17
 */
public class GoodsSpecificationValueRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "商品id")
	private Long goodsId;

	@Schema(description = "商品id集合")
	private List<Long> goodsIds;

	@Schema(description = "规格类型ID")
	private Long goodsSpecificationTypeId;

	@Schema(description = "规格类型ID集合")
	private List<Long> goodsSpecificationTypeIds;

	@Schema(description = "规格值")
	private String name;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public Long getGoodsId (){
		return goodsId;
	}

	public void setGoodsId (Long goodsId) {
		this.goodsId = goodsId;
	}

	public List<Long> getGoodsIds (){
		return goodsIds;
	}

	public void setGoodsIds (List<Long> goodsIds) {
		this.goodsIds = goodsIds;
	}

	public Long getGoodsSpecificationTypeId (){
		return goodsSpecificationTypeId;
	}

	public void setGoodsSpecificationTypeId (Long goodsSpecificationTypeId) {
		this.goodsSpecificationTypeId = goodsSpecificationTypeId;
	}

	public List<Long> getGoodsSpecificationTypeIds (){
		return goodsSpecificationTypeIds;
	}

	public void setGoodsSpecificationTypeIds (List<Long> goodsSpecificationTypeIds) {
		this.goodsSpecificationTypeIds = goodsSpecificationTypeIds;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

}
