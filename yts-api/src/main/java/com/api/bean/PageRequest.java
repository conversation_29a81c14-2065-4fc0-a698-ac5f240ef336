package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.common.bean.Bean;

public class PageRequest extends Bean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@Schema(description = "分页页码")
	private Integer page;

	@Schema(description = "分页数量")
	private Integer limit;

	public Integer getPage() {
		return page;
	}
	public void setPage(Integer page) {
		this.page = page;
	}
	public Integer getLimit() {
		return limit;
	}
	public void setLimit(Integer limit) {
		this.limit = limit;
	}
}
