package com.api.bean;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.Course;

/**
 * 课程表返回值对象
 *
 * @date 2025-07-19 10:58:28
 */
public class CourseResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "课程标题")
	private String title;

	@Schema(description = "课程封面")
	private String imgUrl;

	@Schema(description = "课程时长")
	private Integer length;

	@Schema(description = "课程详情图片")
	private String detailImgUrl;

	@Schema(description = "原价")
	private BigDecimal originalPrice;

	@Schema(description = "价格")
	private BigDecimal price;

	@Schema(description = "描述")
	private String comment;

	@Schema(description = "虚拟销量")
	private Integer salesVolume;

	@Schema(description = "是否首页展示(0:展示 1:不展示)")
	private String home;

	@Schema(description = "排序字段")
	private Integer sort;

	@Schema(description = "上架状态(0:上架 1:下架)")
	private String stage;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public CourseResponse(){}

	public CourseResponse(Course course){
		this.id = course.getId();
		this.title = course.getTitle();
		this.imgUrl = course.getImgUrl();
		this.length = course.getLength();
		this.detailImgUrl = course.getDetailImgUrl();
		this.originalPrice = course.getOriginalPrice();
		this.price = course.getPrice();
		this.comment = course.getComment();
		this.salesVolume = course.getSalesVolume();
		this.home = course.getHome();
		this.sort = course.getSort();
		this.stage = course.getStage();
		this.status = course.getStatus();
		if (course.getModifyTime() != null){
			this.modifyTime = DateUtil.format(course.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (course.getCreateTime() != null){
			this.createTime = DateUtil.format(course.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getTitle (){
		return title;
	}

	public void setTitle (String title) {
		this.title = title;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public Integer getLength (){
		return length;
	}

	public void setLength (Integer length) {
		this.length = length;
	}

	public String getDetailImgUrl (){
		return detailImgUrl;
	}

	public void setDetailImgUrl (String detailImgUrl) {
		this.detailImgUrl = detailImgUrl;
	}

	public BigDecimal getOriginalPrice (){
		return originalPrice;
	}

	public void setOriginalPrice (BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice (){
		return price;
	}

	public void setPrice (BigDecimal price) {
		this.price = price;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public Integer getSalesVolume (){
		return salesVolume;
	}

	public void setSalesVolume (Integer salesVolume) {
		this.salesVolume = salesVolume;
	}

	public String getHome (){
		return home;
	}

	public void setHome (String home) {
		this.home = home;
	}

	public Integer getSort (){
		return sort;
	}

	public void setSort (Integer sort) {
		this.sort = sort;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
