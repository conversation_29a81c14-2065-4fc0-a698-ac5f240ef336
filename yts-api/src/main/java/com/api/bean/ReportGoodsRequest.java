package com.api.bean;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 统计商品表请求对象
 *
 * @date 2025-07-20 16:36:02
 */
public class ReportGoodsRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "日期")
	private String date;

	@Schema(description = "最小日期")
	private String minDate;

	@Schema(description = "最大日期")
	private String maxDate;

	@Schema(description = "商品编号")
	private Long goodsId;

	@Schema(description = "商品编号集合")
	private List<Long> goodsIds;

	@Schema(description = "浏览数量")
	private Long browseNum;

	@Schema(description = "支付数量")
	private Long payNum;

	@Schema(description = "支付金额")
	private BigDecimal payAmount;

	@Schema(description = "退款数量")
	private Long refundNum;

	@Schema(description = "退款金额")
	private BigDecimal refundAmount;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;

	@Schema(description = "排序字段")
	private String sortType;

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public String getDate (){
		return date;
	}

	public void setDate (String date) {
		this.date = date;
	}

	public String getMinDate (){
		return minDate;
	}

	public void setMinDate (String minDate) {
		this.minDate = minDate;
	}

	public String getMaxDate (){
		return maxDate;
	}

	public void setMaxDate (String maxDate) {
		this.maxDate = maxDate;
	}

	public Long getGoodsId (){
		return goodsId;
	}

	public void setGoodsId (Long goodsId) {
		this.goodsId = goodsId;
	}

	public List<Long> getGoodsIds (){
		return goodsIds;
	}

	public void setGoodsIds (List<Long> goodsIds) {
		this.goodsIds = goodsIds;
	}

	public Long getBrowseNum (){
		return browseNum;
	}

	public void setBrowseNum (Long browseNum) {
		this.browseNum = browseNum;
	}

	public Long getPayNum (){
		return payNum;
	}

	public void setPayNum (Long payNum) {
		this.payNum = payNum;
	}

	public BigDecimal getPayAmount (){
		return payAmount;
	}

	public void setPayAmount (BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

	public Long getRefundNum (){
		return refundNum;
	}

	public void setRefundNum (Long refundNum) {
		this.refundNum = refundNum;
	}

	public BigDecimal getRefundAmount (){
		return refundAmount;
	}

	public void setRefundAmount (BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getSortType() {
		return sortType;
	}

	public void setSortType(String sortType) {
		this.sortType = sortType;
	}
}
