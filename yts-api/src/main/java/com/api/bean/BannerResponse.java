package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.constant.BannerJump;
import com.common.constant.BannerJumpCatalog;
import com.common.constant.PublicStage;
import com.common.util.DateUtil;
import com.domain.Banner;

/**
 * 轮播图表返回值对象
 *
 * @date 2025-07-19 10:58:28
 */
public class BannerResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "图片地址")
	private String imgUrl;

	@Schema(description = "是否跳转(0:跳转 1:不跳转)")
	private String jump;

	@Schema(description = "是否跳转名称(0:跳转 1:不跳转)")
	private String jumpName;

	@Schema(description = "跳转类型")
	private String jumpCatalog;

	@Schema(description = "跳转类型名称")
	private String jumpCatalogName;

	@Schema(description = "跳转编号")
	private Long jumpId;

	@Schema(description = "跳转产品名称")
	private String productName;

	@Schema(description = "上架状态(0:上架 1:下架)")
	private String stage;

	@Schema(description = "上架状态名称(0:上架 1:下架)")
	private String stageName;

	@Schema(description = "排序字段")
	private Integer sort;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public BannerResponse(){}

	public BannerResponse(Banner banner){
		this.id = banner.getId();
		this.imgUrl = banner.getImgUrl();
		this.jump = banner.getJump();
		this.jumpName = BannerJump.getName(banner.getJump());
		this.jumpCatalog = banner.getJumpCatalog();
		this.jumpCatalogName = BannerJumpCatalog.getName(banner.getJumpCatalog());
		this.jumpId = banner.getJumpId();
		this.stage = banner.getStage();
		this.stageName = PublicStage.getName(banner.getStage());
		this.sort = banner.getSort();
		this.status = banner.getStatus();
		if (banner.getModifyTime() != null){
			this.modifyTime = DateUtil.format(banner.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (banner.getCreateTime() != null){
			this.createTime = DateUtil.format(banner.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getJump (){
		return jump;
	}

	public void setJump (String jump) {
		this.jump = jump;
	}

	public String getJumpCatalog (){
		return jumpCatalog;
	}

	public void setJumpCatalog (String jumpCatalog) {
		this.jumpCatalog = jumpCatalog;
	}

	public Long getJumpId (){
		return jumpId;
	}

	public void setJumpId (Long jumpId) {
		this.jumpId = jumpId;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public Integer getSort (){
		return sort;
	}

	public void setSort (Integer sort) {
		this.sort = sort;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getStageName() {
		return stageName;
	}

	public void setStageName(String stageName) {
		this.stageName = stageName;
	}

	public String getJumpCatalogName() {
		return jumpCatalogName;
	}

	public void setJumpCatalogName(String jumpCatalogName) {
		this.jumpCatalogName = jumpCatalogName;
	}

	public String getJumpName() {
		return jumpName;
	}

	public void setJumpName(String jumpName) {
		this.jumpName = jumpName;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}
}
