package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.constant.PublicStage;
import com.common.util.DateUtil;
import com.domain.SysUser;

/**
 * 系统用户表返回值对象
 *
 * @date 2025-07-16 21:03:31
 */
public class SysUserResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "姓名")
	private String name;

	@Schema(description = "用户名")
	private String username;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "密码")
	private String password;

	@Schema(description = "启用/禁用(0: 启用 1:禁用)")
	private String stage;

	@Schema(description = "启用/禁用名称(0: 启用 1:禁用)")
	private String stageName;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "token")
	private String token;

	public SysUserResponse(){}

	public SysUserResponse(SysUser sysUser){
		this.id = sysUser.getId();
		this.name = sysUser.getName();
		this.username = sysUser.getUsername();
		this.mobile = sysUser.getMobile();
		this.password = sysUser.getPassword();
		this.stage = sysUser.getStage();
		this.stageName = PublicStage.getName(sysUser.getStage());
		this.status = sysUser.getStatus();
		if (sysUser.getModifyTime() != null){
			this.modifyTime = DateUtil.format(sysUser.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (sysUser.getCreateTime() != null){
			this.createTime = DateUtil.format(sysUser.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getUsername (){
		return username;
	}

	public void setUsername (String username) {
		this.username = username;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getPassword (){
		return password;
	}

	public void setPassword (String password) {
		this.password = password;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getStageName() {
		return stageName;
	}

	public void setStageName(String stageName) {
		this.stageName = stageName;
	}
}
