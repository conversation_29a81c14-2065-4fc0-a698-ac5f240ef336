package com.api.bean;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.ReportGoods;

/**
 * 统计商品表返回值对象
 *
 * @date 2025-07-20 16:36:02
 */
public class ReportGoodsResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "日期")
	private String date;

	@Schema(description = "商品编号")
	private Long goodsId;

	@Schema(description = "商品名称")
	private String goodsName;

	@Schema(description = "首个商品封面")
	private String firstImgUrl;

	@Schema(description = "浏览数量")
	private Long browseNum;

	@Schema(description = "支付数量")
	private Long payNum;

	@Schema(description = "支付金额")
	private BigDecimal payAmount;

	@Schema(description = "退款数量")
	private Long refundNum;

	@Schema(description = "退款金额")
	private BigDecimal refundAmount;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "时间集合")
	private List<String> dates;

	@Schema(description = "时间集合")
	private List<ReportGoodsResponse> reportGoodsResponses;

	public ReportGoodsResponse(){}

	public ReportGoodsResponse(ReportGoods reportGoods){
		this.id = reportGoods.getId();
		if (reportGoods.getDate() != null){
			this.date = DateUtil.format(reportGoods.getDate(), App.DATE_FORMAT);
		}
		this.goodsId = reportGoods.getGoodsId();
		this.browseNum = reportGoods.getBrowseNum();
		this.payNum = reportGoods.getPayNum();
		this.payAmount = reportGoods.getPayAmount();
		this.refundNum = reportGoods.getRefundNum();
		this.refundAmount = reportGoods.getRefundAmount();
		this.status = reportGoods.getStatus();
		if (reportGoods.getModifyTime() != null){
			this.modifyTime = DateUtil.format(reportGoods.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (reportGoods.getCreateTime() != null){
			this.createTime = DateUtil.format(reportGoods.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getDate (){
		return date;
	}

	public void setDate (String date) {
		this.date = date;
	}

	public Long getGoodsId (){
		return goodsId;
	}

	public void setGoodsId (Long goodsId) {
		this.goodsId = goodsId;
	}

	public Long getBrowseNum (){
		return browseNum;
	}

	public void setBrowseNum (Long browseNum) {
		this.browseNum = browseNum;
	}

	public Long getPayNum (){
		return payNum;
	}

	public void setPayNum (Long payNum) {
		this.payNum = payNum;
	}

	public BigDecimal getPayAmount (){
		return payAmount;
	}

	public void setPayAmount (BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

	public Long getRefundNum (){
		return refundNum;
	}

	public void setRefundNum (Long refundNum) {
		this.refundNum = refundNum;
	}

	public BigDecimal getRefundAmount (){
		return refundAmount;
	}

	public void setRefundAmount (BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public List<ReportGoodsResponse> getReportGoodsResponses() {
		return reportGoodsResponses;
	}

	public void setReportGoodsResponses(List<ReportGoodsResponse> reportGoodsResponses) {
		this.reportGoodsResponses = reportGoodsResponses;
	}

	public List<String> getDates() {
		return dates;
	}

	public void setDates(List<String> dates) {
		this.dates = dates;
	}

	public String getGoodsName() {
		return goodsName;
	}

	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}

	public String getFirstImgUrl() {
		return firstImgUrl;
	}

	public void setFirstImgUrl(String firstImgUrl) {
		this.firstImgUrl = firstImgUrl;
	}
}
