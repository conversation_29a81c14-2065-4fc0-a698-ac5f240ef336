package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.OrderRefundInfo;

/**
 * 订单退款记录表返回值对象
 *
 * @date 2025-07-29 22:28:19
 */
public class OrderRefundInfoResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "商户订单编号")
	private String orderNo;

	@Schema(description = "商户退款单编号")
	private String refundNo;

	@Schema(description = "支付系统退款单号")
	private String refundId;

	@Schema(description = "原订单金额(分)")
	private Integer totalFee;

	@Schema(description = "退款金额(分)")
	private Integer refund;

	@Schema(description = "退款原因")
	private String reason;

	@Schema(description = "退款状态")
	private String refundStatus;

	@Schema(description = "申请退款返回参数")
	private String contentReturn;

	@Schema(description = "退款结果通知参数")
	private String contentNotify;

	@Schema(description = "支付类型")
	private String paymentType;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public OrderRefundInfoResponse(){}

	public OrderRefundInfoResponse(OrderRefundInfo orderRefundInfo){
		this.id = orderRefundInfo.getId();
		this.orderNo = orderRefundInfo.getOrderNo();
		this.refundNo = orderRefundInfo.getRefundNo();
		this.refundId = orderRefundInfo.getRefundId();
		this.totalFee = orderRefundInfo.getTotalFee();
		this.refund = orderRefundInfo.getRefund();
		this.reason = orderRefundInfo.getReason();
		this.refundStatus = orderRefundInfo.getRefundStatus();
		this.contentReturn = orderRefundInfo.getContentReturn();
		this.contentNotify = orderRefundInfo.getContentNotify();
		this.paymentType = orderRefundInfo.getPaymentType();
		this.status = orderRefundInfo.getStatus();
		if (orderRefundInfo.getModifyTime() != null){
			this.modifyTime = DateUtil.format(orderRefundInfo.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (orderRefundInfo.getCreateTime() != null){
			this.createTime = DateUtil.format(orderRefundInfo.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getOrderNo (){
		return orderNo;
	}

	public void setOrderNo (String orderNo) {
		this.orderNo = orderNo;
	}

	public String getRefundNo (){
		return refundNo;
	}

	public void setRefundNo (String refundNo) {
		this.refundNo = refundNo;
	}

	public String getRefundId (){
		return refundId;
	}

	public void setRefundId (String refundId) {
		this.refundId = refundId;
	}

	public Integer getTotalFee (){
		return totalFee;
	}

	public void setTotalFee (Integer totalFee) {
		this.totalFee = totalFee;
	}

	public Integer getRefund (){
		return refund;
	}

	public void setRefund (Integer refund) {
		this.refund = refund;
	}

	public String getReason (){
		return reason;
	}

	public void setReason (String reason) {
		this.reason = reason;
	}

	public String getRefundStatus (){
		return refundStatus;
	}

	public void setRefundStatus (String refundStatus) {
		this.refundStatus = refundStatus;
	}

	public String getContentReturn (){
		return contentReturn;
	}

	public void setContentReturn (String contentReturn) {
		this.contentReturn = contentReturn;
	}

	public String getContentNotify (){
		return contentNotify;
	}

	public void setContentNotify (String contentNotify) {
		this.contentNotify = contentNotify;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}
}
