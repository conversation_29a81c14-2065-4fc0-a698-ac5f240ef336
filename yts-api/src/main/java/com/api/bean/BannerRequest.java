package com.api.bean;

import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 轮播图表请求对象
 *
 * @date 2025-07-19 10:58:28
 */
public class BannerRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "图片地址")
	private String imgUrl;

	@Schema(description = "是否跳转(0:跳转 1:不跳转)")
	private String jump;

	@Schema(description = "跳转类型")
	private String jumpCatalog;

	@Schema(description = "跳转编号")
	private Long jumpId;

	@Schema(description = "跳转编号集合")
	private List<Long> jumpIds;

	@Schema(description = "上架状态(0:上架 1:下架)")
	private String stage;

	@Schema(description = "排序字段")
	private Integer sort;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getJump (){
		return jump;
	}

	public void setJump (String jump) {
		this.jump = jump;
	}

	public String getJumpCatalog (){
		return jumpCatalog;
	}

	public void setJumpCatalog (String jumpCatalog) {
		this.jumpCatalog = jumpCatalog;
	}

	public Long getJumpId (){
		return jumpId;
	}

	public void setJumpId (Long jumpId) {
		this.jumpId = jumpId;
	}

	public List<Long> getJumpIds (){
		return jumpIds;
	}

	public void setJumpIds (List<Long> jumpIds) {
		this.jumpIds = jumpIds;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public Integer getSort (){
		return sort;
	}

	public void setSort (Integer sort) {
		this.sort = sort;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

}
