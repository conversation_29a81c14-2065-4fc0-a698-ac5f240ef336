package com.api.bean;

import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 订单退款记录表请求对象
 *
 * @date 2025-07-29 22:28:19
 */
public class OrderRefundInfoRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "商户订单编号")
	private String orderNo;

	@Schema(description = "商户退款单编号")
	private String refundNo;

	@Schema(description = "支付系统退款单号")
	private String refundId;

	@Schema(description = "支付系统退款单号集合")
	private List<String> refundIds;

	@Schema(description = "原订单金额(分)")
	private Integer totalFee;

	@Schema(description = "退款金额(分)")
	private Integer refund;

	@Schema(description = "退款原因")
	private String reason;

	@Schema(description = "退款状态")
	private String refundStatus;

	@Schema(description = "申请退款返回参数")
	private String contentReturn;

	@Schema(description = "退款结果通知参数")
	private String contentNotify;

	@Schema(description = "支付类型")
	private String paymentType;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public String getOrderNo (){
		return orderNo;
	}

	public void setOrderNo (String orderNo) {
		this.orderNo = orderNo;
	}

	public String getRefundNo (){
		return refundNo;
	}

	public void setRefundNo (String refundNo) {
		this.refundNo = refundNo;
	}

	public String getRefundId (){
		return refundId;
	}

	public void setRefundId (String refundId) {
		this.refundId = refundId;
	}

	public List<String> getRefundIds (){
		return refundIds;
	}

	public void setRefundIds (List<String> refundIds) {
		this.refundIds = refundIds;
	}

	public Integer getTotalFee (){
		return totalFee;
	}

	public void setTotalFee (Integer totalFee) {
		this.totalFee = totalFee;
	}

	public Integer getRefund (){
		return refund;
	}

	public void setRefund (Integer refund) {
		this.refund = refund;
	}

	public String getReason (){
		return reason;
	}

	public void setReason (String reason) {
		this.reason = reason;
	}

	public String getRefundStatus (){
		return refundStatus;
	}

	public void setRefundStatus (String refundStatus) {
		this.refundStatus = refundStatus;
	}

	public String getContentReturn (){
		return contentReturn;
	}

	public void setContentReturn (String contentReturn) {
		this.contentReturn = contentReturn;
	}

	public String getContentNotify (){
		return contentNotify;
	}

	public void setContentNotify (String contentNotify) {
		this.contentNotify = contentNotify;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}
}
