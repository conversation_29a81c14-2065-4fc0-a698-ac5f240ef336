package com.api.bean;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;

import com.common.bean.Bean;

/**
 * 订单商品明细返回值对象
 *
 * @date 2025-07-29
 */
public class OrderProductDetailResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "商品名称")
	private String productName;

	@Schema(description = "规格")
	private String specification;

	@Schema(description = "商品类型")
	private String productType;

	@Schema(description = "单价")
	private BigDecimal unitPrice;

	@Schema(description = "数量")
	private Integer quantity;

	@Schema(description = "单位")
	private String unit;

	@Schema(description = "金额")
	private BigDecimal amount;

	@Schema(description = "团长佣金")
	private BigDecimal commission;

	@Schema(description = "分佣比例")
	private String commissionRatio;

	@Schema(description = "销售老师佣金")
	private BigDecimal salesTeacherCommission;

	@Schema(description = "销售老师佣金比例(%)")
	private String salesTeacherCommissionRatio;

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSpecification() {
		return specification;
	}

	public void setSpecification(String specification) {
		this.specification = specification;
	}

	public String getProductType() {
		return productType;
	}

	public void setProductType(String productType) {
		this.productType = productType;
	}

	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getCommissionRatio() {
		return commissionRatio;
	}

	public void setCommissionRatio(String commissionRatio) {
		this.commissionRatio = commissionRatio;
	}

	public String getSalesTeacherCommissionRatio() {
		return salesTeacherCommissionRatio;
	}

	public void setSalesTeacherCommissionRatio(String salesTeacherCommissionRatio) {
		this.salesTeacherCommissionRatio = salesTeacherCommissionRatio;
	}

	public BigDecimal getCommission() {
		return commission;
	}

	public void setCommission(BigDecimal commission) {
		this.commission = commission;
	}

	public BigDecimal getSalesTeacherCommission() {
		return salesTeacherCommission;
	}

	public void setSalesTeacherCommission(BigDecimal salesTeacherCommission) {
		this.salesTeacherCommission = salesTeacherCommission;
	}
}
