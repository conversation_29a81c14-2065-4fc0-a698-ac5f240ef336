package com.api.bean;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.UserCommissionCash;

/**
 * 用户佣金提现表返回值对象
 *
 * @date 2025-07-19 10:58:27
 */
public class UserCommissionCashResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "用户编号")
	private Long userId;

	@Schema(description = "总佣金")
	private BigDecimal sumCommission;

	@Schema(description = "订单数量")
	private Integer orderSize;

	@Schema(description = "操作人编号")
	private Long optSysUserId;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public UserCommissionCashResponse(){}

	public UserCommissionCashResponse(UserCommissionCash userCommissionCash){
		this.id = userCommissionCash.getId();
		this.userId = userCommissionCash.getUserId();
		this.sumCommission = userCommissionCash.getSumCommission();
		this.orderSize = userCommissionCash.getOrderSize();
		this.optSysUserId = userCommissionCash.getOptSysUserId();
		this.status = userCommissionCash.getStatus();
		if (userCommissionCash.getModifyTime() != null){
			this.modifyTime = DateUtil.format(userCommissionCash.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (userCommissionCash.getCreateTime() != null){
			this.createTime = DateUtil.format(userCommissionCash.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public BigDecimal getSumCommission (){
		return sumCommission;
	}

	public void setSumCommission (BigDecimal sumCommission) {
		this.sumCommission = sumCommission;
	}

	public Integer getOrderSize (){
		return orderSize;
	}

	public void setOrderSize (Integer orderSize) {
		this.orderSize = orderSize;
	}

	public Long getOptSysUserId (){
		return optSysUserId;
	}

	public void setOptSysUserId (Long optSysUserId) {
		this.optSysUserId = optSysUserId;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
