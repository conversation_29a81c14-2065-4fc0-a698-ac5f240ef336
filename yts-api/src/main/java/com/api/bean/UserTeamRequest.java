package com.api.bean;

import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户团队表请求对象
 *
 * @date 2025-07-19 10:58:27
 */
public class UserTeamRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "队长编号")
	private Long captainUserId;

	@Schema(description = "队长编号集合")
	private List<Long> captainUserIds;

	@Schema(description = "队员编号")
	private Long teamMemberUserId;

	@Schema(description = "队员编号集合")
	private List<Long> teamMemberUserIds;

	@Schema(description = "绑定类型")
	private String bindingCatalog;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;

	@Schema(description = "关键字")
	private String keyword;

	@Schema(description = "邀请码")
	private Long invitationCode;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public Long getCaptainUserId (){
		return captainUserId;
	}

	public void setCaptainUserId (Long captainUserId) {
		this.captainUserId = captainUserId;
	}

	public List<Long> getCaptainUserIds (){
		return captainUserIds;
	}

	public void setCaptainUserIds (List<Long> captainUserIds) {
		this.captainUserIds = captainUserIds;
	}

	public Long getTeamMemberUserId (){
		return teamMemberUserId;
	}

	public void setTeamMemberUserId (Long teamMemberUserId) {
		this.teamMemberUserId = teamMemberUserId;
	}

	public List<Long> getTeamMemberUserIds (){
		return teamMemberUserIds;
	}

	public void setTeamMemberUserIds (List<Long> teamMemberUserIds) {
		this.teamMemberUserIds = teamMemberUserIds;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public String getBindingCatalog() {
		return bindingCatalog;
	}

	public void setBindingCatalog(String bindingCatalog) {
		this.bindingCatalog = bindingCatalog;
	}

	public Long getInvitationCode() {
		return invitationCode;
	}

	public void setInvitationCode(Long invitationCode) {
		this.invitationCode = invitationCode;
	}
}
