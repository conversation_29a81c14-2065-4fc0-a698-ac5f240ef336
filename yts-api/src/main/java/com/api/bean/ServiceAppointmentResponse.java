package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.ServiceAppointment;

/**
 * 服务预约表返回值对象
 *
 * @date 2025-07-19 10:58:27
 */
public class ServiceAppointmentResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "用户id")
	private Long userId;

	@Schema(description = "用户名称")
	private String userName;

	@Schema(description = "服务id")
	private Long serviceId;

	@Schema(description = "服务名称")
	private String serviceName;

	@Schema(description = "姓名")
	private String name;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "备注")
	private String remark;

	@Schema(description = "是否联系(0:已联系 1:未联系)")
	private String contact;

	@Schema(description = "描述")
	private String comment;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public ServiceAppointmentResponse(){}

	public ServiceAppointmentResponse(ServiceAppointment serviceAppointment){
		this.id = serviceAppointment.getId();
		this.userId = serviceAppointment.getUserId();
		this.serviceId = serviceAppointment.getServiceId();
		this.name = serviceAppointment.getName();
		this.mobile = serviceAppointment.getMobile();
		this.remark = serviceAppointment.getRemark();
		this.contact = serviceAppointment.getContact();
		this.comment = serviceAppointment.getComment();
		this.status = serviceAppointment.getStatus();
		if (serviceAppointment.getModifyTime() != null){
			this.modifyTime = DateUtil.format(serviceAppointment.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (serviceAppointment.getCreateTime() != null){
			this.createTime = DateUtil.format(serviceAppointment.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public Long getServiceId (){
		return serviceId;
	}

	public void setServiceId (Long serviceId) {
		this.serviceId = serviceId;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getRemark (){
		return remark;
	}

	public void setRemark (String remark) {
		this.remark = remark;
	}

	public String getContact (){
		return contact;
	}

	public void setContact (String contact) {
		this.contact = contact;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getServiceName() {
		return serviceName;
	}

	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
