package com.api.bean;

import java.math.BigDecimal;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 商品表请求对象
 *
 * @date 2025-07-27 11:42:53
 */
public class GoodsRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "商品名称")
	private String name;

	@Schema(description = "商品单位")
	private String unit;

	@Schema(description = "商品封面")
	private String imgUrl;

	@Schema(description = "商品类型")
	private String catalog;

	@Schema(description = "商品分类编号")
	private Long goodsTypeId;

	@Schema(description = "商品分类编号集合")
	private List<Long> goodsTypeIds;

	@Schema(description = "成本价")
	private BigDecimal buyingPrice;

	@Schema(description = "划线价")
	private BigDecimal originalPrice;

	@Schema(description = "售价")
	private BigDecimal price;

	@Schema(description = "最低售价")
	private BigDecimal minPrice;

	@Schema(description = "最高售价")
	private BigDecimal maxPrice;

	@Schema(description = "毛利(售价-成本价)")
	private BigDecimal grossProfit;

	@Schema(description = "是否分佣(0:是 1:否)")
	private String openCommission;

	@Schema(description = "团长佣金比例(%)")
	private BigDecimal commissionRatio;

	@Schema(description = "销售老师佣金比例(%)")
	private BigDecimal salesTeacherCommissionRatio;

	@Schema(description = "商品详情图片")
	private String detailImgUrl;

	@Schema(description = "描述")
	private String comment;

	@Schema(description = "虚拟销量")
	private Integer salesVolume;

	@Schema(description = "真实销量")
	private Integer realSalesVolume;

	@Schema(description = "最小真实销量")
	private Integer minRealSalesVolume;

	@Schema(description = "最大真实销量")
	private Integer maxRealSalesVolume;

	@Schema(description = "是否首页展示(0:展示 1:不展示)")
	private String home;

	@Schema(description = "上架状态(0:上架 1:下架)")
	private String stage;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;

	@Schema(description = "排序字段")
	private String sortType;

	@Schema(description = "商品规格类型表请求对象集合")
	private List<GoodsSpecificationTypeRequest> goodsSpecificationTypeRequests;

	@Schema(description = "商品规格sku表请求对象集合")
	private List<GoodsSpecificationSkuRequest> goodsSpecificationSkuRequests;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getCatalog() {
		return catalog;
	}

	public void setCatalog(String catalog) {
		this.catalog = catalog;
	}

	public Long getGoodsTypeId() {
		return goodsTypeId;
	}

	public void setGoodsTypeId(Long goodsTypeId) {
		this.goodsTypeId = goodsTypeId;
	}

	public List<Long> getGoodsTypeIds() {
		return goodsTypeIds;
	}

	public void setGoodsTypeIds(List<Long> goodsTypeIds) {
		this.goodsTypeIds = goodsTypeIds;
	}

	public BigDecimal getBuyingPrice() {
		return buyingPrice;
	}

	public void setBuyingPrice(BigDecimal buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getMinPrice() {
		return minPrice;
	}

	public void setMinPrice(BigDecimal minPrice) {
		this.minPrice = minPrice;
	}

	public BigDecimal getMaxPrice() {
		return maxPrice;
	}

	public void setMaxPrice(BigDecimal maxPrice) {
		this.maxPrice = maxPrice;
	}

	public BigDecimal getGrossProfit() {
		return grossProfit;
	}

	public void setGrossProfit(BigDecimal grossProfit) {
		this.grossProfit = grossProfit;
	}

	public String getOpenCommission() {
		return openCommission;
	}

	public void setOpenCommission(String openCommission) {
		this.openCommission = openCommission;
	}

	public BigDecimal getCommissionRatio() {
		return commissionRatio;
	}

	public void setCommissionRatio(BigDecimal commissionRatio) {
		this.commissionRatio = commissionRatio;
	}

	public BigDecimal getSalesTeacherCommissionRatio() {
		return salesTeacherCommissionRatio;
	}

	public void setSalesTeacherCommissionRatio(BigDecimal salesTeacherCommissionRatio) {
		this.salesTeacherCommissionRatio = salesTeacherCommissionRatio;
	}

	public String getDetailImgUrl() {
		return detailImgUrl;
	}

	public void setDetailImgUrl(String detailImgUrl) {
		this.detailImgUrl = detailImgUrl;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public Integer getSalesVolume() {
		return salesVolume;
	}

	public void setSalesVolume(Integer salesVolume) {
		this.salesVolume = salesVolume;
	}

	public Integer getRealSalesVolume() {
		return realSalesVolume;
	}

	public void setRealSalesVolume(Integer realSalesVolume) {
		this.realSalesVolume = realSalesVolume;
	}

	public Integer getMinRealSalesVolume() {
		return minRealSalesVolume;
	}

	public void setMinRealSalesVolume(Integer minRealSalesVolume) {
		this.minRealSalesVolume = minRealSalesVolume;
	}

	public Integer getMaxRealSalesVolume() {
		return maxRealSalesVolume;
	}

	public void setMaxRealSalesVolume(Integer maxRealSalesVolume) {
		this.maxRealSalesVolume = maxRealSalesVolume;
	}

	public String getStage() {
		return stage;
	}

	public void setStage(String stage) {
		this.stage = stage;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getSortType() {
		return sortType;
	}

	public void setSortType(String sortType) {
		this.sortType = sortType;
	}

	public List<GoodsSpecificationTypeRequest> getGoodsSpecificationTypeRequests() {
		return goodsSpecificationTypeRequests;
	}

	public void setGoodsSpecificationTypeRequests(List<GoodsSpecificationTypeRequest> goodsSpecificationTypeRequests) {
		this.goodsSpecificationTypeRequests = goodsSpecificationTypeRequests;
	}

	public List<GoodsSpecificationSkuRequest> getGoodsSpecificationSkuRequests() {
		return goodsSpecificationSkuRequests;
	}

	public void setGoodsSpecificationSkuRequests(List<GoodsSpecificationSkuRequest> goodsSpecificationSkuRequests) {
		this.goodsSpecificationSkuRequests = goodsSpecificationSkuRequests;
	}

	public String getHome() {
		return home;
	}

	public void setHome(String home) {
		this.home = home;
	}
}
