package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.District;

/**
 * 区(县)表返回值对象
 *
 * @date 2025-07-23 22:58:35
 */
public class DistrictResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "城市编号")
	private Long cityId;

	@Schema(description = "区(县)名称")
	private String name;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public DistrictResponse(){}

	public DistrictResponse(District district){
		this.id = district.getId();
		this.cityId = district.getCityId();
		this.name = district.getName();
		this.status = district.getStatus();
		if (district.getModifyTime() != null){
			this.modifyTime = DateUtil.format(district.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (district.getCreateTime() != null){
			this.createTime = DateUtil.format(district.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public Long getCityId (){
		return cityId;
	}

	public void setCityId (Long cityId) {
		this.cityId = cityId;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
