package com.api.bean;

import java.math.BigDecimal;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 订单产品表请求对象
 *
 * @date 2025-07-24 23:22:31
 */
public class OrderProductRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "订单编号")
	private Long orderId;

	@Schema(description = "订单编号集合")
	private List<Long> orderIds;

	@Schema(description = "类型(0:课程 1:商品)")
	private String productCatalog;

	@Schema(description = "产品编号")
	private Long productId;

	@Schema(description = "产品编号集合")
	private List<Long> productIds;

	@Schema(description = "数量")
	private Integer number;

	@Schema(description = "实付金额")
	private BigDecimal amount;

	@Schema(description = "商品名称")
	private String productName;

	@Schema(description = "SKUid")
	private Long skuId;

	@Schema(description = "商品规格值")
	private String specValues;

	@Schema(description = "图片")
	private String imgUrl;

	@Schema(description = "商品价格")
	private BigDecimal price;

	@Schema(description = "商品原价")
	private BigDecimal originalPrice;

	@Schema(description = "商品进价")
	private BigDecimal buyingPrice;

	@Schema(description = "商品利润")
	private BigDecimal grossProfit;

	@Schema(description = "团长佣金比例(%)")
	private BigDecimal commissionRatio;

	@Schema(description = "团长佣金")
	private BigDecimal commission;

	@Schema(description = "销售老师佣金比例(%)")
	private BigDecimal salesTeacherCommissionRatio;

	@Schema(description = "销售老师佣金")
	private BigDecimal salesTeacherCommission;

	@Schema(description = "商品单位")
	private String unit;

	@Schema(description = "商品类型")
	private String goodsCatalog;

	@Schema(description = "商品分类编号")
	private Long goodsTypeId;

	@Schema(description = "商品分类名称")
	private String goodsTypeName;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public Long getOrderId (){
		return orderId;
	}

	public void setOrderId (Long orderId) {
		this.orderId = orderId;
	}

	public List<Long> getOrderIds (){
		return orderIds;
	}

	public void setOrderIds (List<Long> orderIds) {
		this.orderIds = orderIds;
	}

	public String getProductCatalog (){
		return productCatalog;
	}

	public void setProductCatalog (String productCatalog) {
		this.productCatalog = productCatalog;
	}

	public Long getProductId (){
		return productId;
	}

	public void setProductId (Long productId) {
		this.productId = productId;
	}

	public List<Long> getProductIds (){
		return productIds;
	}

	public void setProductIds (List<Long> productIds) {
		this.productIds = productIds;
	}

	public Integer getNumber (){
		return number;
	}

	public void setNumber (Integer number) {
		this.number = number;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getCommissionRatio (){
		return commissionRatio;
	}

	public void setCommissionRatio (BigDecimal commissionRatio) {
		this.commissionRatio = commissionRatio;
	}

	public BigDecimal getCommission (){
		return commission;
	}

	public void setCommission (BigDecimal commission) {
		this.commission = commission;
	}

	public BigDecimal getSalesTeacherCommissionRatio (){
		return salesTeacherCommissionRatio;
	}

	public void setSalesTeacherCommissionRatio (BigDecimal salesTeacherCommissionRatio) {
		this.salesTeacherCommissionRatio = salesTeacherCommissionRatio;
	}

	public BigDecimal getSalesTeacherCommission (){
		return salesTeacherCommission;
	}

	public void setSalesTeacherCommission (BigDecimal salesTeacherCommission) {
		this.salesTeacherCommission = salesTeacherCommission;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSpecValues() {
		return specValues;
	}

	public void setSpecValues(String specValues) {
		this.specValues = specValues;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getBuyingPrice() {
		return buyingPrice;
	}

	public void setBuyingPrice(BigDecimal buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public BigDecimal getGrossProfit() {
		return grossProfit;
	}

	public void setGrossProfit(BigDecimal grossProfit) {
		this.grossProfit = grossProfit;
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getGoodsCatalog() {
		return goodsCatalog;
	}

	public void setGoodsCatalog(String goodsCatalog) {
		this.goodsCatalog = goodsCatalog;
	}

	public Long getGoodsTypeId() {
		return goodsTypeId;
	}

	public void setGoodsTypeId(Long goodsTypeId) {
		this.goodsTypeId = goodsTypeId;
	}

	public String getGoodsTypeName() {
		return goodsTypeName;
	}

	public void setGoodsTypeName(String goodsTypeName) {
		this.goodsTypeName = goodsTypeName;
	}
}
