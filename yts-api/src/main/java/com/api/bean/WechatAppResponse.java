package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.WechatApp;

/**
 * 微信应用表返回值对象
 *
 * @date 2025-07-16 21:29:32
 */
public class WechatAppResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "应用ID")
	private String appid;

	@Schema(description = "应用密钥")
	private String secret;

	@Schema(description = "应用名称")
	private String name;

	@Schema(description = "访问凭证")
	private String accessToken;

	@Schema(description = "凭证刷新日期")
	private String accessTokenDatetime;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public WechatAppResponse(){}

	public WechatAppResponse(WechatApp wechatApp){
		this.id = wechatApp.getId();
		this.appid = wechatApp.getAppid();
		this.secret = wechatApp.getSecret();
		this.name = wechatApp.getName();
		this.accessToken = wechatApp.getAccessToken();
		if (wechatApp.getAccessTokenDatetime() != null){
			this.accessTokenDatetime = DateUtil.format(wechatApp.getAccessTokenDatetime(), App.DATETIME_FORMAT);
		}
		this.status = wechatApp.getStatus();
		if (wechatApp.getModifyTime() != null){
			this.modifyTime = DateUtil.format(wechatApp.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (wechatApp.getCreateTime() != null){
			this.createTime = DateUtil.format(wechatApp.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getAppid (){
		return appid;
	}

	public void setAppid (String appid) {
		this.appid = appid;
	}

	public String getSecret (){
		return secret;
	}

	public void setSecret (String secret) {
		this.secret = secret;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getAccessToken (){
		return accessToken;
	}

	public void setAccessToken (String accessToken) {
		this.accessToken = accessToken;
	}

	public String getAccessTokenDatetime (){
		return accessTokenDatetime;
	}

	public void setAccessTokenDatetime (String accessTokenDatetime) {
		this.accessTokenDatetime = accessTokenDatetime;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
