package com.api.bean;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.GoodsSpecificationType;

/**
 * 商品规格类型表返回值对象
 *
 * @date 2025-07-27 21:26:17
 */
public class GoodsSpecificationTypeResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "商品id")
	private Long goodsId;

	@Schema(description = "规格类型名称")
	private String name;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "规格值列表")
	private List<GoodsSpecificationValueResponse> goodsSpecificationValueResponses;

	public GoodsSpecificationTypeResponse(){}

	public GoodsSpecificationTypeResponse(GoodsSpecificationType goodsSpecificationType){
		this.id = goodsSpecificationType.getId();
		this.goodsId = goodsSpecificationType.getGoodsId();
		this.name = goodsSpecificationType.getName();
		this.status = goodsSpecificationType.getStatus();
		if (goodsSpecificationType.getModifyTime() != null){
			this.modifyTime = DateUtil.format(goodsSpecificationType.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (goodsSpecificationType.getCreateTime() != null){
			this.createTime = DateUtil.format(goodsSpecificationType.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public List<GoodsSpecificationValueResponse> getGoodsSpecificationValueResponses() {
		return goodsSpecificationValueResponses;
	}

	public void setGoodsSpecificationValueResponses(List<GoodsSpecificationValueResponse> goodsSpecificationValueResponses) {
		this.goodsSpecificationValueResponses = goodsSpecificationValueResponses;
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public Long getGoodsId (){
		return goodsId;
	}

	public void setGoodsId (Long goodsId) {
		this.goodsId = goodsId;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
