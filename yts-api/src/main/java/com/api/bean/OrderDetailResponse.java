package com.api.bean;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

import com.common.bean.Bean;

/**
 * 订单详情返回值对象
 *
 * @date 2025-07-29
 */
public class OrderDetailResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "订单号")
	private String orderCode;

	@Schema(description = "下单时间")
	private String createTime;

	@Schema(description = "支付时间")
	private String payTime;

	@Schema(description = "用户名称")
	private String userName;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "用户类型")
	private String userType;

	@Schema(description = "支付状态")
	private String payStatus;

	@Schema(description = "订单状态")
	private String orderStatus;

	@Schema(description = "订单总额")
	private BigDecimal totalAmount;

	@Schema(description = "退款金额")
	private BigDecimal refundAmount;

	@Schema(description = "团长总佣金")
	private BigDecimal sumCommission;

	@Schema(description = "销售老师佣金金额")
	private BigDecimal sumSalesTeacherCommission;

	@Schema(description = "商品明细列表")
	private List<OrderProductDetailResponse> productDetails;

	public String getOrderCode() {
		return orderCode;
	}

	public void setOrderCode(String orderCode) {
		this.orderCode = orderCode;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public String getPayStatus() {
		return payStatus;
	}

	public void setPayStatus(String payStatus) {
		this.payStatus = payStatus;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public List<OrderProductDetailResponse> getProductDetails() {
		return productDetails;
	}

	public void setProductDetails(List<OrderProductDetailResponse> productDetails) {
		this.productDetails = productDetails;
	}

	public String getPayTime() {
		return payTime;
	}

	public void setPayTime(String payTime) {
		this.payTime = payTime;
	}

	public BigDecimal getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public BigDecimal getSumCommission() {
		return sumCommission;
	}

	public void setSumCommission(BigDecimal sumCommission) {
		this.sumCommission = sumCommission;
	}

	public BigDecimal getSumSalesTeacherCommission() {
		return sumSalesTeacherCommission;
	}

	public void setSumSalesTeacherCommission(BigDecimal sumSalesTeacherCommission) {
		this.sumSalesTeacherCommission = sumSalesTeacherCommission;
	}
}
