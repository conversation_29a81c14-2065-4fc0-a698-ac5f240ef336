package com.api.bean;

import com.common.bean.Bean;

public class UserToken extends Bean {

	private static final long serialVersionUID = 1L;
	/**
	 * tokenID
	 */
	private Long id;
	/**
	 * 系统用户名称
	 */
	private String username;
	/**
	 * 用户名称
	 */
	private String name;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
