package com.api.bean;

import com.common.bean.Bean;
import java.math.BigDecimal;

public class ReportHomeResponse extends Bean {
	private static final long serialVersionUID = 1L;

	/**
	 * 今日金额
	 */
	private BigDecimal todayAmount;

	/**
	 * 今日订单数
	 */
	private Integer todayOrderCount;

	/**
	 * 订单总数
	 */
	private Integer totalOrderCount;

	/**
	 * 订单总金额
	 */
	private BigDecimal totalOrderAmount;

	/**
	 * 佣金订单数
	 */
	private Integer commissionOrderCount;

	/**
	 * 佣金总金额
	 */
	private BigDecimal totalCommissionAmount;

	/**
	 * 团员人数
	 */
	private Integer memberCount;

	// Getter and Setter methods
	public BigDecimal getTodayAmount() {
		return todayAmount;
	}

	public void setTodayAmount(BigDecimal todayAmount) {
		this.todayAmount = todayAmount;
	}

	public Integer getTodayOrderCount() {
		return todayOrderCount;
	}

	public void setTodayOrderCount(Integer todayOrderCount) {
		this.todayOrderCount = todayOrderCount;
	}

	public Integer getTotalOrderCount() {
		return totalOrderCount;
	}

	public void setTotalOrderCount(Integer totalOrderCount) {
		this.totalOrderCount = totalOrderCount;
	}

	public BigDecimal getTotalOrderAmount() {
		return totalOrderAmount;
	}

	public void setTotalOrderAmount(BigDecimal totalOrderAmount) {
		this.totalOrderAmount = totalOrderAmount;
	}

	public Integer getCommissionOrderCount() {
		return commissionOrderCount;
	}

	public void setCommissionOrderCount(Integer commissionOrderCount) {
		this.commissionOrderCount = commissionOrderCount;
	}

	public BigDecimal getTotalCommissionAmount() {
		return totalCommissionAmount;
	}

	public void setTotalCommissionAmount(BigDecimal totalCommissionAmount) {
		this.totalCommissionAmount = totalCommissionAmount;
	}

	public Integer getMemberCount() {
		return memberCount;
	}

	public void setMemberCount(Integer memberCount) {
		this.memberCount = memberCount;
	}
}
