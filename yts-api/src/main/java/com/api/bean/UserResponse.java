package com.api.bean;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.constant.UserCatalog;
import com.common.util.DateUtil;
import com.domain.User;

/**
 * 用户表返回值对象
 *
 * @date 2025-07-24 23:22:31
 */
public class UserResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "用户统一标识(微信开放平台)")
	private String unionId;

	@Schema(description = "邀请码")
	private Long invitationCode;

	@Schema(description = "二维码")
	private String qrCode;

	@Schema(description = "用户类型(0:普通用户 1:常驻用户)")
	private String catalog;

	@Schema(description = "用户类型名称(0:普通用户 1:常驻用户)")
	private String catalogName;

	@Schema(description = "名字")
	private String name;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "头像")
	private String avatar;

	@Schema(description = "性别(0:男 1:女)")
	private String gender;

	@Schema(description = "最后登录时间")
	private String lastLoginTime;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	private String openId;

	private String token;

	@Schema(description = "累计分佣总金额")
	private BigDecimal sumCommission;

	@Schema(description = "累计分佣金额")
	private BigDecimal commission;

	@Schema(description = "累计分佣总订单")
	private Integer commissionOrderCount;

	@Schema(description = "未结算佣金")
	private BigDecimal unsettledCommission;

	@Schema(description = "未结算订单")
	private Integer unsettledOrderCount;

	@Schema(description = "已提现佣金")
	private BigDecimal transferCommission;

	@Schema(description = "上次提现时间")
	private String lastTransferTime;

	@Schema(description = "队长名称")
	private String captainUserName;

	@Schema(description = "团员人数")
	private Integer teamCount;

	@Schema(description = "订单数")
	private Integer orderCount;

	@Schema(description = "订单金额")
	private BigDecimal orderAmount;

	@Schema(description = "退款金额")
	private BigDecimal refundAmount;

	public UserResponse(){}

	public UserResponse(User user){
		this.id = user.getId();
		this.unionId = user.getUnionId();
		this.openId = user.getOpenId();
		this.invitationCode = user.getInvitationCode();
		this.qrCode = user.getQrCode();
		this.catalog = user.getCatalog();
		this.catalogName = UserCatalog.getName(user.getCatalog());
		this.name = user.getName();
		this.mobile = user.getMobile();
		this.avatar = user.getAvatar();
		this.gender = user.getGender();
		if (user.getLastLoginTime() != null){
			this.lastLoginTime = DateUtil.format(user.getLastLoginTime(), App.DATE_FORMAT);
		}
		this.status = user.getStatus();
		if (user.getModifyTime() != null){
			this.modifyTime = DateUtil.format(user.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (user.getCreateTime() != null){
			this.createTime = DateUtil.format(user.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getUnionId (){
		return unionId;
	}

	public void setUnionId (String unionId) {
		this.unionId = unionId;
	}

	public Long getInvitationCode (){
		return invitationCode;
	}

	public void setInvitationCode (Long invitationCode) {
		this.invitationCode = invitationCode;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getAvatar (){
		return avatar;
	}

	public void setAvatar (String avatar) {
		this.avatar = avatar;
	}

	public String getGender (){
		return gender;
	}

	public void setGender (String gender) {
		this.gender = gender;
	}

	public String getLastLoginTime (){
		return lastLoginTime;
	}

	public void setLastLoginTime (String lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public BigDecimal getSumCommission() {
		return sumCommission;
	}

	public void setSumCommission(BigDecimal sumCommission) {
		this.sumCommission = sumCommission;
	}

	public Integer getCommissionOrderCount() {
		return commissionOrderCount;
	}

	public void setCommissionOrderCount(Integer commissionOrderCount) {
		this.commissionOrderCount = commissionOrderCount;
	}

	public Integer getTeamCount() {
		return teamCount;
	}

	public void setTeamCount(Integer teamCount) {
		this.teamCount = teamCount;
	}

	public BigDecimal getUnsettledCommission() {
		return unsettledCommission;
	}

	public void setUnsettledCommission(BigDecimal unsettledCommission) {
		this.unsettledCommission = unsettledCommission;
	}

	public Integer getUnsettledOrderCount() {
		return unsettledOrderCount;
	}

	public void setUnsettledOrderCount(Integer unsettledOrderCount) {
		this.unsettledOrderCount = unsettledOrderCount;
	}

	public BigDecimal getTransferCommission() {
		return transferCommission;
	}

	public void setTransferCommission(BigDecimal transferCommission) {
		this.transferCommission = transferCommission;
	}

	public String getLastTransferTime() {
		return lastTransferTime;
	}

	public void setLastTransferTime(String lastTransferTime) {
		this.lastTransferTime = lastTransferTime;
	}

	public String getCaptainUserName() {
		return captainUserName;
	}

	public void setCaptainUserName(String captainUserName) {
		this.captainUserName = captainUserName;
	}

	public String getCatalogName() {
		return catalogName;
	}

	public void setCatalogName(String catalogName) {
		this.catalogName = catalogName;
	}

	public Integer getOrderCount() {
		return orderCount;
	}

	public void setOrderCount(Integer orderCount) {
		this.orderCount = orderCount;
	}

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	public BigDecimal getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public BigDecimal getCommission() {
		return commission;
	}

	public void setCommission(BigDecimal commission) {
		this.commission = commission;
	}

	public String getQrCode() {
		return qrCode;
	}

	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}
}
