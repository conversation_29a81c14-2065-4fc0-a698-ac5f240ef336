package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.ServiceInfo;

/**
 * 服务信息表返回值对象
 *
 * @date 2025-07-19 10:58:27
 */
public class ServiceInfoResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "服务名称")
	private String name;

	@Schema(description = "服务封面url")
	private String coverUrl;

	@Schema(description = "服务详情图片url")
	private String detailImgUrl;

	@Schema(description = "备注")
	private String comment;

	@Schema(description = "排序字段")
	private Integer sort;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public ServiceInfoResponse(){}

	public ServiceInfoResponse(ServiceInfo serviceInfo){
		this.id = serviceInfo.getId();
		this.name = serviceInfo.getName();
		this.coverUrl = serviceInfo.getCoverUrl();
		this.detailImgUrl = serviceInfo.getDetailImgUrl();
		this.comment = serviceInfo.getComment();
		this.sort = serviceInfo.getSort();
		this.status = serviceInfo.getStatus();
		if (serviceInfo.getModifyTime() != null){
			this.modifyTime = DateUtil.format(serviceInfo.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (serviceInfo.getCreateTime() != null){
			this.createTime = DateUtil.format(serviceInfo.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getCoverUrl (){
		return coverUrl;
	}

	public void setCoverUrl (String coverUrl) {
		this.coverUrl = coverUrl;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getDetailImgUrl() {
		return detailImgUrl;
	}

	public void setDetailImgUrl(String detailImgUrl) {
		this.detailImgUrl = detailImgUrl;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}
}
