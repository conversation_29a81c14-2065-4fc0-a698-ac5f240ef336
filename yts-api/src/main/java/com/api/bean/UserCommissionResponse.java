package com.api.bean;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.UserCommission;

/**
 * 用户佣金表返回值对象
 *
 * @date 2025-07-27 11:32:54
 */
public class UserCommissionResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "用户编号")
	private Long userId;

	@Schema(description = "可用余额")
	private BigDecimal amount;

	@Schema(description = "总余额")
	private BigDecimal totalAmount;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public UserCommissionResponse(){}

	public UserCommissionResponse(UserCommission userCommission){
		this.id = userCommission.getId();
		this.userId = userCommission.getUserId();
		this.amount = userCommission.getAmount();
		this.totalAmount = userCommission.getTotalAmount();
		this.status = userCommission.getStatus();
		if (userCommission.getModifyTime() != null){
			this.modifyTime = DateUtil.format(userCommission.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (userCommission.getCreateTime() != null){
			this.createTime = DateUtil.format(userCommission.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getTotalAmount (){
		return totalAmount;
	}

	public void setTotalAmount (BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
