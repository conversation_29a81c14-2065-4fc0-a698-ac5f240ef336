package com.api.bean;

import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 微信应用表请求对象
 *
 * @date 2025-07-16 21:29:32
 */
public class WechatAppRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "应用ID")
	private String appid;

	@Schema(description = "应用密钥")
	private String secret;

	@Schema(description = "应用名称")
	private String name;

	@Schema(description = "访问凭证")
	private String accessToken;

	@Schema(description = "凭证刷新日期")
	private String accessTokenDatetime;

	@Schema(description = "最小凭证刷新日期")
	private String minAccessTokenDatetime;

	@Schema(description = "最大凭证刷新日期")
	private String maxAccessTokenDatetime;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public String getAppid (){
		return appid;
	}

	public void setAppid (String appid) {
		this.appid = appid;
	}

	public String getSecret (){
		return secret;
	}

	public void setSecret (String secret) {
		this.secret = secret;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getAccessToken (){
		return accessToken;
	}

	public void setAccessToken (String accessToken) {
		this.accessToken = accessToken;
	}

	public String getAccessTokenDatetime (){
		return accessTokenDatetime;
	}

	public void setAccessTokenDatetime (String accessTokenDatetime) {
		this.accessTokenDatetime = accessTokenDatetime;
	}

	public String getMinAccessTokenDatetime (){
		return minAccessTokenDatetime;
	}

	public void setMinAccessTokenDatetime (String minAccessTokenDatetime) {
		this.minAccessTokenDatetime = minAccessTokenDatetime;
	}

	public String getMaxAccessTokenDatetime (){
		return maxAccessTokenDatetime;
	}

	public void setMaxAccessTokenDatetime (String maxAccessTokenDatetime) {
		this.maxAccessTokenDatetime = maxAccessTokenDatetime;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

}
