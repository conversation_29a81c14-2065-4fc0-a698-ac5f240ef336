package com.api.bean;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;

import com.common.bean.Bean;

/**
 * 用户佣金统计返回值对象
 *
 * @date 2025-07-31
 */
public class UserCommissionStatsResponse extends Bean {
    private static final long serialVersionUID = 1L;

    @Schema(description = "累计佣金")
    private BigDecimal totalCommission;

    @Schema(description = "分销订单数量")
    private Integer commissionOrderCount;

    @Schema(description = "团员人数")
    private Integer teamMemberCount;

    @Schema(description = "可提现佣金")
    private BigDecimal availableCommission;

    public UserCommissionStatsResponse() {
        this.totalCommission = BigDecimal.ZERO;
        this.commissionOrderCount = 0;
        this.teamMemberCount = 0;
        this.availableCommission = BigDecimal.ZERO;
    }

    public BigDecimal getTotalCommission() {
        return totalCommission;
    }

    public void setTotalCommission(BigDecimal totalCommission) {
        this.totalCommission = totalCommission;
    }

    public Integer getCommissionOrderCount() {
        return commissionOrderCount;
    }

    public void setCommissionOrderCount(Integer commissionOrderCount) {
        this.commissionOrderCount = commissionOrderCount;
    }

    public Integer getTeamMemberCount() {
        return teamMemberCount;
    }

    public void setTeamMemberCount(Integer teamMemberCount) {
        this.teamMemberCount = teamMemberCount;
    }

    public BigDecimal getAvailableCommission() {
        return availableCommission;
    }

    public void setAvailableCommission(BigDecimal availableCommission) {
        this.availableCommission = availableCommission;
    }
}
