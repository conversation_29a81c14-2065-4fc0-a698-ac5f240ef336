package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.UserCommissionCashFlow;

/**
 * 用户佣金提现明细表返回值对象
 *
 * @date 2025-07-19 10:58:27
 */
public class UserCommissionCashFlowResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "用户编号")
	private Long userId;

	@Schema(description = "订单编号")
	private Integer orderId;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public UserCommissionCashFlowResponse(){}

	public UserCommissionCashFlowResponse(UserCommissionCashFlow userCommissionCashFlow){
		this.id = userCommissionCashFlow.getId();
		this.userId = userCommissionCashFlow.getUserId();
		this.orderId = userCommissionCashFlow.getOrderId();
		this.status = userCommissionCashFlow.getStatus();
		if (userCommissionCashFlow.getModifyTime() != null){
			this.modifyTime = DateUtil.format(userCommissionCashFlow.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (userCommissionCashFlow.getCreateTime() != null){
			this.createTime = DateUtil.format(userCommissionCashFlow.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public Integer getOrderId (){
		return orderId;
	}

	public void setOrderId (Integer orderId) {
		this.orderId = orderId;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
