package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.UserAddress;

/**
 * 用户地址表返回值对象
 *
 * @date 2025-07-23 22:58:35
 */
public class UserAddressResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "用户编号")
	private Long userId;

	@Schema(description = "省份编号")
	private Long provinceId;

	@Schema(description = "省份名称")
	private String provinceName;

	@Schema(description = "城市编号")
	private Long cityId;

	@Schema(description = "城市名称")
	private String cityName;

	@Schema(description = "区县编号")
	private Long districtId;

	@Schema(description = "区县名称")
	private String districtName;

	@Schema(description = "详细地址")
	private String detailAddress;

	@Schema(description = "姓名")
	private String name;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "是否默认地址(0:是 1:否)")
	private String defaultAddress;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public UserAddressResponse(){}

	public UserAddressResponse(UserAddress userAddress){
		this.id = userAddress.getId();
		this.userId = userAddress.getUserId();
		this.provinceId = userAddress.getProvinceId();
		this.cityId = userAddress.getCityId();
		this.districtId = userAddress.getDistrictId();
		this.detailAddress = userAddress.getDetailAddress();
		this.name = userAddress.getName();
		this.mobile = userAddress.getMobile();
		this.defaultAddress = userAddress.getDefaultAddress();
		this.status = userAddress.getStatus();
		if (userAddress.getModifyTime() != null){
			this.modifyTime = DateUtil.format(userAddress.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (userAddress.getCreateTime() != null){
			this.createTime = DateUtil.format(userAddress.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public Long getProvinceId (){
		return provinceId;
	}

	public void setProvinceId (Long provinceId) {
		this.provinceId = provinceId;
	}

	public Long getCityId (){
		return cityId;
	}

	public void setCityId (Long cityId) {
		this.cityId = cityId;
	}

	public Long getDistrictId (){
		return districtId;
	}

	public void setDistrictId (Long districtId) {
		this.districtId = districtId;
	}

	public String getDetailAddress (){
		return detailAddress;
	}

	public void setDetailAddress (String detailAddress) {
		this.detailAddress = detailAddress;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getDefaultAddress() {
		return defaultAddress;
	}

	public void setDefaultAddress(String defaultAddress) {
		this.defaultAddress = defaultAddress;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getDistrictName() {
		return districtName;
	}

	public void setDistrictName(String districtName) {
		this.districtName = districtName;
	}
}
