package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.ProductBrowse;

/**
 * 产品浏览表返回值对象
 *
 * @date 2025-07-20 16:36:02
 */
public class ProductBrowseResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "类型(0:课程 1:商品)")
	private String productCatalog;

	@Schema(description = "产品编号")
	private Long productId;

	@Schema(description = "用户编号")
	private Long userId;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public ProductBrowseResponse(){}

	public ProductBrowseResponse(ProductBrowse productBrowse){
		this.id = productBrowse.getId();
		this.productCatalog = productBrowse.getProductCatalog();
		this.productId = productBrowse.getProductId();
		this.userId = productBrowse.getUserId();
		this.status = productBrowse.getStatus();
		if (productBrowse.getModifyTime() != null){
			this.modifyTime = DateUtil.format(productBrowse.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (productBrowse.getCreateTime() != null){
			this.createTime = DateUtil.format(productBrowse.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getProductCatalog (){
		return productCatalog;
	}

	public void setProductCatalog (String productCatalog) {
		this.productCatalog = productCatalog;
	}

	public Long getProductId (){
		return productId;
	}

	public void setProductId (Long productId) {
		this.productId = productId;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
