package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 字典表
 *
 * @date 2023-01-03 14:47:37
 */
public class DictionaryRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "数据编号")
	private Integer id;

	@Schema(description = "代码")
	private String code;

	@Schema(description = "键")
	private String key;

	@Schema(description = "值")
	private String value;

	@Schema(description = "顺序")
	private Integer order;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "sql语句")
	private String sql;

	@Schema(description = "临时存储目录类型")
	private String temporaryStorageCatalog;

	@Schema(description = "菜单名称")
	private String menuName;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public Integer getOrder() {
		return order;
	}

	public void setOrder(Integer order) {
		this.order = order;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getSql() {
		return sql;
	}

	public void setSql(String sql) {
		this.sql = sql;
	}

	public String getTemporaryStorageCatalog() {
		return temporaryStorageCatalog;
	}

	public void setTemporaryStorageCatalog(String temporaryStorageCatalog) {
		this.temporaryStorageCatalog = temporaryStorageCatalog;
	}

	public String getMenuName() {
		return menuName;
	}

	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}
}
