package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.GoodsSpecificationValue;

/**
 * 商品规格值表返回值对象
 *
 * @date 2025-07-27 21:26:17
 */
public class GoodsSpecificationValueResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "商品id")
	private Long goodsId;

	@Schema(description = "规格类型ID")
	private Long goodsSpecificationTypeId;

	@Schema(description = "规格类型名称")
	private String goodsSpecificationTypeName;

	@Schema(description = "规格值")
	private String name;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public GoodsSpecificationValueResponse(){}

	public GoodsSpecificationValueResponse(GoodsSpecificationValue goodsSpecificationValue){
		this.id = goodsSpecificationValue.getId();
		this.goodsId = goodsSpecificationValue.getGoodsId();
		this.goodsSpecificationTypeId = goodsSpecificationValue.getGoodsSpecificationTypeId();
		this.name = goodsSpecificationValue.getName();
		this.status = goodsSpecificationValue.getStatus();
		if (goodsSpecificationValue.getModifyTime() != null){
			this.modifyTime = DateUtil.format(goodsSpecificationValue.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (goodsSpecificationValue.getCreateTime() != null){
			this.createTime = DateUtil.format(goodsSpecificationValue.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public Long getGoodsId (){
		return goodsId;
	}

	public void setGoodsId (Long goodsId) {
		this.goodsId = goodsId;
	}

	public Long getGoodsSpecificationTypeId (){
		return goodsSpecificationTypeId;
	}

	public void setGoodsSpecificationTypeId (Long goodsSpecificationTypeId) {
		this.goodsSpecificationTypeId = goodsSpecificationTypeId;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getGoodsSpecificationTypeName() {
		return goodsSpecificationTypeName;
	}

	public void setGoodsSpecificationTypeName(String goodsSpecificationTypeName) {
		this.goodsSpecificationTypeName = goodsSpecificationTypeName;
	}
}
