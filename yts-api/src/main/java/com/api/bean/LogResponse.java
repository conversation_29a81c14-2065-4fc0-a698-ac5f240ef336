package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.Log;

/**
 * 系统用户日志表返回值对象
 *
 * @date 2025-07-20 18:59:08
 */
public class LogResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "平台")
	private String platform;

	@Schema(description = "版本")
	private String version;

	@Schema(description = "用户类型")
	private String type;

	@Schema(description = "用户编号")
	private Long userId;

	@Schema(description = "IP")
	private String ip;

	@Schema(description = "地址")
	private String url;

	@Schema(description = "请求编号")
	private String requestId;

	@Schema(description = "开始时间")
	private String startTime;

	@Schema(description = "结束时间")
	private String endTime;

	@Schema(description = "执行时长")
	private Integer duration;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public LogResponse(){}

	public LogResponse(Log log){
		this.id = log.getId();
		this.platform = log.getPlatform();
		this.version = log.getVersion();
		this.type = log.getType();
		this.userId = log.getUserId();
		this.ip = log.getIp();
		this.url = log.getUrl();
		this.requestId = log.getRequestId();
		if (log.getStartTime() != null){
			this.startTime = DateUtil.format(log.getStartTime(), App.DATETIME_FORMAT);
		}
		if (log.getEndTime() != null){
			this.endTime = DateUtil.format(log.getEndTime(), App.DATETIME_FORMAT);
		}
		this.duration = log.getDuration();
		this.status = log.getStatus();
		if (log.getModifyTime() != null){
			this.modifyTime = DateUtil.format(log.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (log.getCreateTime() != null){
			this.createTime = DateUtil.format(log.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getPlatform (){
		return platform;
	}

	public void setPlatform (String platform) {
		this.platform = platform;
	}

	public String getVersion (){
		return version;
	}

	public void setVersion (String version) {
		this.version = version;
	}

	public String getType (){
		return type;
	}

	public void setType (String type) {
		this.type = type;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public String getIp (){
		return ip;
	}

	public void setIp (String ip) {
		this.ip = ip;
	}

	public String getUrl (){
		return url;
	}

	public void setUrl (String url) {
		this.url = url;
	}

	public String getRequestId (){
		return requestId;
	}

	public void setRequestId (String requestId) {
		this.requestId = requestId;
	}

	public String getStartTime (){
		return startTime;
	}

	public void setStartTime (String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime (){
		return endTime;
	}

	public void setEndTime (String endTime) {
		this.endTime = endTime;
	}

	public Integer getDuration (){
		return duration;
	}

	public void setDuration (Integer duration) {
		this.duration = duration;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
