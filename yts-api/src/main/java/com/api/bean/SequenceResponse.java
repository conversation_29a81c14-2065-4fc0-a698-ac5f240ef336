package com.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.Sequence;

/**
 * 序列表返回值对象
 *
 * @date 2025-07-16 21:29:32
 */
public class SequenceResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public SequenceResponse(){}

	public SequenceResponse(Sequence sequence){
		this.id = sequence.getId();
		this.status = sequence.getStatus();
		if (sequence.getModifyTime() != null){
			this.modifyTime = DateUtil.format(sequence.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (sequence.getCreateTime() != null){
			this.createTime = DateUtil.format(sequence.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
