package com.api.bean;

import java.math.BigDecimal;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 商品规格sku表请求对象
 *
 * @date 2025-07-27 21:26:17
 */
public class GoodsSpecificationSkuRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "商品id")
	private Long goodsId;

	@Schema(description = "商品id集合")
	private List<Long> goodsIds;

	@Schema(description = "SKU属性")
	private String specValues;

	@Schema(description = "商品封面")
	private String imgUrl;

	@Schema(description = "成本价")
	private BigDecimal buyingPrice;

	@Schema(description = "划线价")
	private BigDecimal originalPrice;

	@Schema(description = "售价")
	private BigDecimal price;

	@Schema(description = "毛利(售价-成本价)")
	private BigDecimal grossProfit;

	@Schema(description = "库存数量")
	private Integer stock;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public Long getGoodsId (){
		return goodsId;
	}

	public void setGoodsId (Long goodsId) {
		this.goodsId = goodsId;
	}

	public List<Long> getGoodsIds (){
		return goodsIds;
	}

	public void setGoodsIds (List<Long> goodsIds) {
		this.goodsIds = goodsIds;
	}

	public String getSpecValues (){
		return specValues;
	}

	public void setSpecValues (String specValues) {
		this.specValues = specValues;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public BigDecimal getBuyingPrice (){
		return buyingPrice;
	}

	public void setBuyingPrice (BigDecimal buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public BigDecimal getOriginalPrice (){
		return originalPrice;
	}

	public void setOriginalPrice (BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice (){
		return price;
	}

	public void setPrice (BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getGrossProfit (){
		return grossProfit;
	}

	public void setGrossProfit (BigDecimal grossProfit) {
		this.grossProfit = grossProfit;
	}

	public Integer getStock (){
		return stock;
	}

	public void setStock (Integer stock) {
		this.stock = stock;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

}
