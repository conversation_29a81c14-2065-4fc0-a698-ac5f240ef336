package com.api.bean;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.UserCommissionFlow;

/**
 * 用户佣金明细表返回值对象
 *
 * @date 2025-07-27 11:32:54
 */
public class UserCommissionFlowResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "流水号")
	private String flowCode;

	@Schema(description = "用户编号")
	private Long userId;

	@Schema(description = "订单ID")
	private Long orderId;

	@Schema(description = "订单号")
	private String orderCode;

	@Schema(description = "预支付交易会话标识")
	private String prepayId;

	@Schema(description = "交易时间")
	private String transactionTime;

	@Schema(description = "流转金额")
	private BigDecimal flowAmount;

	@Schema(description = "总金额")
	private BigDecimal amount;

	@Schema(description = "分类(1000:分佣流水)")
	private String catalog;

	@Schema(description = "支付方式(1000:微信小程序支付)")
	private String payChannel;

	@Schema(description = "收支出(0:收入 1:支出)")
	private String income;

	@Schema(description = "标签")
	private String label;

	@Schema(description = "备注")
	private String comment;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "订单信息")
	private OrderResponse orderResponse;

	@Schema(description = "分类名称")
	private String catalogName;

	@Schema(description = "收支类型名称")
	private String incomeName;

	@Schema(description = "支付方式名称")
	private String payChannelName;

	@Schema(description = "提现状态")
	private String cashStatus;

	@Schema(description = "提现方式")
	private String cashMethod;

	@Schema(description = "总佣金")
	private BigDecimal sumAmount;

	@Schema(description = "总订单数量")
	private Integer sumOrderCount;

	public UserCommissionFlowResponse(){}

	public UserCommissionFlowResponse(UserCommissionFlow userCommissionFlow){
		this.id = userCommissionFlow.getId();
		this.flowCode = userCommissionFlow.getFlowCode();
		this.userId = userCommissionFlow.getUserId();
		this.orderId = userCommissionFlow.getOrderId();
		this.orderCode = userCommissionFlow.getOrderCode();
		this.prepayId = userCommissionFlow.getPrepayId();
		if (userCommissionFlow.getTransactionTime() != null){
			this.transactionTime = DateUtil.format(userCommissionFlow.getTransactionTime(), App.DATETIME_FORMAT);
		}
		this.flowAmount = userCommissionFlow.getFlowAmount();
		this.amount = userCommissionFlow.getAmount();
		this.catalog = userCommissionFlow.getCatalog();
		this.payChannel = userCommissionFlow.getPayChannel();
		this.income = userCommissionFlow.getIncome();
		this.label = userCommissionFlow.getLabel();
		this.comment = userCommissionFlow.getComment();
		this.status = userCommissionFlow.getStatus();
		if (userCommissionFlow.getModifyTime() != null){
			this.modifyTime = DateUtil.format(userCommissionFlow.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (userCommissionFlow.getCreateTime() != null){
			this.createTime = DateUtil.format(userCommissionFlow.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getFlowCode (){
		return flowCode;
	}

	public void setFlowCode (String flowCode) {
		this.flowCode = flowCode;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public Long getOrderId (){
		return orderId;
	}

	public void setOrderId (Long orderId) {
		this.orderId = orderId;
	}

	public String getOrderCode (){
		return orderCode;
	}

	public void setOrderCode (String orderCode) {
		this.orderCode = orderCode;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public String getTransactionTime (){
		return transactionTime;
	}

	public void setTransactionTime (String transactionTime) {
		this.transactionTime = transactionTime;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public String getPayChannel (){
		return payChannel;
	}

	public void setPayChannel (String payChannel) {
		this.payChannel = payChannel;
	}

	public String getIncome (){
		return income;
	}

	public void setIncome (String income) {
		this.income = income;
	}


	public String getLabel (){
		return label;
	}

	public void setLabel (String label) {
		this.label = label;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public BigDecimal getFlowAmount() {
		return flowAmount;
	}

	public void setFlowAmount(BigDecimal flowAmount) {
		this.flowAmount = flowAmount;
	}

	public String getCatalogName() {
		return catalogName;
	}

	public void setCatalogName(String catalogName) {
		this.catalogName = catalogName;
	}

	public String getIncomeName() {
		return incomeName;
	}

	public void setIncomeName(String incomeName) {
		this.incomeName = incomeName;
	}

	public String getPayChannelName() {
		return payChannelName;
	}

	public void setPayChannelName(String payChannelName) {
		this.payChannelName = payChannelName;
	}

	public String getCashStatus() {
		return cashStatus;
	}

	public void setCashStatus(String cashStatus) {
		this.cashStatus = cashStatus;
	}

	public String getCashMethod() {
		return cashMethod;
	}

	public void setCashMethod(String cashMethod) {
		this.cashMethod = cashMethod;
	}

	public OrderResponse getOrderResponse() {
		return orderResponse;
	}

	public void setOrderResponse(OrderResponse orderResponse) {
		this.orderResponse = orderResponse;
	}

	public BigDecimal getSumAmount() {
		return sumAmount;
	}

	public void setSumAmount(BigDecimal sumAmount) {
		this.sumAmount = sumAmount;
	}

	public Integer getSumOrderCount() {
		return sumOrderCount;
	}

	public void setSumOrderCount(Integer sumOrderCount) {
		this.sumOrderCount = sumOrderCount;
	}
}
