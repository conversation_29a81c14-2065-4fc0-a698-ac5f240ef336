package com.api.bean;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.GoodsSpecificationSku;

/**
 * 商品规格sku表返回值对象
 *
 * @date 2025-07-27 21:26:17
 */
public class GoodsSpecificationSkuResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "商品id")
	private Long goodsId;

	@Schema(description = "SKU属性")
	private String specValues;

	@Schema(description = "商品封面")
	private String imgUrl;

	@Schema(description = "成本价")
	private BigDecimal buyingPrice;

	@Schema(description = "划线价")
	private BigDecimal originalPrice;

	@Schema(description = "售价")
	private BigDecimal price;

	@Schema(description = "毛利(售价-成本价)")
	private BigDecimal grossProfit;

	@Schema(description = "库存数量")
	private Integer stock;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	public GoodsSpecificationSkuResponse(){}

	public GoodsSpecificationSkuResponse(GoodsSpecificationSku goodsSpecificationSku){
		this.id = goodsSpecificationSku.getId();
		this.goodsId = goodsSpecificationSku.getGoodsId();
		this.specValues = goodsSpecificationSku.getSpecValues();
		this.imgUrl = goodsSpecificationSku.getImgUrl();
		this.buyingPrice = goodsSpecificationSku.getBuyingPrice();
		this.originalPrice = goodsSpecificationSku.getOriginalPrice();
		this.price = goodsSpecificationSku.getPrice();
		this.grossProfit = goodsSpecificationSku.getGrossProfit();
		this.stock = goodsSpecificationSku.getStock();
		this.status = goodsSpecificationSku.getStatus();
		if (goodsSpecificationSku.getModifyTime() != null){
			this.modifyTime = DateUtil.format(goodsSpecificationSku.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (goodsSpecificationSku.getCreateTime() != null){
			this.createTime = DateUtil.format(goodsSpecificationSku.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public Long getGoodsId (){
		return goodsId;
	}

	public void setGoodsId (Long goodsId) {
		this.goodsId = goodsId;
	}

	public String getSpecValues (){
		return specValues;
	}

	public void setSpecValues (String specValues) {
		this.specValues = specValues;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public BigDecimal getBuyingPrice (){
		return buyingPrice;
	}

	public void setBuyingPrice (BigDecimal buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public BigDecimal getOriginalPrice (){
		return originalPrice;
	}

	public void setOriginalPrice (BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice (){
		return price;
	}

	public void setPrice (BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getGrossProfit (){
		return grossProfit;
	}

	public void setGrossProfit (BigDecimal grossProfit) {
		this.grossProfit = grossProfit;
	}

	public Integer getStock (){
		return stock;
	}

	public void setStock (Integer stock) {
		this.stock = stock;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
