package com.api.bean;

import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户地址表请求对象
 *
 * @date 2025-07-23 22:58:35
 */
public class UserAddressRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "主键集合")
	private List<Long> ids;

	@Schema(description = "用户编号")
	private Long userId;

	@Schema(description = "用户编号集合")
	private List<Long> userIds;

	@Schema(description = "省份编号")
	private Long provinceId;

	@Schema(description = "省份编号集合")
	private List<Long> provinceIds;

	@Schema(description = "城市编号")
	private Long cityId;

	@Schema(description = "城市编号集合")
	private List<Long> cityIds;

	@Schema(description = "区县编号")
	private Long districtId;

	@Schema(description = "区县编号集合")
	private List<Long> districtIds;

	@Schema(description = "详细地址")
	private String detailAddress;

	@Schema(description = "姓名")
	private String name;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "是否默认地址(0:是 1:否)")
	private String defaultAddress;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "最小修改时间")
	private String minModifyTime;

	@Schema(description = "最大修改时间")
	private String maxModifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "最小创建时间")
	private String minCreateTime;

	@Schema(description = "最大创建时间")
	private String maxCreateTime;


	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public List<Long> getIds (){
		return ids;
	}

	public void setIds (List<Long> ids) {
		this.ids = ids;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public List<Long> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Long> userIds) {
		this.userIds = userIds;
	}

	public Long getProvinceId (){
		return provinceId;
	}

	public void setProvinceId (Long provinceId) {
		this.provinceId = provinceId;
	}

	public List<Long> getProvinceIds (){
		return provinceIds;
	}

	public void setProvinceIds (List<Long> provinceIds) {
		this.provinceIds = provinceIds;
	}

	public Long getCityId (){
		return cityId;
	}

	public void setCityId (Long cityId) {
		this.cityId = cityId;
	}

	public List<Long> getCityIds (){
		return cityIds;
	}

	public void setCityIds (List<Long> cityIds) {
		this.cityIds = cityIds;
	}

	public Long getDistrictId (){
		return districtId;
	}

	public void setDistrictId (Long districtId) {
		this.districtId = districtId;
	}

	public List<Long> getDistrictIds (){
		return districtIds;
	}

	public void setDistrictIds (List<Long> districtIds) {
		this.districtIds = districtIds;
	}

	public String getDetailAddress (){
		return detailAddress;
	}

	public void setDetailAddress (String detailAddress) {
		this.detailAddress = detailAddress;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getDefaultAddress() {
		return defaultAddress;
	}

	public void setDefaultAddress(String defaultAddress) {
		this.defaultAddress = defaultAddress;
	}
}
