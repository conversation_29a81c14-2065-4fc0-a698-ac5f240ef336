package com.api.bean;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.OrderProduct;

/**
 * 订单产品表返回值对象
 *
 * @date 2025-07-24 23:22:31
 */
public class OrderProductResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "订单编号")
	private Long orderId;

	@Schema(description = "类型(0:课程 1:商品)")
	private String productCatalog;

	@Schema(description = "产品编号")
	private Long productId;

	@Schema(description = "数量")
	private Integer number;

	@Schema(description = "实付金额")
	private BigDecimal amount;

	@Schema(description = "产品名称")
	private String productName;

	@Schema(description = "SKUid")
	private Long skuId;

	@Schema(description = "规格值")
	private String specValues;

	@Schema(description = "图片")
	private String imgUrl;

	@Schema(description = "价格")
	private BigDecimal price;

	@Schema(description = "原价")
	private BigDecimal originalPrice;

	@Schema(description = "采购价")
	private BigDecimal buyingPrice;

	@Schema(description = "毛利润")
	private BigDecimal grossProfit;

	@Schema(description = "团长佣金比例(%)")
	private BigDecimal commissionRatio;

	@Schema(description = "团长佣金")
	private BigDecimal commission;

	@Schema(description = "销售老师佣金比例(%)")
	private BigDecimal salesTeacherCommissionRatio;

	@Schema(description = "销售老师佣金")
	private BigDecimal salesTeacherCommission;

	@Schema(description = "单位")
	private String unit;

	@Schema(description = "商品类型")
	private String goodsCatalog;

	@Schema(description = "商品分类编号")
	private Long goodsTypeId;

	@Schema(description = "商品分类名称")
	private String goodsTypeName;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "code")
	private String code;

	public OrderProductResponse(){}

	public OrderProductResponse(OrderProduct orderProduct){
		this.id = orderProduct.getId();
		this.orderId = orderProduct.getOrderId();
		this.productCatalog = orderProduct.getProductCatalog();
		this.productId = orderProduct.getProductId();
		this.number = orderProduct.getNumber();
		this.amount = orderProduct.getAmount();
		this.productName = orderProduct.getProductName();
		this.skuId = orderProduct.getSkuId();
		this.specValues = orderProduct.getSpecValues();
		this.imgUrl = orderProduct.getImgUrl();
		this.price = orderProduct.getPrice();
		this.originalPrice = orderProduct.getOriginalPrice();
		this.buyingPrice = orderProduct.getBuyingPrice();
		this.grossProfit = orderProduct.getGrossProfit();
		this.commissionRatio = orderProduct.getCommissionRatio();
		this.commission = orderProduct.getCommission();
		this.salesTeacherCommissionRatio = orderProduct.getSalesTeacherCommissionRatio();
		this.salesTeacherCommission = orderProduct.getSalesTeacherCommission();
		this.unit = orderProduct.getUnit();
		this.goodsCatalog = orderProduct.getGoodsCatalog();
		this.goodsTypeId = orderProduct.getGoodsTypeId();
		this.goodsTypeName = orderProduct.getGoodsTypeName();
		this.status = orderProduct.getStatus();
		if (orderProduct.getModifyTime() != null){
			this.modifyTime = DateUtil.format(orderProduct.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (orderProduct.getCreateTime() != null){
			this.createTime = DateUtil.format(orderProduct.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public Long getOrderId (){
		return orderId;
	}

	public void setOrderId (Long orderId) {
		this.orderId = orderId;
	}

	public String getProductCatalog (){
		return productCatalog;
	}

	public void setProductCatalog (String productCatalog) {
		this.productCatalog = productCatalog;
	}

	public Long getProductId (){
		return productId;
	}

	public void setProductId (Long productId) {
		this.productId = productId;
	}

	public Integer getNumber (){
		return number;
	}

	public void setNumber (Integer number) {
		this.number = number;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getCommissionRatio (){
		return commissionRatio;
	}

	public void setCommissionRatio (BigDecimal commissionRatio) {
		this.commissionRatio = commissionRatio;
	}

	public BigDecimal getCommission (){
		return commission;
	}

	public void setCommission (BigDecimal commission) {
		this.commission = commission;
	}

	public BigDecimal getSalesTeacherCommissionRatio (){
		return salesTeacherCommissionRatio;
	}

	public void setSalesTeacherCommissionRatio (BigDecimal salesTeacherCommissionRatio) {
		this.salesTeacherCommissionRatio = salesTeacherCommissionRatio;
	}

	public BigDecimal getSalesTeacherCommission (){
		return salesTeacherCommission;
	}

	public void setSalesTeacherCommission (BigDecimal salesTeacherCommission) {
		this.salesTeacherCommission = salesTeacherCommission;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSpecValues() {
		return specValues;
	}

	public void setSpecValues(String specValues) {
		this.specValues = specValues;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getBuyingPrice() {
		return buyingPrice;
	}

	public void setBuyingPrice(BigDecimal buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public BigDecimal getGrossProfit() {
		return grossProfit;
	}

	public void setGrossProfit(BigDecimal grossProfit) {
		this.grossProfit = grossProfit;
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getGoodsCatalog() {
		return goodsCatalog;
	}

	public void setGoodsCatalog(String goodsCatalog) {
		this.goodsCatalog = goodsCatalog;
	}

	public Long getGoodsTypeId() {
		return goodsTypeId;
	}

	public void setGoodsTypeId(Long goodsTypeId) {
		this.goodsTypeId = goodsTypeId;
	}

	public String getGoodsTypeName() {
		return goodsTypeName;
	}

	public void setGoodsTypeName(String goodsTypeName) {
		this.goodsTypeName = goodsTypeName;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
}
