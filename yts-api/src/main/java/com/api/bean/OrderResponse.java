package com.api.bean;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.constant.OrderCatalog;
import com.common.constant.OrderDeliveryCatalog;
import com.common.constant.OrderOperateStage;
import com.common.constant.OrderSelectCatalog;
import com.common.constant.OrderStage;
import com.common.constant.UserCatalog;
import com.common.util.DateUtil;
import com.domain.Order;

/**
 * 订单表返回值对象
 *
 * @date 2025-07-24 23:22:31
 */
public class OrderResponse extends Bean {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键")
	private Long id;

	@Schema(description = "订单号")
	private String code;

	@Schema(description = "预支付交易会话标识")
	private String prepayId;

	@Schema(description = "用户id")
	private Long userId;

	@Schema(description = "用户名称")
	private String userName;

	@Schema(description = "手机号")
	private String mobile;

	@Schema(description = "用户类型")
	private String userCatalog;

	@Schema(description = "用户类型名称")
	private String userCatalogName;

	@Schema(description = "订单类型(0:课程 1:商品)")
	private String orderCatalog;

	@Schema(description = "订单类型名称(0:课程 1:商品)")
	private String orderCatalogName;

	@Schema(description = "选择类型(0:自选 1:帮选)")
	private String selectCatalog;

	@Schema(description = "选择类型名称(0:自选 1:帮选)")
	private String selectCatalogName;

	@Schema(description = "实付金额")
	private BigDecimal amount;

	@Schema(description = "退款金额")
	private BigDecimal refundAmount;

	@Schema(description = "团长总佣金")
	private BigDecimal sumCommission;

	@Schema(description = "团长佣金人")
	private Long commissionUserId;

	@Schema(description = "团长佣金人名称")
	private String commissionUserName;

	@Schema(description = "销售老师佣金金额")
	private BigDecimal sumSalesTeacherCommission;

	@Schema(description = "销售老师ID")
	private Long salesTeacherId;

	@Schema(description = "销售老师名称")
	private String salesTeacherName;

	@Schema(description = "支付状态")
	private String stage;

	@Schema(description = "提货状态(0:已提货 1:未提货)")
	private String operateStage;

	@Schema(description = "提货状态名称(0:已提货 1:未提货)")
	private String operateStageName;

	@Schema(description = "配送方式(0:自提 1:派送)")
	private String deliveryCatalog;

	@Schema(description = "配送方式名称(0:自提 1:派送)")
	private String deliveryCatalogName;

	@Schema(description = "预约时间")
	private String reservationTime;

	@Schema(description = "用户地址编号")
	private Long userAddressId;

	@Schema(description = "地址-省份编号")
	private Long addressProvinceId;

	@Schema(description = "地址-城市编号")
	private Long addressCityId;

	@Schema(description = "地址-区县编号")
	private Long addressDistrictId;

	@Schema(description = "地址-详细地址")
	private String addressDetailAddress;

	@Schema(description = "地址-姓名")
	private String addressName;

	@Schema(description = "地址-手机号")
	private String addressMobile;

	@Schema(description = "支付时间")
	private String payTime;

	@Schema(description = "备注")
	private String comment;

	@Schema(description = "状态(0:正常 1:无效)")
	private String status;

	@Schema(description = "修改时间")
	private String modifyTime;

	@Schema(description = "创建时间")
	private String createTime;

	@Schema(description = "商品信息名称集合")
	private List<String> productNames;

	@Schema(description = "支付状态名称")
	private String stageName;

	@Schema(description = "商品总数量")
	private Integer totalProductCount;

	@Schema(description = "订单商品对象集合")
	private List<OrderProductResponse> orderProducts;

	@Schema(description = "订单统计-总金额")
	private BigDecimal totalAmount;

	@Schema(description = "订单统计-总佣金金额")
	private BigDecimal totalCommission;

	@Schema(description = "订单统计-总未支付金额")
	private BigDecimal totalNotPayAmount;

	@Schema(description = "订单统计-总退款金额")
	private BigDecimal totalRefundAmount;


	public OrderResponse(){}

	public OrderResponse(Order order){
		this.id = order.getId();
		this.code = order.getCode();
		this.prepayId = order.getPrepayId();
		this.userId = order.getUserId();
		this.userCatalog = order.getUserCatalog();
		this.userCatalogName = UserCatalog.getName(order.getUserCatalog());
		this.orderCatalog = order.getOrderCatalog();
		this.orderCatalogName = OrderCatalog.getName(order.getOrderCatalog());
		this.selectCatalog = order.getSelectCatalog();
		this.selectCatalogName = OrderSelectCatalog.getName(order.getSelectCatalog());
		this.amount = order.getAmount();
		this.refundAmount = order.getRefundAmount();
		this.sumCommission = order.getSumCommission();
		this.commissionUserId = order.getCommissionUserId();
		this.sumSalesTeacherCommission = order.getSumSalesTeacherCommission();
		this.salesTeacherId = order.getSalesTeacherId();
		this.stage = order.getStage();
		this.stageName = OrderStage.getName(order.getStage());
		this.operateStage = order.getOperateStage();
		this.operateStageName = OrderOperateStage.getName(order.getOperateStage());
		this.deliveryCatalog = order.getDeliveryCatalog();
		this.deliveryCatalogName = OrderDeliveryCatalog.getName(order.getDeliveryCatalog());
		if (order.getReservationTime() != null){
			this.reservationTime = DateUtil.format(order.getReservationTime(), App.DATETIME_FORMAT);
		}
		this.userAddressId = order.getUserAddressId();
		this.addressProvinceId = order.getAddressProvinceId();
		this.addressCityId = order.getAddressCityId();
		this.addressDistrictId = order.getAddressDistrictId();
		this.addressDetailAddress = order.getAddressDetailAddress();
		this.addressName = order.getAddressName();
		this.addressMobile = order.getAddressMobile();
		if (order.getPayTime() != null){
			this.payTime = DateUtil.format(order.getPayTime(), App.DATETIME_FORMAT);
		}
		this.comment = order.getComment();
		this.status = order.getStatus();
		if (order.getModifyTime() != null){
			this.modifyTime = DateUtil.format(order.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (order.getCreateTime() != null){
			this.createTime = DateUtil.format(order.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Long getId (){
		return id;
	}

	public void setId (Long id) {
		this.id = id;
	}

	public String getCode (){
		return code;
	}

	public void setCode (String code) {
		this.code = code;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public String getUserCatalog (){
		return userCatalog;
	}

	public void setUserCatalog (String userCatalog) {
		this.userCatalog = userCatalog;
	}

	public String getOrderCatalog (){
		return orderCatalog;
	}

	public void setOrderCatalog (String orderCatalog) {
		this.orderCatalog = orderCatalog;
	}

	public String getSelectCatalog (){
		return selectCatalog;
	}

	public void setSelectCatalog (String selectCatalog) {
		this.selectCatalog = selectCatalog;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getRefundAmount (){
		return refundAmount;
	}

	public void setRefundAmount (BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public BigDecimal getSumCommission (){
		return sumCommission;
	}

	public void setSumCommission (BigDecimal sumCommission) {
		this.sumCommission = sumCommission;
	}

	public Long getCommissionUserId (){
		return commissionUserId;
	}

	public void setCommissionUserId (Long commissionUserId) {
		this.commissionUserId = commissionUserId;
	}

	public BigDecimal getSumSalesTeacherCommission (){
		return sumSalesTeacherCommission;
	}

	public void setSumSalesTeacherCommission (BigDecimal sumSalesTeacherCommission) {
		this.sumSalesTeacherCommission = sumSalesTeacherCommission;
	}

	public Long getSalesTeacherId (){
		return salesTeacherId;
	}

	public void setSalesTeacherId (Long salesTeacherId) {
		this.salesTeacherId = salesTeacherId;
	}


	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public String getOperateStage (){
		return operateStage;
	}

	public void setOperateStage (String operateStage) {
		this.operateStage = operateStage;
	}

	public String getDeliveryCatalog (){
		return deliveryCatalog;
	}

	public void setDeliveryCatalog (String deliveryCatalog) {
		this.deliveryCatalog = deliveryCatalog;
	}

	public String getReservationTime (){
		return reservationTime;
	}

	public void setReservationTime (String reservationTime) {
		this.reservationTime = reservationTime;
	}

	public Long getUserAddressId (){
		return userAddressId;
	}

	public void setUserAddressId (Long userAddressId) {
		this.userAddressId = userAddressId;
	}

	public String getPayTime (){
		return payTime;
	}

	public void setPayTime (String payTime) {
		this.payTime = payTime;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public Long getAddressProvinceId() {
		return addressProvinceId;
	}

	public void setAddressProvinceId(Long addressProvinceId) {
		this.addressProvinceId = addressProvinceId;
	}

	public Long getAddressCityId() {
		return addressCityId;
	}

	public void setAddressCityId(Long addressCityId) {
		this.addressCityId = addressCityId;
	}

	public Long getAddressDistrictId() {
		return addressDistrictId;
	}

	public void setAddressDistrictId(Long addressDistrictId) {
		this.addressDistrictId = addressDistrictId;
	}

	public String getAddressDetailAddress() {
		return addressDetailAddress;
	}

	public void setAddressDetailAddress(String addressDetailAddress) {
		this.addressDetailAddress = addressDetailAddress;
	}

	public String getAddressName() {
		return addressName;
	}

	public void setAddressName(String addressName) {
		this.addressName = addressName;
	}

	public String getAddressMobile() {
		return addressMobile;
	}

	public void setAddressMobile(String addressMobile) {
		this.addressMobile = addressMobile;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getUserCatalogName() {
		return userCatalogName;
	}

	public void setUserCatalogName(String userCatalogName) {
		this.userCatalogName = userCatalogName;
	}

	public List<String> getProductNames() {
		return productNames;
	}

	public void setProductNames(List<String> productNames) {
		this.productNames = productNames;
	}

	public String getStageName() {
		return stageName;
	}

	public void setStageName(String stageName) {
		this.stageName = stageName;
	}

	public Integer getTotalProductCount() {
		return totalProductCount;
	}

	public void setTotalProductCount(Integer totalProductCount) {
		this.totalProductCount = totalProductCount;
	}

	public List<OrderProductResponse> getOrderProducts() {
		return orderProducts;
	}

	public void setOrderProducts(List<OrderProductResponse> orderProducts) {
		this.orderProducts = orderProducts;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getOrderCatalogName() {
		return orderCatalogName;
	}

	public void setOrderCatalogName(String orderCatalogName) {
		this.orderCatalogName = orderCatalogName;
	}

	public String getSelectCatalogName() {
		return selectCatalogName;
	}

	public void setSelectCatalogName(String selectCatalogName) {
		this.selectCatalogName = selectCatalogName;
	}

	public String getOperateStageName() {
		return operateStageName;
	}

	public void setOperateStageName(String operateStageName) {
		this.operateStageName = operateStageName;
	}

	public String getDeliveryCatalogName() {
		return deliveryCatalogName;
	}

	public void setDeliveryCatalogName(String deliveryCatalogName) {
		this.deliveryCatalogName = deliveryCatalogName;
	}

	public String getCommissionUserName() {
		return commissionUserName;
	}

	public void setCommissionUserName(String commissionUserName) {
		this.commissionUserName = commissionUserName;
	}

	public String getSalesTeacherName() {
		return salesTeacherName;
	}

	public void setSalesTeacherName(String salesTeacherName) {
		this.salesTeacherName = salesTeacherName;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public BigDecimal getTotalCommission() {
		return totalCommission;
	}

	public void setTotalCommission(BigDecimal totalCommission) {
		this.totalCommission = totalCommission;
	}

	public BigDecimal getTotalNotPayAmount() {
		return totalNotPayAmount;
	}

	public void setTotalNotPayAmount(BigDecimal totalNotPayAmount) {
		this.totalNotPayAmount = totalNotPayAmount;
	}

	public BigDecimal getTotalRefundAmount() {
		return totalRefundAmount;
	}

	public void setTotalRefundAmount(BigDecimal totalRefundAmount) {
		this.totalRefundAmount = totalRefundAmount;
	}
}
