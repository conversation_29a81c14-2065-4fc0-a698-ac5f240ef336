package com.service;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.common.util.DateUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

public class BaseService {
	protected final String DATE_FORMAT = "yyyy-MM-dd";
	protected final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	Logger logger = LoggerFactory.getLogger(this.getClass());
	
	Boolean isEmpty(String str){
		if(str != null){
			str = str.trim();
			if(!"".equals(str) && !"null".equalsIgnoreCase(str) && !"undefined".equalsIgnoreCase(str)){
				return false;
			}
		}
		return true;
	}
	Boolean isEmpty(Object obj){
		if(obj != null){
			if(!"".equals(obj) && !"null".equals(obj) && !"undefined".equals(obj)){
				return false;
			}
		}
		return true;
	}
	Date getServerTime(){
		return DateUtil.getServerTime();
	}

	Date getDatetime(String datetime, String format){
		if( datetime != null){
			return DateUtil.parse(datetime, format);
		}
		return null;
	}

	public <T> T objectToMap(Object object, TypeReference<T> typeRef) {
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
		try {
			return objectMapper.convertValue(object, typeRef);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public <T> T getObject(String str, TypeReference<T> typeRef) {
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
		try {
			return objectMapper.readValue(str, typeRef);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
	Object getObject(String str, Class<?> clazz) {
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
			return objectMapper.readValue(str, clazz);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	String getJSON(Object obj){
		ObjectMapper objectMapper = new ObjectMapper();
		try{
			return objectMapper.writeValueAsString(obj);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

}
