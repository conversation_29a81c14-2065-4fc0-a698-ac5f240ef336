package com.service;

import java.util.List;
import java.util.Map;

import com.domain.UserCommission;
import com.domain.UserCommissionCash;
import com.domain.UserCommissionCashFlow;
import com.domain.UserCommissionFlow;
import com.domain.complex.UserCommissionFlowQuery;
/**
 * 用户佣金明细表业务层
 *
 * @date 2025-07-27 11:32:54
 */
public interface UserCommissionFlowService {

    Integer create(UserCommissionFlow userCommissionFlow);

    Integer modifyById(UserCommissionFlow userCommissionFlow);

    UserCommissionFlow findById(Long id);

    List<UserCommissionFlow> find(UserCommissionFlowQuery userCommissionFlowQuery);

    List<UserCommissionFlow> findAll(UserCommissionFlowQuery userCommissionFlowQuery);

    Integer count(UserCommissionFlowQuery userCommissionFlowQuery);

    List<UserCommissionFlow> findByIds(List<Long> ids);

    Map<Long, UserCommissionFlow> findMapByIds(List<Long> ids);

    Integer createBatch(List<UserCommissionFlow> userCommissionFlows);

    Integer modifyBatch(List<UserCommissionFlow> userCommissionFlows);

    Integer create(UserCommissionFlow commissionFlow, UserCommission updateCommission, UserCommissionCash userCommissionCash, List<UserCommissionCashFlow> userCommissionCashFlowList);

    void createBatch(List<UserCommissionFlow> commissionFlows, List<UserCommission> updateCommissions, List<UserCommissionCash> userCommissionCashes, List<UserCommissionCashFlow> userCommissionCashFlows);
}

