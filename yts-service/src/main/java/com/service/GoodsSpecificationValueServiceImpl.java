package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.GoodsSpecificationValue;
import com.domain.complex.GoodsSpecificationValueQuery;

import com.dao.GoodsSpecificationValueDao;

@Service
public class GoodsSpecificationValueServiceImpl extends BaseService implements GoodsSpecificationValueService {

    @Autowired
    private GoodsSpecificationValueDao goodsSpecificationValueDao;

    @Override
    public Integer create(GoodsSpecificationValue goodsSpecificationValue) {
        return goodsSpecificationValueDao.insert(goodsSpecificationValue);
    }

    @Override
    public Integer modifyById(GoodsSpecificationValue goodsSpecificationValue) {
        return goodsSpecificationValueDao.updateById(goodsSpecificationValue);
    }

    @Override
    public GoodsSpecificationValue findById(Long id) {
        return goodsSpecificationValueDao.selectById(id);
    }

    @Override
    public List<GoodsSpecificationValue> find(GoodsSpecificationValueQuery goodsSpecificationValueQuery) {
        return goodsSpecificationValueDao.select(goodsSpecificationValueQuery);
    }

    @Override
    public List<GoodsSpecificationValue> findAll(GoodsSpecificationValueQuery goodsSpecificationValueQuery) {
        return goodsSpecificationValueDao.selectAll(goodsSpecificationValueQuery);
    }

    @Override
    public Integer count(GoodsSpecificationValueQuery goodsSpecificationValueQuery) {
        return goodsSpecificationValueDao.count(goodsSpecificationValueQuery);
    }

    @Override
    public List<GoodsSpecificationValue> findByIds(List<Long> ids) {
        return goodsSpecificationValueDao.selectByIds(ids);
    }

    @Override
    public Map<Long, GoodsSpecificationValue> findMapByIds(List<Long> ids) {
        Map<Long, GoodsSpecificationValue> goodsSpecificationValueMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<GoodsSpecificationValue> goodsSpecificationValues = goodsSpecificationValueDao.selectByIds(ids);
            for (GoodsSpecificationValue goodsSpecificationValue : goodsSpecificationValues) {
                goodsSpecificationValueMap.put(goodsSpecificationValue.getId(),goodsSpecificationValue);
            }
        }
        return goodsSpecificationValueMap;
    }

    @Override
    public Integer createBatch(List<GoodsSpecificationValue> goodsSpecificationValues) {
        return goodsSpecificationValueDao.insertBatch(goodsSpecificationValues);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<GoodsSpecificationValue> goodsSpecificationValues) {
        Integer result = 0;
        if (!this.isEmpty(goodsSpecificationValues) && !goodsSpecificationValues.isEmpty()){
            for (GoodsSpecificationValue goodsSpecificationValue : goodsSpecificationValues) {
                goodsSpecificationValueDao.updateById(goodsSpecificationValue);
                result ++;
            }
        }
        return result;
    }
}