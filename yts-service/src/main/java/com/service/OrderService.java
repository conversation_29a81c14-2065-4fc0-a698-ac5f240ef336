package com.service;

import java.util.List;
import java.util.Map;

import com.domain.GoodsSpecificationSku;
import com.domain.Order;
import com.domain.OrderCodeFlow;
import com.domain.OrderProduct;
import com.domain.OrderRefundInfo;
import com.domain.UserCommission;
import com.domain.UserCommissionFlow;
import com.domain.complex.OrderQuery;
/**
 * 订单表业务层
 *
 * @date 2025-07-24 23:22:31
 */
public interface OrderService {

    Integer create(Order order);

    Integer create(Order order, List<OrderProduct> orderProducts, List<GoodsSpecificationSku> goodsSpecificationSkus);

    Integer modifyById(Order order);

    Integer modifyById(Order order, OrderCodeFlow orderCodeFlow);

    Integer modifyById(Order order, List<GoodsSpecificationSku> goodsSpecificationSkus);

    Integer modifyById(Order order, OrderRefundInfo orderRefundInfo);

    Integer modifyById(Order order, String plainText);

    Integer modifyById(Order order, UserCommissionFlow userCommissionFlow, UserCommission modifyUserCommission);

    Integer modifyById(Order order, OrderRefundInfo orderRefundInfo, UserCommissionFlow userCommissionFlow, UserCommission modifyUserCommission);

    Order findById(Long id);

    List<Order> find(OrderQuery orderQuery);

    List<Order> findAll(OrderQuery orderQuery);

    Integer count(OrderQuery orderQuery);

    List<Order> findByIds(List<Long> ids);

    Map<Long, Order> findMapByIds(List<Long> ids);

    Integer createBatch(List<Order> orders);

    Integer modifyBatch(List<Order> orders);

    Order findByCode(String orderNo);
}

