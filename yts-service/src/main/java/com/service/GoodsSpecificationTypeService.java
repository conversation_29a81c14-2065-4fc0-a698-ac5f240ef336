package com.service;

import java.util.List;
import java.util.Map;

import com.domain.GoodsSpecificationType;
import com.domain.complex.GoodsSpecificationTypeQuery;
/**
 * 商品规格类型表业务层
 *
 * @date 2025-07-27 21:26:17
 */
public interface GoodsSpecificationTypeService {

    Integer create(GoodsSpecificationType goodsSpecificationType);

    Integer modifyById(GoodsSpecificationType goodsSpecificationType);

    GoodsSpecificationType findById(Long id);

    List<GoodsSpecificationType> find(GoodsSpecificationTypeQuery goodsSpecificationTypeQuery);

    List<GoodsSpecificationType> findAll(GoodsSpecificationTypeQuery goodsSpecificationTypeQuery);

    Integer count(GoodsSpecificationTypeQuery goodsSpecificationTypeQuery);

    List<GoodsSpecificationType> findByIds(List<Long> ids);

    Map<Long, GoodsSpecificationType> findMapByIds(List<Long> ids);

    Integer createBatch(List<GoodsSpecificationType> goodsSpecificationTypes);

    Integer modifyBatch(List<GoodsSpecificationType> goodsSpecificationTypes);

}

