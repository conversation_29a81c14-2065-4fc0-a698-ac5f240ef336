package com.service;

import java.util.List;
import java.util.Map;

import com.domain.GoodsSpecificationValue;
import com.domain.complex.GoodsSpecificationValueQuery;
/**
 * 商品规格值表业务层
 *
 * @date 2025-07-27 21:26:17
 */
public interface GoodsSpecificationValueService {

    Integer create(GoodsSpecificationValue goodsSpecificationValue);

    Integer modifyById(GoodsSpecificationValue goodsSpecificationValue);

    GoodsSpecificationValue findById(Long id);

    List<GoodsSpecificationValue> find(GoodsSpecificationValueQuery goodsSpecificationValueQuery);

    List<GoodsSpecificationValue> findAll(GoodsSpecificationValueQuery goodsSpecificationValueQuery);

    Integer count(GoodsSpecificationValueQuery goodsSpecificationValueQuery);

    List<GoodsSpecificationValue> findByIds(List<Long> ids);

    Map<Long, GoodsSpecificationValue> findMapByIds(List<Long> ids);

    Integer createBatch(List<GoodsSpecificationValue> goodsSpecificationValues);

    Integer modifyBatch(List<GoodsSpecificationValue> goodsSpecificationValues);

}

