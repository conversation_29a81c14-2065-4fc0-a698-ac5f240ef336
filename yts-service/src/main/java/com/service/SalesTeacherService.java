package com.service;

import java.util.List;
import java.util.Map;

import com.domain.SalesTeacher;
import com.domain.complex.SalesTeacherQuery;
/**
 * 销售老师表业务层
 *
 * @date 2025-07-24 23:22:31
 */
public interface SalesTeacherService {

    Integer create(SalesTeacher salesTeacher);

    Integer modifyById(SalesTeacher salesTeacher);

    SalesTeacher findById(Long id);

    List<SalesTeacher> find(SalesTeacherQuery salesTeacherQuery);

    List<SalesTeacher> findAll(SalesTeacherQuery salesTeacherQuery);

    Integer count(SalesTeacherQuery salesTeacherQuery);

    List<SalesTeacher> findByIds(List<Long> ids);

    Map<Long, SalesTeacher> findMapByIds(List<Long> ids);

    Integer createBatch(List<SalesTeacher> salesTeachers);

    Integer modifyBatch(List<SalesTeacher> salesTeachers);

}

