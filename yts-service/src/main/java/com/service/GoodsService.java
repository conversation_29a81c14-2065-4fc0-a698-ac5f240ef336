package com.service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.domain.Goods;
import com.domain.GoodsSpecificationSku;
import com.domain.GoodsSpecificationType;
import com.domain.GoodsSpecificationValue;
import com.domain.complex.GoodsQuery;
/**
 * 商品表业务层
 *
 * @date 2025-07-24 23:22:31
 */
public interface GoodsService {

    Integer create(Goods goods);

    Integer create(Goods goods, LinkedHashMap<GoodsSpecificationType, List<GoodsSpecificationValue>> goodsSpecificationTypeMap, List<GoodsSpecificationSku> goodsSpecificationSkus);

    Integer modifyById(Goods goods);

    Goods findById(Long id);

    List<Goods> find(GoodsQuery goodsQuery);

    List<Goods> findAll(GoodsQuery goodsQuery);

    Integer count(GoodsQuery goodsQuery);

    List<Goods> findByIds(List<Long> ids);

    Map<Long, Goods> findMapByIds(List<Long> ids);

    Integer createBatch(List<Goods> goodss);

    Integer modifyBatch(List<Goods> goodss);

    Integer modify(Goods goods, LinkedHashMap<GoodsSpecificationType, List<GoodsSpecificationValue>> goodsSpecificationTypeMap, List<GoodsSpecificationSku> goodsSpecificationSkus);

    Integer remove(Goods goods, List<GoodsSpecificationType> goodsSpecificationTypes, List<GoodsSpecificationValue> goodsSpecificationValues, List<GoodsSpecificationSku> goodsSpecificationSkus);
}

