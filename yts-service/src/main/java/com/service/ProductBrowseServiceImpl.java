package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.ProductBrowseDao;
import com.domain.ProductBrowse;
import com.domain.complex.ProductBrowseQuery;

@Service
public class ProductBrowseServiceImpl extends BaseService implements ProductBrowseService {

    @Autowired
    private ProductBrowseDao productBrowseDao;

    @Override
    public Integer create(ProductBrowse productBrowse) {
        return productBrowseDao.insert(productBrowse);
    }

    @Override
    public Integer modifyById(ProductBrowse productBrowse) {
        return productBrowseDao.updateById(productBrowse);
    }

    @Override
    public ProductBrowse findById(Long id) {
        return productBrowseDao.selectById(id);
    }

    @Override
    public List<ProductBrowse> find(ProductBrowseQuery productBrowseQuery) {
        return productBrowseDao.select(productBrowseQuery);
    }

    @Override
    public List<ProductBrowse> findAll(ProductBrowseQuery productBrowseQuery) {
        return productBrowseDao.selectAll(productBrowseQuery);
    }

    @Override
    public Integer count(ProductBrowseQuery productBrowseQuery) {
        return productBrowseDao.count(productBrowseQuery);
    }

    @Override
    public List<ProductBrowse> findByIds(List<Long> ids) {
        return productBrowseDao.selectByIds(ids);
    }

    @Override
    public Map<Long, ProductBrowse> findMapByIds(List<Long> ids) {
        Map<Long, ProductBrowse> productBrowseMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<ProductBrowse> productBrowses = productBrowseDao.selectByIds(ids);
            for (ProductBrowse productBrowse : productBrowses) {
                productBrowseMap.put(productBrowse.getId(),productBrowse);
            }
        }
        return productBrowseMap;
    }

    @Override
    public Integer createBatch(List<ProductBrowse> productBrowses) {
        return productBrowseDao.insertBatch(productBrowses);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<ProductBrowse> productBrowses) {
        Integer result = 0;
        if (!this.isEmpty(productBrowses) && !productBrowses.isEmpty()){
            for (ProductBrowse productBrowse : productBrowses) {
                productBrowseDao.updateById(productBrowse);
                result ++;
            }
        }
        return result;
    }
}