package com.service;

import java.util.List;
import java.util.Map;

import com.domain.UserCommission;
import com.domain.UserCommissionFlow;
import com.domain.complex.UserCommissionQuery;
/**
 * 用户佣金表业务层
 *
 * @date 2025-07-27 11:32:54
 */
public interface UserCommissionService {

    Integer create(UserCommission userCommission);

    Integer modifyById(UserCommission userCommission);

    UserCommission findById(Long id);

    UserCommission findByUserId(Long userId);

    List<UserCommission> find(UserCommissionQuery userCommissionQuery);

    List<UserCommission> findAll(UserCommissionQuery userCommissionQuery);

    Integer count(UserCommissionQuery userCommissionQuery);

    List<UserCommission> findByIds(List<Long> ids);

    Map<Long, UserCommission> findMapByIds(List<Long> ids);

    Integer createBatch(List<UserCommission> userCommissions);

    Integer modifyBatch(List<UserCommission> userCommissions);

    Integer save(UserCommission userCommission, UserCommissionFlow userCommissionFlow);
}

