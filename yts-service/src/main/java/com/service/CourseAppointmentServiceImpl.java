package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.CourseAppointment;
import com.domain.complex.CourseAppointmentQuery;

import com.dao.CourseAppointmentDao;

@Service
public class CourseAppointmentServiceImpl extends BaseService implements CourseAppointmentService {

    @Autowired
    private CourseAppointmentDao courseAppointmentDao;

    @Override
    public Integer create(CourseAppointment courseAppointment) {
        return courseAppointmentDao.insert(courseAppointment);
    }

    @Override
    public Integer modifyById(CourseAppointment courseAppointment) {
        return courseAppointmentDao.updateById(courseAppointment);
    }

    @Override
    public CourseAppointment findById(Long id) {
        return courseAppointmentDao.selectById(id);
    }

    @Override
    public List<CourseAppointment> find(CourseAppointmentQuery courseAppointmentQuery) {
        return courseAppointmentDao.select(courseAppointmentQuery);
    }

    @Override
    public List<CourseAppointment> findAll(CourseAppointmentQuery courseAppointmentQuery) {
        return courseAppointmentDao.selectAll(courseAppointmentQuery);
    }

    @Override
    public Integer count(CourseAppointmentQuery courseAppointmentQuery) {
        return courseAppointmentDao.count(courseAppointmentQuery);
    }

    @Override
    public List<CourseAppointment> findByIds(List<Long> ids) {
        return courseAppointmentDao.selectByIds(ids);
    }

    @Override
    public Map<Long, CourseAppointment> findMapByIds(List<Long> ids) {
        Map<Long, CourseAppointment> courseAppointmentMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<CourseAppointment> courseAppointments = courseAppointmentDao.selectByIds(ids);
            for (CourseAppointment courseAppointment : courseAppointments) {
                courseAppointmentMap.put(courseAppointment.getId(),courseAppointment);
            }
        }
        return courseAppointmentMap;
    }

    @Override
    public Integer createBatch(List<CourseAppointment> courseAppointments) {
        return courseAppointmentDao.insertBatch(courseAppointments);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<CourseAppointment> courseAppointments) {
        Integer result = 0;
        if (!this.isEmpty(courseAppointments) && !courseAppointments.isEmpty()){
            for (CourseAppointment courseAppointment : courseAppointments) {
                courseAppointmentDao.updateById(courseAppointment);
                result ++;
            }
        }
        return result;
    }
}