package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.WechatApp;
import com.domain.complex.WechatAppQuery;

import com.dao.WechatAppDao;

@Service
public class WechatAppServiceImpl extends BaseService implements WechatAppService {

    @Autowired
    private WechatAppDao wechatAppDao;

    @Override
    public Integer create(WechatApp wechatApp) {
        return wechatAppDao.insert(wechatApp);
    }

    @Override
    public Integer modifyById(WechatApp wechatApp) {
        return wechatAppDao.updateById(wechatApp);
    }

    @Override
    public WechatApp findById(Long id) {
        return wechatAppDao.selectById(id);
    }

    @Override
    public List<WechatApp> find(WechatAppQuery wechatAppQuery) {
        return wechatAppDao.select(wechatAppQuery);
    }

    @Override
    public List<WechatApp> findAll(WechatAppQuery wechatAppQuery) {
        return wechatAppDao.selectAll(wechatAppQuery);
    }

    @Override
    public Integer count(WechatAppQuery wechatAppQuery) {
        return wechatAppDao.count(wechatAppQuery);
    }

    @Override
    public List<WechatApp> findByIds(List<Long> ids) {
        return wechatAppDao.selectByIds(ids);
    }

    @Override
    public Map<Long, WechatApp> findMapByIds(List<Long> ids) {
        Map<Long, WechatApp> wechatAppMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<WechatApp> wechatApps = wechatAppDao.selectByIds(ids);
            for (WechatApp wechatApp : wechatApps) {
                wechatAppMap.put(wechatApp.getId(),wechatApp);
            }
        }
        return wechatAppMap;
    }

    @Override
    public Integer createBatch(List<WechatApp> wechatApps) {
        return wechatAppDao.insertBatch(wechatApps);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<WechatApp> wechatApps) {
        Integer result = 0;
        if (!this.isEmpty(wechatApps) && !wechatApps.isEmpty()){
            for (WechatApp wechatApp : wechatApps) {
                wechatAppDao.updateById(wechatApp);
                result ++;
            }
        }
        return result;
    }
}