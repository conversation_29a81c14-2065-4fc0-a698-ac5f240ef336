package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.GoodsSpecificationType;
import com.domain.complex.GoodsSpecificationTypeQuery;

import com.dao.GoodsSpecificationTypeDao;

@Service
public class GoodsSpecificationTypeServiceImpl extends BaseService implements GoodsSpecificationTypeService {

    @Autowired
    private GoodsSpecificationTypeDao goodsSpecificationTypeDao;

    @Override
    public Integer create(GoodsSpecificationType goodsSpecificationType) {
        return goodsSpecificationTypeDao.insert(goodsSpecificationType);
    }

    @Override
    public Integer modifyById(GoodsSpecificationType goodsSpecificationType) {
        return goodsSpecificationTypeDao.updateById(goodsSpecificationType);
    }

    @Override
    public GoodsSpecificationType findById(Long id) {
        return goodsSpecificationTypeDao.selectById(id);
    }

    @Override
    public List<GoodsSpecificationType> find(GoodsSpecificationTypeQuery goodsSpecificationTypeQuery) {
        return goodsSpecificationTypeDao.select(goodsSpecificationTypeQuery);
    }

    @Override
    public List<GoodsSpecificationType> findAll(GoodsSpecificationTypeQuery goodsSpecificationTypeQuery) {
        return goodsSpecificationTypeDao.selectAll(goodsSpecificationTypeQuery);
    }

    @Override
    public Integer count(GoodsSpecificationTypeQuery goodsSpecificationTypeQuery) {
        return goodsSpecificationTypeDao.count(goodsSpecificationTypeQuery);
    }

    @Override
    public List<GoodsSpecificationType> findByIds(List<Long> ids) {
        return goodsSpecificationTypeDao.selectByIds(ids);
    }

    @Override
    public Map<Long, GoodsSpecificationType> findMapByIds(List<Long> ids) {
        Map<Long, GoodsSpecificationType> goodsSpecificationTypeMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<GoodsSpecificationType> goodsSpecificationTypes = goodsSpecificationTypeDao.selectByIds(ids);
            for (GoodsSpecificationType goodsSpecificationType : goodsSpecificationTypes) {
                goodsSpecificationTypeMap.put(goodsSpecificationType.getId(),goodsSpecificationType);
            }
        }
        return goodsSpecificationTypeMap;
    }

    @Override
    public Integer createBatch(List<GoodsSpecificationType> goodsSpecificationTypes) {
        return goodsSpecificationTypeDao.insertBatch(goodsSpecificationTypes);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<GoodsSpecificationType> goodsSpecificationTypes) {
        Integer result = 0;
        if (!this.isEmpty(goodsSpecificationTypes) && !goodsSpecificationTypes.isEmpty()){
            for (GoodsSpecificationType goodsSpecificationType : goodsSpecificationTypes) {
                goodsSpecificationTypeDao.updateById(goodsSpecificationType);
                result ++;
            }
        }
        return result;
    }
}