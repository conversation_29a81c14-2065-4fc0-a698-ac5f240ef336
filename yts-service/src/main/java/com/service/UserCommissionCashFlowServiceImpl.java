package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.UserCommissionCashFlow;
import com.domain.complex.UserCommissionCashFlowQuery;

import com.dao.UserCommissionCashFlowDao;

@Service
public class UserCommissionCashFlowServiceImpl extends BaseService implements UserCommissionCashFlowService {

    @Autowired
    private UserCommissionCashFlowDao userCommissionCashFlowDao;

    @Override
    public Integer create(UserCommissionCashFlow userCommissionCashFlow) {
        return userCommissionCashFlowDao.insert(userCommissionCashFlow);
    }

    @Override
    public Integer modifyById(UserCommissionCashFlow userCommissionCashFlow) {
        return userCommissionCashFlowDao.updateById(userCommissionCashFlow);
    }

    @Override
    public UserCommissionCashFlow findById(Long id) {
        return userCommissionCashFlowDao.selectById(id);
    }

    @Override
    public List<UserCommissionCashFlow> find(UserCommissionCashFlowQuery userCommissionCashFlowQuery) {
        return userCommissionCashFlowDao.select(userCommissionCashFlowQuery);
    }

    @Override
    public List<UserCommissionCashFlow> findAll(UserCommissionCashFlowQuery userCommissionCashFlowQuery) {
        return userCommissionCashFlowDao.selectAll(userCommissionCashFlowQuery);
    }

    @Override
    public Integer count(UserCommissionCashFlowQuery userCommissionCashFlowQuery) {
        return userCommissionCashFlowDao.count(userCommissionCashFlowQuery);
    }

    @Override
    public List<UserCommissionCashFlow> findByIds(List<Long> ids) {
        return userCommissionCashFlowDao.selectByIds(ids);
    }

    @Override
    public Map<Long, UserCommissionCashFlow> findMapByIds(List<Long> ids) {
        Map<Long, UserCommissionCashFlow> userCommissionCashFlowMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserCommissionCashFlow> userCommissionCashFlows = userCommissionCashFlowDao.selectByIds(ids);
            for (UserCommissionCashFlow userCommissionCashFlow : userCommissionCashFlows) {
                userCommissionCashFlowMap.put(userCommissionCashFlow.getId(),userCommissionCashFlow);
            }
        }
        return userCommissionCashFlowMap;
    }

    @Override
    public Integer createBatch(List<UserCommissionCashFlow> userCommissionCashFlows) {
        return userCommissionCashFlowDao.insertBatch(userCommissionCashFlows);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<UserCommissionCashFlow> userCommissionCashFlows) {
        Integer result = 0;
        if (!this.isEmpty(userCommissionCashFlows) && !userCommissionCashFlows.isEmpty()){
            for (UserCommissionCashFlow userCommissionCashFlow : userCommissionCashFlows) {
                userCommissionCashFlowDao.updateById(userCommissionCashFlow);
                result ++;
            }
        }
        return result;
    }
}