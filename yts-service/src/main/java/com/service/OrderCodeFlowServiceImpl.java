package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.OrderCodeFlow;
import com.domain.complex.OrderCodeFlowQuery;

import com.dao.OrderCodeFlowDao;

@Service
public class OrderCodeFlowServiceImpl extends BaseService implements OrderCodeFlowService {

    @Autowired
    private OrderCodeFlowDao orderCodeFlowDao;

    @Override
    public Integer create(OrderCodeFlow orderCodeFlow) {
        return orderCodeFlowDao.insert(orderCodeFlow);
    }

    @Override
    public Integer modifyById(OrderCodeFlow orderCodeFlow) {
        return orderCodeFlowDao.updateById(orderCodeFlow);
    }

    @Override
    public OrderCodeFlow findById(Long id) {
        return orderCodeFlowDao.selectById(id);
    }

    @Override
    public List<OrderCodeFlow> find(OrderCodeFlowQuery orderCodeFlowQuery) {
        return orderCodeFlowDao.select(orderCodeFlowQuery);
    }

    @Override
    public List<OrderCodeFlow> findAll(OrderCodeFlowQuery orderCodeFlowQuery) {
        return orderCodeFlowDao.selectAll(orderCodeFlowQuery);
    }

    @Override
    public Integer count(OrderCodeFlowQuery orderCodeFlowQuery) {
        return orderCodeFlowDao.count(orderCodeFlowQuery);
    }

    @Override
    public List<OrderCodeFlow> findByIds(List<Long> ids) {
        return orderCodeFlowDao.selectByIds(ids);
    }

    @Override
    public Map<Long, OrderCodeFlow> findMapByIds(List<Long> ids) {
        Map<Long, OrderCodeFlow> orderCodeFlowMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<OrderCodeFlow> orderCodeFlows = orderCodeFlowDao.selectByIds(ids);
            for (OrderCodeFlow orderCodeFlow : orderCodeFlows) {
                orderCodeFlowMap.put(orderCodeFlow.getId(),orderCodeFlow);
            }
        }
        return orderCodeFlowMap;
    }

    @Override
    public Integer createBatch(List<OrderCodeFlow> orderCodeFlows) {
        return orderCodeFlowDao.insertBatch(orderCodeFlows);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<OrderCodeFlow> orderCodeFlows) {
        Integer result = 0;
        if (!this.isEmpty(orderCodeFlows) && !orderCodeFlows.isEmpty()){
            for (OrderCodeFlow orderCodeFlow : orderCodeFlows) {
                orderCodeFlowDao.updateById(orderCodeFlow);
                result ++;
            }
        }
        return result;
    }
}