package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.File;
import com.domain.complex.FileQuery;

import com.dao.FileDao;

@Service
public class FileServiceImpl extends BaseService implements FileService {

    @Autowired
    private FileDao fileDao;

    @Override
    public Integer create(File file) {
        return fileDao.insert(file);
    }

    @Override
    public Integer modifyById(File file) {
        return fileDao.updateById(file);
    }

    @Override
    public File findById(Long id) {
        return fileDao.selectById(id);
    }

    @Override
    public List<File> find(FileQuery fileQuery) {
        return fileDao.select(fileQuery);
    }

    @Override
    public List<File> findAll(FileQuery fileQuery) {
        return fileDao.selectAll(fileQuery);
    }

    @Override
    public Integer count(FileQuery fileQuery) {
        return fileDao.count(fileQuery);
    }

    @Override
    public List<File> findByIds(List<Long> ids) {
        return fileDao.selectByIds(ids);
    }

    @Override
    public Map<Long, File> findMapByIds(List<Long> ids) {
        Map<Long, File> fileMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<File> files = fileDao.selectByIds(ids);
            for (File file : files) {
                fileMap.put(file.getId(),file);
            }
        }
        return fileMap;
    }

    @Override
    public Integer createBatch(List<File> files) {
        return fileDao.insertBatch(files);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<File> files) {
        Integer result = 0;
        if (!this.isEmpty(files) && !files.isEmpty()){
            for (File file : files) {
                fileDao.updateById(file);
                result ++;
            }
        }
        return result;
    }

    @Override
    public File findByUrl(String url) {
        return fileDao.selectByUrl(url);
    }
}