package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.GoodsTypeDao;
import com.domain.GoodsType;
import com.domain.complex.GoodsTypeQuery;

@Service
public class GoodsTypeServiceImpl extends BaseService implements GoodsTypeService {

    @Autowired
    private GoodsTypeDao goodsTypeDao;

    @Override
    public Integer create(GoodsType goodsType) {
        return goodsTypeDao.insert(goodsType);
    }

    @Override
    public Integer modifyById(GoodsType goodsType) {
        return goodsTypeDao.updateById(goodsType);
    }

    @Override
    public GoodsType findById(Long id) {
        return goodsTypeDao.selectById(id);
    }

    @Override
    public List<GoodsType> find(GoodsTypeQuery goodsTypeQuery) {
        return goodsTypeDao.select(goodsTypeQuery);
    }

    @Override
    public List<GoodsType> findAll(GoodsTypeQuery goodsTypeQuery) {
        return goodsTypeDao.selectAll(goodsTypeQuery);
    }

    @Override
    public Integer count(GoodsTypeQuery goodsTypeQuery) {
        return goodsTypeDao.count(goodsTypeQuery);
    }

    @Override
    public List<GoodsType> findByIds(List<Long> ids) {
        return goodsTypeDao.selectByIds(ids);
    }

    @Override
    public Map<Long, GoodsType> findMapByIds(List<Long> ids) {
        Map<Long, GoodsType> goodsTypeMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<GoodsType> goodsTypes = goodsTypeDao.selectByIds(ids);
            for (GoodsType goodsType : goodsTypes) {
                goodsTypeMap.put(goodsType.getId(),goodsType);
            }
        }
        return goodsTypeMap;
    }

    @Override
    public Integer createBatch(List<GoodsType> goodsTypes) {
        return goodsTypeDao.insertBatch(goodsTypes);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<GoodsType> goodsTypes) {
        Integer result = 0;
        if (!this.isEmpty(goodsTypes) && !goodsTypes.isEmpty()){
            for (GoodsType goodsType : goodsTypes) {
                goodsTypeDao.updateById(goodsType);
                result ++;
            }
        }
        return result;
    }
}