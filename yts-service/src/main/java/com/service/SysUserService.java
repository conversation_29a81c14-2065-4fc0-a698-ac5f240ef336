package com.service;

import java.util.List;
import java.util.Map;

import com.domain.SysUser;
import com.domain.complex.SysUserQuery;
/**
 * 系统用户表业务层
 *
 * @date 2025-07-16 21:03:31
 */
public interface SysUserService {

    Integer create(SysUser sysUser);

    Integer modifyById(SysUser sysUser);

    SysUser findById(Long id);

    List<SysUser> find(SysUserQuery sysUserQuery);

    List<SysUser> findAll(SysUserQuery sysUserQuery);

    Integer count(SysUserQuery sysUserQuery);

    List<SysUser> findByIds(List<Long> ids);

    Map<Long, SysUser> findMapByIds(List<Long> ids);

    Integer createBatch(List<SysUser> sysUsers);

    Integer modifyBatch(List<SysUser> sysUsers);

}

