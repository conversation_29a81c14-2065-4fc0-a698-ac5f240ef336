package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.Course;
import com.domain.complex.CourseQuery;

import com.dao.CourseDao;

@Service
public class CourseServiceImpl extends BaseService implements CourseService {

    @Autowired
    private CourseDao courseDao;

    @Override
    public Integer create(Course course) {
        return courseDao.insert(course);
    }

    @Override
    public Integer modifyById(Course course) {
        return courseDao.updateById(course);
    }

    @Override
    public Course findById(Long id) {
        return courseDao.selectById(id);
    }

    @Override
    public List<Course> find(CourseQuery courseQuery) {
        return courseDao.select(courseQuery);
    }

    @Override
    public List<Course> findAll(CourseQuery courseQuery) {
        return courseDao.selectAll(courseQuery);
    }

    @Override
    public Integer count(CourseQuery courseQuery) {
        return courseDao.count(courseQuery);
    }

    @Override
    public List<Course> findByIds(List<Long> ids) {
        return courseDao.selectByIds(ids);
    }

    @Override
    public Map<Long, Course> findMapByIds(List<Long> ids) {
        Map<Long, Course> courseMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<Course> courses = courseDao.selectByIds(ids);
            for (Course course : courses) {
                courseMap.put(course.getId(),course);
            }
        }
        return courseMap;
    }

    @Override
    public Integer createBatch(List<Course> courses) {
        return courseDao.insertBatch(courses);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<Course> courses) {
        Integer result = 0;
        if (!this.isEmpty(courses) && !courses.isEmpty()){
            for (Course course : courses) {
                courseDao.updateById(course);
                result ++;
            }
        }
        return result;
    }
}