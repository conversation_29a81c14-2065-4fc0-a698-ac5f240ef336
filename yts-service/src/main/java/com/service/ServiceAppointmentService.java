package com.service;

import java.util.List;
import java.util.Map;

import com.domain.ServiceAppointment;
import com.domain.complex.ServiceAppointmentQuery;
/**
 * 服务预约表业务层
 *
 * @date 2025-07-19 10:58:27
 */
public interface ServiceAppointmentService {

    Integer create(ServiceAppointment serviceAppointment);

    Integer modifyById(ServiceAppointment serviceAppointment);

    ServiceAppointment findById(Long id);

    List<ServiceAppointment> find(ServiceAppointmentQuery serviceAppointmentQuery);

    List<ServiceAppointment> findAll(ServiceAppointmentQuery serviceAppointmentQuery);

    Integer count(ServiceAppointmentQuery serviceAppointmentQuery);

    List<ServiceAppointment> findByIds(List<Long> ids);

    Map<Long, ServiceAppointment> findMapByIds(List<Long> ids);

    Integer createBatch(List<ServiceAppointment> serviceAppointments);

    Integer modifyBatch(List<ServiceAppointment> serviceAppointments);

}

