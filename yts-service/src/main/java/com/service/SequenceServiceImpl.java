package com.service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.common.constant.DataStatus;
import com.domain.Sequence;
import com.domain.complex.SequenceQuery;

import com.dao.SequenceDao;

@Service
public class SequenceServiceImpl extends BaseService implements SequenceService {

    @Autowired
    private SequenceDao sequenceDao;

    @Override
    public Integer create(Sequence sequence) {
        Date datetime = this.getServerTime();
        sequence.setStatus(DataStatus.Y.getCode());
        sequence.setModifyTime(datetime);
        sequence.setCreateTime(datetime);
        return sequenceDao.insert(sequence);
    }

    @Override
    public Integer modifyById(Sequence sequence) {
        return sequenceDao.updateById(sequence);
    }

    @Override
    public Sequence findById(Long id) {
        return sequenceDao.selectById(id);
    }

    @Override
    public List<Sequence> find(SequenceQuery sequenceQuery) {
        return sequenceDao.select(sequenceQuery);
    }

    @Override
    public List<Sequence> findAll(SequenceQuery sequenceQuery) {
        return sequenceDao.selectAll(sequenceQuery);
    }

    @Override
    public Integer count(SequenceQuery sequenceQuery) {
        return sequenceDao.count(sequenceQuery);
    }

    @Override
    public List<Sequence> findByIds(List<Long> ids) {
        return sequenceDao.selectByIds(ids);
    }

    @Override
    public Map<Long, Sequence> findMapByIds(List<Long> ids) {
        Map<Long, Sequence> sequenceMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<Sequence> sequences = sequenceDao.selectByIds(ids);
            for (Sequence sequence : sequences) {
                sequenceMap.put(sequence.getId(),sequence);
            }
        }
        return sequenceMap;
    }

    @Override
    public Integer createBatch(List<Sequence> sequences) {
        return sequenceDao.insertBatch(sequences);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<Sequence> sequences) {
        Integer result = 0;
        if (!this.isEmpty(sequences) && !sequences.isEmpty()){
            for (Sequence sequence : sequences) {
                sequenceDao.updateById(sequence);
                result ++;
            }
        }
        return result;
    }
}