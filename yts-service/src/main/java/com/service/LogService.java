package com.service;

import java.util.List;
import java.util.Map;

import com.domain.Log;
import com.domain.complex.LogQuery;
/**
 * 系统用户日志表业务层
 *
 * @date 2025-07-20 18:59:08
 */
public interface LogService {

    Integer create(Log log);

    Integer modifyById(Log log);

    Log findById(Long id);

    List<Log> find(LogQuery logQuery);

    List<Log> findAll(LogQuery logQuery);

    Integer count(LogQuery logQuery);

    List<Log> findByIds(List<Long> ids);

    Map<Long, Log> findMapByIds(List<Long> ids);

    Integer createBatch(List<Log> logs);

    Integer modifyBatch(List<Log> logs);

    Integer removeById(Long id);
}

