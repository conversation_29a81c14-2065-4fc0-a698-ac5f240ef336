package com.service;

import java.util.List;
import java.util.Map;

import com.domain.ReportGoods;
import com.domain.complex.ReportGoodsQuery;
/**
 * 统计商品表业务层
 *
 * @date 2025-07-20 16:36:02
 */
public interface ReportGoodsService {

    Integer create(ReportGoods reportGoods);

    Integer modifyById(ReportGoods reportGoods);

    ReportGoods findById(Long id);

    List<ReportGoods> find(ReportGoodsQuery reportGoodsQuery);

    List<ReportGoods> findAll(ReportGoodsQuery reportGoodsQuery);

    Integer count(ReportGoodsQuery reportGoodsQuery);

    List<ReportGoods> findByIds(List<Long> ids);

    Map<Long, ReportGoods> findMapByIds(List<Long> ids);

    Integer createBatch(List<ReportGoods> reportGoodss);

    Integer modifyBatch(List<ReportGoods> reportGoodss);

}

