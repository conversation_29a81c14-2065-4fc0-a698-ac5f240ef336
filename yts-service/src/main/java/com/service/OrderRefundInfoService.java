package com.service;

import java.util.List;
import java.util.Map;

import com.domain.OrderRefundInfo;
import com.domain.complex.OrderRefundInfoQuery;
/**
 * 订单退款记录表业务层
 *
 * @date 2025-07-29 22:28:19
 */
public interface OrderRefundInfoService {

    Integer create(OrderRefundInfo orderRefundInfo);

    Integer modifyById(OrderRefundInfo orderRefundInfo);

    OrderRefundInfo findById(Long id);

    List<OrderRefundInfo> find(OrderRefundInfoQuery orderRefundInfoQuery);

    List<OrderRefundInfo> findAll(OrderRefundInfoQuery orderRefundInfoQuery);

    Integer count(OrderRefundInfoQuery orderRefundInfoQuery);

    List<OrderRefundInfo> findByIds(List<Long> ids);

    Map<Long, OrderRefundInfo> findMapByIds(List<Long> ids);

    Integer createBatch(List<OrderRefundInfo> orderRefundInfos);

    Integer modifyBatch(List<OrderRefundInfo> orderRefundInfos);

    void updateRefund(String bodyAsString);
}

