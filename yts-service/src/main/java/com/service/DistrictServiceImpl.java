package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.District;
import com.domain.complex.DistrictQuery;

import com.dao.DistrictDao;

@Service
public class DistrictServiceImpl extends BaseService implements DistrictService {

    @Autowired
    private DistrictDao districtDao;

    @Override
    public Integer create(District district) {
        return districtDao.insert(district);
    }

    @Override
    public Integer modifyById(District district) {
        return districtDao.updateById(district);
    }

    @Override
    public District findById(Long id) {
        return districtDao.selectById(id);
    }

    @Override
    public List<District> find(DistrictQuery districtQuery) {
        return districtDao.select(districtQuery);
    }

    @Override
    public List<District> findAll(DistrictQuery districtQuery) {
        return districtDao.selectAll(districtQuery);
    }

    @Override
    public Integer count(DistrictQuery districtQuery) {
        return districtDao.count(districtQuery);
    }

    @Override
    public List<District> findByIds(List<Long> ids) {
        return districtDao.selectByIds(ids);
    }

    @Override
    public Map<Long, District> findMapByIds(List<Long> ids) {
        Map<Long, District> districtMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<District> districts = districtDao.selectByIds(ids);
            for (District district : districts) {
                districtMap.put(district.getId(),district);
            }
        }
        return districtMap;
    }

    @Override
    public Integer createBatch(List<District> districts) {
        return districtDao.insertBatch(districts);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<District> districts) {
        Integer result = 0;
        if (!this.isEmpty(districts) && !districts.isEmpty()){
            for (District district : districts) {
                districtDao.updateById(district);
                result ++;
            }
        }
        return result;
    }
}