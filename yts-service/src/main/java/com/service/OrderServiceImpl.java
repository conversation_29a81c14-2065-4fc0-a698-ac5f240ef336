package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.common.bean.SkuStockException;
import com.common.constant.OrderCatalog;
import com.dao.GoodsDao;
import com.dao.GoodsSpecificationSkuDao;
import com.dao.OrderCodeFlowDao;
import com.dao.OrderProductDao;
import com.dao.OrderRefundInfoDao;
import com.dao.UserCommissionDao;
import com.dao.UserCommissionFlowDao;
import com.domain.Goods;
import com.domain.GoodsSpecificationSku;
import com.domain.Order;
import com.domain.OrderCodeFlow;
import com.domain.OrderProduct;
import com.domain.OrderRefundInfo;
import com.domain.UserCommission;
import com.domain.UserCommissionFlow;
import com.domain.complex.OrderQuery;

import com.dao.OrderDao;

@Service
public class OrderServiceImpl extends BaseService implements OrderService {

    @Autowired
    private OrderDao orderDao;
    @Autowired
    private OrderCodeFlowDao orderCodeFlowDao;
    @Autowired
    private OrderProductDao orderProductDao;
    @Autowired
    private GoodsDao goodsDao;
    @Autowired
    private GoodsSpecificationSkuDao goodsSpecificationSkuDao;
    @Autowired
    private UserCommissionFlowDao userCommissionFlowDao;
    @Autowired
    private UserCommissionDao userCommissionDao;
    @Autowired
    private OrderRefundInfoService orderRefundInfoService;

    @Override
    public Integer create(Order order) {
        return orderDao.insert(order);
    }

    @Override
    @Transactional
    public Integer create(Order order,List<OrderProduct> orderProducts,List<GoodsSpecificationSku> goodsSpecificationSkus) {
        orderDao.insert(order);
        for (OrderProduct orderProduct : orderProducts) {
            orderProduct.setOrderId(order.getId());
            orderProductDao.insert(orderProduct);
            if (OrderCatalog.C1.getCode().equals(orderProduct.getProductCatalog())){
                Goods goods = goodsDao.selectById(orderProduct.getProductId());
                if (goods != null){
                    goods.setRealSalesVolume(goods.getRealSalesVolume() + orderProduct.getNumber());
                    goodsDao.updateById(goods);
                }
            }
        }
        for (GoodsSpecificationSku goodsSpecificationSku : goodsSpecificationSkus) {
            if (goodsSpecificationSkuDao.updateStockById(goodsSpecificationSku) == 0) {
                throw new SkuStockException(
                        goodsSpecificationSku.getGoodsId(),
                        goodsSpecificationSku.getSpecValues(),
                        goodsSpecificationSku.getStock(),
                        goodsSpecificationSku.getProductName());
            }
        }
        return null;
    }

    @Override
    public Integer modifyById(Order order) {
        return orderDao.updateById(order);
    }

    @Override
    @Transactional
    public Integer modifyById(Order order, OrderCodeFlow orderCodeFlowCreate) {
        if (this.isEmpty(orderCodeFlowCreate.getId())) {
            orderCodeFlowDao.insert(orderCodeFlowCreate);
        }else if (!this.isEmpty(orderCodeFlowCreate.getId())){
            orderCodeFlowCreate.setModifyTime(null);
            orderCodeFlowDao.updateById(orderCodeFlowCreate);
        }
        return orderDao.updateById(order);
    }

    @Override
    @Transactional
    public Integer modifyById(Order order, List<GoodsSpecificationSku> goodsSpecificationSkus) {
        if (!this.isEmpty(goodsSpecificationSkus) && !goodsSpecificationSkus.isEmpty()) {
            for (GoodsSpecificationSku goodsSpecificationSku : goodsSpecificationSkus) {
                goodsSpecificationSkuDao.updateById(goodsSpecificationSku);
            }
        }
        return orderDao.updateById(order);
    }

    @Override
    @Transactional
    public Integer modifyById(Order order, OrderRefundInfo orderRefundInfo) {
        if (orderRefundInfo != null){
            orderRefundInfoService.create(orderRefundInfo);
        }
        return orderDao.updateById(order);
    }

    @Override
    @Transactional
    public Integer modifyById(Order order, String plainText) {
        orderRefundInfoService.updateRefund(plainText);
        return orderDao.updateById(order);
    }

    @Override
    @Transactional
    public Integer modifyById(Order order, UserCommissionFlow userCommissionFlow, UserCommission modifyUserCommission) {
        if (userCommissionFlow != null){
            userCommissionFlowDao.insert(userCommissionFlow);
        }
        if (modifyUserCommission != null){
            if (this.isEmpty(modifyUserCommission.getId())) {
                userCommissionDao.insert(modifyUserCommission);
            }else {
                userCommissionDao.updateById(modifyUserCommission);
            }
        }
        return orderDao.updateById(order);
    }

    @Override
    @Transactional
    public Integer modifyById(Order order, OrderRefundInfo orderRefundInfo, UserCommissionFlow userCommissionFlow, UserCommission modifyUserCommission) {
        if (orderRefundInfo != null){
            orderRefundInfoService.create(orderRefundInfo);
        }
        if (userCommissionFlow != null){
            userCommissionFlowDao.insert(userCommissionFlow);
        }
        if (modifyUserCommission != null){
            if (this.isEmpty(modifyUserCommission.getId())) {
                userCommissionDao.insert(modifyUserCommission);
            }else {
                userCommissionDao.updateById(modifyUserCommission);
            }
        }
        return orderDao.updateById(order);
    }

    @Override
    public Order findById(Long id) {
        return orderDao.selectById(id);
    }

    @Override
    public List<Order> find(OrderQuery orderQuery) {
        return orderDao.select(orderQuery);
    }

    @Override
    public List<Order> findAll(OrderQuery orderQuery) {
        return orderDao.selectAll(orderQuery);
    }

    @Override
    public Integer count(OrderQuery orderQuery) {
        return orderDao.count(orderQuery);
    }

    @Override
    public List<Order> findByIds(List<Long> ids) {
        return orderDao.selectByIds(ids);
    }

    @Override
    public Map<Long, Order> findMapByIds(List<Long> ids) {
        Map<Long, Order> orderMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<Order> orders = orderDao.selectByIds(ids);
            for (Order order : orders) {
                orderMap.put(order.getId(),order);
            }
        }
        return orderMap;
    }

    @Override
    public Integer createBatch(List<Order> orders) {
        return orderDao.insertBatch(orders);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<Order> orders) {
        Integer result = 0;
        if (!this.isEmpty(orders) && !orders.isEmpty()){
            for (Order order : orders) {
                orderDao.updateById(order);
                result ++;
            }
        }
        return result;
    }

    @Override
    public Order findByCode(String orderNo) {
        return orderDao.selectByCode(orderNo);
    }

}