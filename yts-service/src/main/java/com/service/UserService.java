package com.service;

import java.util.List;
import java.util.Map;

import com.domain.User;
import com.domain.complex.UserQuery;
/**
 * 用户表业务层
 *
 * @date 2025-07-24 23:22:31
 */
public interface UserService {

    Integer create(User user);

    Integer modifyById(User user);

    User findById(Long id);

    List<User> find(UserQuery userQuery);

    List<User> findAll(UserQuery userQuery);

    Integer count(UserQuery userQuery);

    List<User> findByIds(List<Long> ids);

    Map<Long, User> findMapByIds(List<Long> ids);

    Integer createBatch(List<User> users);

    Integer modifyBatch(List<User> users);

    User findByUnionId(String unionId);

    User findByOpenId(String openId);
}

