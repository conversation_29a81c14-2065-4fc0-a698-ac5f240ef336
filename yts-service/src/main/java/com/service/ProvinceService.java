package com.service;

import java.util.List;
import java.util.Map;

import com.domain.Province;
import com.domain.complex.ProvinceQuery;
/**
 * 省份表业务层
 *
 * @date 2025-07-23 22:58:36
 */
public interface ProvinceService {

    Integer create(Province province);

    Integer modifyById(Province province);

    Province findById(Long id);

    List<Province> find(ProvinceQuery provinceQuery);

    List<Province> findAll(ProvinceQuery provinceQuery);

    Integer count(ProvinceQuery provinceQuery);

    List<Province> findByIds(List<Long> ids);

    Map<Long, Province> findMapByIds(List<Long> ids);

    Integer createBatch(List<Province> provinces);

    Integer modifyBatch(List<Province> provinces);

}

