package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.Province;
import com.domain.complex.ProvinceQuery;

import com.dao.ProvinceDao;

@Service
public class ProvinceServiceImpl extends BaseService implements ProvinceService {

    @Autowired
    private ProvinceDao provinceDao;

    @Override
    public Integer create(Province province) {
        return provinceDao.insert(province);
    }

    @Override
    public Integer modifyById(Province province) {
        return provinceDao.updateById(province);
    }

    @Override
    public Province findById(Long id) {
        return provinceDao.selectById(id);
    }

    @Override
    public List<Province> find(ProvinceQuery provinceQuery) {
        return provinceDao.select(provinceQuery);
    }

    @Override
    public List<Province> findAll(ProvinceQuery provinceQuery) {
        return provinceDao.selectAll(provinceQuery);
    }

    @Override
    public Integer count(ProvinceQuery provinceQuery) {
        return provinceDao.count(provinceQuery);
    }

    @Override
    public List<Province> findByIds(List<Long> ids) {
        return provinceDao.selectByIds(ids);
    }

    @Override
    public Map<Long, Province> findMapByIds(List<Long> ids) {
        Map<Long, Province> provinceMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<Province> provinces = provinceDao.selectByIds(ids);
            for (Province province : provinces) {
                provinceMap.put(province.getId(),province);
            }
        }
        return provinceMap;
    }

    @Override
    public Integer createBatch(List<Province> provinces) {
        return provinceDao.insertBatch(provinces);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<Province> provinces) {
        Integer result = 0;
        if (!this.isEmpty(provinces) && !provinces.isEmpty()){
            for (Province province : provinces) {
                provinceDao.updateById(province);
                result ++;
            }
        }
        return result;
    }
}