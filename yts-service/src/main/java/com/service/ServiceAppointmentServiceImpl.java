package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.ServiceAppointment;
import com.domain.complex.ServiceAppointmentQuery;

import com.dao.ServiceAppointmentDao;

@Service
public class ServiceAppointmentServiceImpl extends BaseService implements ServiceAppointmentService {

    @Autowired
    private ServiceAppointmentDao serviceAppointmentDao;

    @Override
    public Integer create(ServiceAppointment serviceAppointment) {
        return serviceAppointmentDao.insert(serviceAppointment);
    }

    @Override
    public Integer modifyById(ServiceAppointment serviceAppointment) {
        return serviceAppointmentDao.updateById(serviceAppointment);
    }

    @Override
    public ServiceAppointment findById(Long id) {
        return serviceAppointmentDao.selectById(id);
    }

    @Override
    public List<ServiceAppointment> find(ServiceAppointmentQuery serviceAppointmentQuery) {
        return serviceAppointmentDao.select(serviceAppointmentQuery);
    }

    @Override
    public List<ServiceAppointment> findAll(ServiceAppointmentQuery serviceAppointmentQuery) {
        return serviceAppointmentDao.selectAll(serviceAppointmentQuery);
    }

    @Override
    public Integer count(ServiceAppointmentQuery serviceAppointmentQuery) {
        return serviceAppointmentDao.count(serviceAppointmentQuery);
    }

    @Override
    public List<ServiceAppointment> findByIds(List<Long> ids) {
        return serviceAppointmentDao.selectByIds(ids);
    }

    @Override
    public Map<Long, ServiceAppointment> findMapByIds(List<Long> ids) {
        Map<Long, ServiceAppointment> serviceAppointmentMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<ServiceAppointment> serviceAppointments = serviceAppointmentDao.selectByIds(ids);
            for (ServiceAppointment serviceAppointment : serviceAppointments) {
                serviceAppointmentMap.put(serviceAppointment.getId(),serviceAppointment);
            }
        }
        return serviceAppointmentMap;
    }

    @Override
    public Integer createBatch(List<ServiceAppointment> serviceAppointments) {
        return serviceAppointmentDao.insertBatch(serviceAppointments);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<ServiceAppointment> serviceAppointments) {
        Integer result = 0;
        if (!this.isEmpty(serviceAppointments) && !serviceAppointments.isEmpty()){
            for (ServiceAppointment serviceAppointment : serviceAppointments) {
                serviceAppointmentDao.updateById(serviceAppointment);
                result ++;
            }
        }
        return result;
    }
}