package com.service;

import java.util.List;
import java.util.Map;

import com.domain.OrderProduct;
import com.domain.complex.OrderProductQuery;
/**
 * 订单产品表业务层
 *
 * @date 2025-07-24 23:22:31
 */
public interface OrderProductService {

    Integer create(OrderProduct orderProduct);

    Integer modifyById(OrderProduct orderProduct);

    OrderProduct findById(Long id);

    List<OrderProduct> find(OrderProductQuery orderProductQuery);

    List<OrderProduct> findAll(OrderProductQuery orderProductQuery);

    Integer count(OrderProductQuery orderProductQuery);

    List<OrderProduct> findByIds(List<Long> ids);

    Map<Long, OrderProduct> findMapByIds(List<Long> ids);

    Integer createBatch(List<OrderProduct> orderProducts);

    Integer modifyBatch(List<OrderProduct> orderProducts);

}

