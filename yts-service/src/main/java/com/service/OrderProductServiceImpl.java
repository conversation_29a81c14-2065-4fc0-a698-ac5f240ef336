package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.OrderProduct;
import com.domain.complex.OrderProductQuery;

import com.dao.OrderProductDao;

@Service
public class OrderProductServiceImpl extends BaseService implements OrderProductService {

    @Autowired
    private OrderProductDao orderProductDao;

    @Override
    public Integer create(OrderProduct orderProduct) {
        return orderProductDao.insert(orderProduct);
    }

    @Override
    public Integer modifyById(OrderProduct orderProduct) {
        return orderProductDao.updateById(orderProduct);
    }

    @Override
    public OrderProduct findById(Long id) {
        return orderProductDao.selectById(id);
    }

    @Override
    public List<OrderProduct> find(OrderProductQuery orderProductQuery) {
        return orderProductDao.select(orderProductQuery);
    }

    @Override
    public List<OrderProduct> findAll(OrderProductQuery orderProductQuery) {
        return orderProductDao.selectAll(orderProductQuery);
    }

    @Override
    public Integer count(OrderProductQuery orderProductQuery) {
        return orderProductDao.count(orderProductQuery);
    }

    @Override
    public List<OrderProduct> findByIds(List<Long> ids) {
        return orderProductDao.selectByIds(ids);
    }

    @Override
    public Map<Long, OrderProduct> findMapByIds(List<Long> ids) {
        Map<Long, OrderProduct> orderProductMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<OrderProduct> orderProducts = orderProductDao.selectByIds(ids);
            for (OrderProduct orderProduct : orderProducts) {
                orderProductMap.put(orderProduct.getId(),orderProduct);
            }
        }
        return orderProductMap;
    }

    @Override
    public Integer createBatch(List<OrderProduct> orderProducts) {
        return orderProductDao.insertBatch(orderProducts);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<OrderProduct> orderProducts) {
        Integer result = 0;
        if (!this.isEmpty(orderProducts) && !orderProducts.isEmpty()){
            for (OrderProduct orderProduct : orderProducts) {
                orderProductDao.updateById(orderProduct);
                result ++;
            }
        }
        return result;
    }
}