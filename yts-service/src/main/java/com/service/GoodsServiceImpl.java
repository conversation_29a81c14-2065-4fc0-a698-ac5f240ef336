package com.service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.common.constant.DataStatus;
import com.dao.GoodsDao;
import com.dao.GoodsSpecificationSkuDao;
import com.dao.GoodsSpecificationTypeDao;
import com.dao.GoodsSpecificationValueDao;
import com.domain.Goods;
import com.domain.GoodsSpecificationSku;
import com.domain.GoodsSpecificationType;
import com.domain.GoodsSpecificationValue;
import com.domain.complex.GoodsQuery;
import com.domain.complex.GoodsSpecificationSkuQuery;
import com.domain.complex.GoodsSpecificationTypeQuery;
import com.domain.complex.GoodsSpecificationValueQuery;

@Service
public class GoodsServiceImpl extends BaseService implements GoodsService {

    @Autowired
    private GoodsDao goodsDao;
    @Autowired
    private GoodsSpecificationTypeDao goodsSpecificationTypeDao;
    @Autowired
    private GoodsSpecificationValueDao goodsSpecificationValueDao;
    @Autowired
    private GoodsSpecificationSkuDao goodsSpecificationSkuDao;

    @Override
    public Integer create(Goods goods) {
        return goodsDao.insert(goods);
    }

    @Override
    @Transactional
    public Integer create(Goods goods, LinkedHashMap<GoodsSpecificationType, List<GoodsSpecificationValue>> goodsSpecificationTypeMap, List<GoodsSpecificationSku> goodsSpecificationSkus) {
        goodsDao.insert(goods);
        if (!this.isEmpty(goodsSpecificationTypeMap) && !goodsSpecificationTypeMap.isEmpty()){
            for (GoodsSpecificationType goodsSpecificationType : goodsSpecificationTypeMap.keySet()) {
                goodsSpecificationType.setGoodsId(goods.getId());
                goodsSpecificationTypeDao.insert(goodsSpecificationType);
                List<GoodsSpecificationValue> goodsSpecificationValues = goodsSpecificationTypeMap.get(goodsSpecificationType);
                if (!this.isEmpty(goodsSpecificationValues) && !goodsSpecificationValues.isEmpty()){
                    for (GoodsSpecificationValue goodsSpecificationValue : goodsSpecificationValues) {
                        goodsSpecificationValue.setGoodsId(goods.getId());
                        goodsSpecificationValue.setGoodsSpecificationTypeId(goodsSpecificationType.getId());
                        goodsSpecificationValueDao.insert(goodsSpecificationValue);
                    }
                }
            }
            if (!this.isEmpty(goodsSpecificationSkus) && !goodsSpecificationSkus.isEmpty()){
                for (GoodsSpecificationSku goodsSpecificationSku : goodsSpecificationSkus) {
                    goodsSpecificationSku.setGoodsId(goods.getId());
//                    //把规格值下标转换成对应的规格值ids
//                    StringBuilder specValuesStr = new StringBuilder();
//                    for (String index : goodsSpecificationSku.getSpecValues().split(App.COMMA)) {
//                        StringBuilder typeValueIdsStr = new StringBuilder();
//                        ArrayList<GoodsSpecificationType> goodsSpecificationTypes = new ArrayList<>(goodsSpecificationTypeMap.keySet());
//                        GoodsSpecificationType goodsSpecificationType = goodsSpecificationTypes.get(Integer.parseInt(index.split(":")[0]));
//                        List<GoodsSpecificationValue> goodsSpecificationValues = goodsSpecificationTypeMap.get(new ArrayList<>(goodsSpecificationTypeMap.keySet()).get(Integer.parseInt(index.split(":")[0])));
//                        GoodsSpecificationValue goodsSpecificationValue = goodsSpecificationValues.get(Integer.parseInt(index.split(":")[1]));
//
//                        typeValueIdsStr.append(goodsSpecificationType.getId()).append(":").append(goodsSpecificationValue.getId());
//                        if (specValuesStr.length() > 0){
//                            specValuesStr.append(App.COMMA);
//                        }
//                        specValuesStr.append(typeValueIdsStr);
//                    }
//                    goodsSpecificationSku.setSpecValues(specValuesStr.toString());
                    goodsSpecificationSkuDao.insert(goodsSpecificationSku);
                }
            }
        }
        return 0;
    }

    @Override
    public Integer modifyById(Goods goods) {
        return goodsDao.updateById(goods);
    }

    @Override
    public Goods findById(Long id) {
        return goodsDao.selectById(id);
    }

    @Override
    public List<Goods> find(GoodsQuery goodsQuery) {
        return goodsDao.select(goodsQuery);
    }

    @Override
    public List<Goods> findAll(GoodsQuery goodsQuery) {
        return goodsDao.selectAll(goodsQuery);
    }

    @Override
    public Integer count(GoodsQuery goodsQuery) {
        return goodsDao.count(goodsQuery);
    }

    @Override
    public List<Goods> findByIds(List<Long> ids) {
        return goodsDao.selectByIds(ids);
    }

    @Override
    public Map<Long, Goods> findMapByIds(List<Long> ids) {
        Map<Long, Goods> goodsMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<Goods> goodss = goodsDao.selectByIds(ids);
            for (Goods goods : goodss) {
                goodsMap.put(goods.getId(),goods);
            }
        }
        return goodsMap;
    }

    @Override
    public Integer createBatch(List<Goods> goodss) {
        return goodsDao.insertBatch(goodss);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<Goods> goodss) {
        Integer result = 0;
        if (!this.isEmpty(goodss) && !goodss.isEmpty()){
            for (Goods goods : goodss) {
                goodsDao.updateById(goods);
                result ++;
            }
        }
        return result;
    }

    @Override
    @Transactional
    public Integer modify(Goods goods, LinkedHashMap<GoodsSpecificationType, List<GoodsSpecificationValue>> goodsSpecificationTypeMap, List<GoodsSpecificationSku> goodsSpecificationSkus) {
        goodsDao.updateById(goods);
        //删除当前商品全部商品规格类型表实体、商品规格值表实体、商品规格sku表实体\
        GoodsSpecificationTypeQuery goodsSpecificationTypeQuery = new GoodsSpecificationTypeQuery();
        goodsSpecificationTypeQuery.setGoodsId(goods.getId());
        List<GoodsSpecificationType> removeGoodsSpecificationTypes = goodsSpecificationTypeDao.selectAll(goodsSpecificationTypeQuery);
        if (!this.isEmpty(removeGoodsSpecificationTypes) && !removeGoodsSpecificationTypes.isEmpty()){
            for (GoodsSpecificationType goodsSpecificationType : removeGoodsSpecificationTypes) {
                goodsSpecificationTypeDao.deleteById(goodsSpecificationType.getId());
            }
        }
        GoodsSpecificationValueQuery goodsSpecificationValueQuery = new GoodsSpecificationValueQuery();
        goodsSpecificationValueQuery.setGoodsId(goods.getId());
        List<GoodsSpecificationValue> removeGoodsSpecificationValues = goodsSpecificationValueDao.selectAll(goodsSpecificationValueQuery);
        if (!this.isEmpty(removeGoodsSpecificationValues) && !removeGoodsSpecificationValues.isEmpty()){
            for (GoodsSpecificationValue goodsSpecificationValue : removeGoodsSpecificationValues) {
                goodsSpecificationValueDao.deleteById(goodsSpecificationValue.getId());
            }
        }
        GoodsSpecificationSkuQuery goodsSpecificationSkuQuery = new GoodsSpecificationSkuQuery();
        goodsSpecificationSkuQuery.setGoodsId(goods.getId());
        List<GoodsSpecificationSku> removeGoodsSpecificationSkus = goodsSpecificationSkuDao.selectAll(goodsSpecificationSkuQuery);
        if (!this.isEmpty(removeGoodsSpecificationSkus) && !removeGoodsSpecificationSkus.isEmpty()){
            for (GoodsSpecificationSku goodsSpecificationSku : removeGoodsSpecificationSkus) {
                goodsSpecificationSkuDao.deleteById(goodsSpecificationSku.getId());
            }
        }
        if (!this.isEmpty(goodsSpecificationTypeMap) && !goodsSpecificationTypeMap.isEmpty()){
            for (GoodsSpecificationType goodsSpecificationType : goodsSpecificationTypeMap.keySet()) {
                if (this.isEmpty(goodsSpecificationType.getId())) {
                    goodsSpecificationType.setCreateTime(goods.getModifyTime());
                    goodsSpecificationType.setModifyTime(goods.getModifyTime());
                    goodsSpecificationType.setStatus(DataStatus.Y.getCode());
                    goodsSpecificationTypeDao.insert(goodsSpecificationType);
                } else {
                    goodsSpecificationTypeDao.updateById(goodsSpecificationType);
                }
                List<GoodsSpecificationValue> goodsSpecificationValues = goodsSpecificationTypeMap.get(goodsSpecificationType);
                if (!this.isEmpty(goodsSpecificationValues) && !goodsSpecificationValues.isEmpty()){
                    for (GoodsSpecificationValue goodsSpecificationValue : goodsSpecificationValues) {
                        goodsSpecificationValue.setGoodsSpecificationTypeId(goodsSpecificationType.getId());
                        if (this.isEmpty(goodsSpecificationValue.getId())) {
                            goodsSpecificationValue.setCreateTime(goods.getModifyTime());
                            goodsSpecificationValue.setModifyTime(goods.getModifyTime());
                            goodsSpecificationValue.setStatus(DataStatus.Y.getCode());
                            goodsSpecificationValueDao.insert(goodsSpecificationValue);
                        } else {
                            goodsSpecificationValueDao.updateById(goodsSpecificationValue);
                        }
                    }
                }
            }
        }
        if (!this.isEmpty(goodsSpecificationSkus) && !goodsSpecificationSkus.isEmpty()){
            for (GoodsSpecificationSku goodsSpecificationSku : goodsSpecificationSkus) {
//                //把规格值下标转换成对应的规格值ids
//                StringBuilder specValuesStr = new StringBuilder();
//                for (String index : goodsSpecificationSku.getSpecValues().split(App.COMMA)) {
//                    StringBuilder typeValueIdsStr = new StringBuilder();
//                    ArrayList<GoodsSpecificationType> goodsSpecificationTypes = new ArrayList<>(goodsSpecificationTypeMap.keySet());
//                    GoodsSpecificationType goodsSpecificationType = goodsSpecificationTypes.get(Integer.parseInt(index.split(":")[0]));
//                    List<GoodsSpecificationValue> goodsSpecificationValues = goodsSpecificationTypeMap.get(new ArrayList<>(goodsSpecificationTypeMap.keySet()).get(Integer.parseInt(index.split(":")[0])));
//                    GoodsSpecificationValue goodsSpecificationValue = goodsSpecificationValues.get(Integer.parseInt(index.split(":")[1]));
//
//                    typeValueIdsStr.append(goodsSpecificationType.getId()).append(":").append(goodsSpecificationValue.getId());
//                    if (specValuesStr.length() > 0){
//                        specValuesStr.append(App.COMMA);
//                    }
//                    specValuesStr.append(typeValueIdsStr);
//                }
//                goodsSpecificationSku.setSpecValues(specValuesStr.toString());
                if (this.isEmpty(goodsSpecificationSku.getId())){
                    goodsSpecificationSku.setCreateTime(goods.getModifyTime());
                    goodsSpecificationSku.setModifyTime(goods.getModifyTime());
                    goodsSpecificationSku.setStatus(DataStatus.Y.getCode());
                    goodsSpecificationSkuDao.insert(goodsSpecificationSku);
                }else {
                    goodsSpecificationSkuDao.updateById(goodsSpecificationSku);
                }
            }
        }
        return 0;
    }

    @Override
    @Transactional
    public Integer remove(Goods goods, List<GoodsSpecificationType> goodsSpecificationTypes, List<GoodsSpecificationValue> goodsSpecificationValues, List<GoodsSpecificationSku> goodsSpecificationSkus) {
        for (GoodsSpecificationType goodsSpecificationType : goodsSpecificationTypes) {
            goodsSpecificationTypeDao.updateById(goodsSpecificationType);
        }
        for (GoodsSpecificationValue goodsSpecificationValue : goodsSpecificationValues) {
            goodsSpecificationValueDao.updateById(goodsSpecificationValue);
        }
        for (GoodsSpecificationSku goodsSpecificationSku : goodsSpecificationSkus) {
            goodsSpecificationSkuDao.updateById(goodsSpecificationSku);
        }
        return goodsDao.updateById(goods);
    }
}