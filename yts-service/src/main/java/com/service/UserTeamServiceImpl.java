package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.UserTeam;
import com.domain.complex.UserTeamQuery;

import com.dao.UserTeamDao;

@Service
public class UserTeamServiceImpl extends BaseService implements UserTeamService {

    @Autowired
    private UserTeamDao userTeamDao;

    @Override
    public Integer create(UserTeam userTeam) {
        return userTeamDao.insert(userTeam);
    }

    @Override
    public Integer modifyById(UserTeam userTeam) {
        return userTeamDao.updateById(userTeam);
    }

    @Override
    public UserTeam findById(Long id) {
        return userTeamDao.selectById(id);
    }

    @Override
    public List<UserTeam> find(UserTeamQuery userTeamQuery) {
        return userTeamDao.select(userTeamQuery);
    }

    @Override
    public List<UserTeam> findAll(UserTeamQuery userTeamQuery) {
        return userTeamDao.selectAll(userTeamQuery);
    }

    @Override
    public Integer count(UserTeamQuery userTeamQuery) {
        return userTeamDao.count(userTeamQuery);
    }

    @Override
    public List<UserTeam> findByIds(List<Long> ids) {
        return userTeamDao.selectByIds(ids);
    }

    @Override
    public Map<Long, UserTeam> findMapByIds(List<Long> ids) {
        Map<Long, UserTeam> userTeamMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserTeam> userTeams = userTeamDao.selectByIds(ids);
            for (UserTeam userTeam : userTeams) {
                userTeamMap.put(userTeam.getId(),userTeam);
            }
        }
        return userTeamMap;
    }

    @Override
    public Integer createBatch(List<UserTeam> userTeams) {
        return userTeamDao.insertBatch(userTeams);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<UserTeam> userTeams) {
        Integer result = 0;
        if (!this.isEmpty(userTeams) && !userTeams.isEmpty()){
            for (UserTeam userTeam : userTeams) {
                userTeamDao.updateById(userTeam);
                result ++;
            }
        }
        return result;
    }
}