package com.service;

import java.util.List;
import java.util.Map;

import com.domain.UserAddress;
import com.domain.complex.UserAddressQuery;
/**
 * 用户地址表业务层
 *
 * @date 2025-07-23 22:58:35
 */
public interface UserAddressService {

    Integer create(UserAddress userAddress);

    Integer modifyById(UserAddress userAddress);

    Integer modifyById(UserAddress userAddress, List<UserAddress> modifyUserAddressList);

    UserAddress findById(Long id);

    List<UserAddress> find(UserAddressQuery userAddressQuery);

    List<UserAddress> findAll(UserAddressQuery userAddressQuery);

    Integer count(UserAddressQuery userAddressQuery);

    List<UserAddress> findByIds(List<Long> ids);

    Map<Long, UserAddress> findMapByIds(List<Long> ids);

    Integer createBatch(List<UserAddress> userAddresss);

    Integer modifyBatch(List<UserAddress> userAddresss);

}

