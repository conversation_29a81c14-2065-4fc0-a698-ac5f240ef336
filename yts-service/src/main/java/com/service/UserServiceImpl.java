package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.User;
import com.domain.complex.UserQuery;

import com.dao.UserDao;

@Service
public class UserServiceImpl extends BaseService implements UserService {

    @Autowired
    private UserDao userDao;

    @Override
    public Integer create(User user) {
        return userDao.insert(user);
    }

    @Override
    public Integer modifyById(User user) {
        return userDao.updateById(user);
    }

    @Override
    public User findById(Long id) {
        return userDao.selectById(id);
    }

    @Override
    public List<User> find(UserQuery userQuery) {
        return userDao.select(userQuery);
    }

    @Override
    public List<User> findAll(UserQuery userQuery) {
        return userDao.selectAll(userQuery);
    }

    @Override
    public Integer count(UserQuery userQuery) {
        return userDao.count(userQuery);
    }

    @Override
    public List<User> findByIds(List<Long> ids) {
        return userDao.selectByIds(ids);
    }

    @Override
    public Map<Long, User> findMapByIds(List<Long> ids) {
        Map<Long, User> userMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<User> users = userDao.selectByIds(ids);
            for (User user : users) {
                userMap.put(user.getId(),user);
            }
        }
        return userMap;
    }

    @Override
    public Integer createBatch(List<User> users) {
        return userDao.insertBatch(users);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<User> users) {
        Integer result = 0;
        if (!this.isEmpty(users) && !users.isEmpty()){
            for (User user : users) {
                userDao.updateById(user);
                result ++;
            }
        }
        return result;
    }

    @Override
    public User findByUnionId(String unionId) {
        return userDao.selectByUnionId(unionId);
    }

    @Override
    public User findByOpenId(String openid) {
        return userDao.selectByOpenId(openid);
    }

}