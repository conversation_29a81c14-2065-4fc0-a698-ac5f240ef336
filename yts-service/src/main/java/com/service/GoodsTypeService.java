package com.service;

import java.util.List;
import java.util.Map;

import com.domain.GoodsType;
import com.domain.complex.GoodsTypeQuery;
/**
 * 商品分类表业务层
 *
 * @date 2025-07-20 10:32:17
 */
public interface GoodsTypeService {

    Integer create(GoodsType goodsType);

    Integer modifyById(GoodsType goodsType);

    GoodsType findById(Long id);

    List<GoodsType> find(GoodsTypeQuery goodsTypeQuery);

    List<GoodsType> findAll(GoodsTypeQuery goodsTypeQuery);

    Integer count(GoodsTypeQuery goodsTypeQuery);

    List<GoodsType> findByIds(List<Long> ids);

    Map<Long, GoodsType> findMapByIds(List<Long> ids);

    Integer createBatch(List<GoodsType> goodsTypes);

    Integer modifyBatch(List<GoodsType> goodsTypes);

}

