package com.service;

import java.util.List;
import java.util.Map;

import com.domain.UserCommissionCashFlow;
import com.domain.complex.UserCommissionCashFlowQuery;
/**
 * 用户佣金提现明细表业务层
 *
 * @date 2025-07-19 10:58:27
 */
public interface UserCommissionCashFlowService {

    Integer create(UserCommissionCashFlow userCommissionCashFlow);

    Integer modifyById(UserCommissionCashFlow userCommissionCashFlow);

    UserCommissionCashFlow findById(Long id);

    List<UserCommissionCashFlow> find(UserCommissionCashFlowQuery userCommissionCashFlowQuery);

    List<UserCommissionCashFlow> findAll(UserCommissionCashFlowQuery userCommissionCashFlowQuery);

    Integer count(UserCommissionCashFlowQuery userCommissionCashFlowQuery);

    List<UserCommissionCashFlow> findByIds(List<Long> ids);

    Map<Long, UserCommissionCashFlow> findMapByIds(List<Long> ids);

    Integer createBatch(List<UserCommissionCashFlow> userCommissionCashFlows);

    Integer modifyBatch(List<UserCommissionCashFlow> userCommissionCashFlows);

}

