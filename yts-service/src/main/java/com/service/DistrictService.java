package com.service;

import java.util.List;
import java.util.Map;

import com.domain.District;
import com.domain.complex.DistrictQuery;
/**
 * 区(县)表业务层
 *
 * @date 2025-07-23 22:58:35
 */
public interface DistrictService {

    Integer create(District district);

    Integer modifyById(District district);

    District findById(Long id);

    List<District> find(DistrictQuery districtQuery);

    List<District> findAll(DistrictQuery districtQuery);

    Integer count(DistrictQuery districtQuery);

    List<District> findByIds(List<Long> ids);

    Map<Long, District> findMapByIds(List<Long> ids);

    Integer createBatch(List<District> districts);

    Integer modifyBatch(List<District> districts);

}

