package com.service;

import java.util.List;
import java.util.Map;

import com.domain.Banner;
import com.domain.complex.BannerQuery;
/**
 * 轮播图表业务层
 *
 * @date 2025-07-19 10:58:28
 */
public interface BannerService {

    Integer create(Banner banner);

    Integer modifyById(Banner banner);

    Banner findById(Long id);

    List<Banner> find(BannerQuery bannerQuery);

    List<Banner> findAll(BannerQuery bannerQuery);

    Integer count(BannerQuery bannerQuery);

    List<Banner> findByIds(List<Long> ids);

    Map<Long, Banner> findMapByIds(List<Long> ids);

    Integer createBatch(List<Banner> banners);

    Integer modifyBatch(List<Banner> banners);

}

