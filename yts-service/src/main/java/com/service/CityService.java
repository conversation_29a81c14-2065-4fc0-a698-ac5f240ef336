package com.service;

import java.util.List;
import java.util.Map;

import com.domain.City;
import com.domain.complex.CityQuery;
/**
 * 城市表业务层
 *
 * @date 2025-07-23 22:58:35
 */
public interface CityService {

    Integer create(City city);

    Integer modifyById(City city);

    City findById(Long id);

    List<City> find(CityQuery cityQuery);

    List<City> findAll(CityQuery cityQuery);

    Integer count(CityQuery cityQuery);

    List<City> findByIds(List<Long> ids);

    Map<Long, City> findMapByIds(List<Long> ids);

    Integer createBatch(List<City> citys);

    Integer modifyBatch(List<City> citys);

}

