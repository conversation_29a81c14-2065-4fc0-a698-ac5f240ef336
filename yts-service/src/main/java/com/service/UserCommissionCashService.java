package com.service;

import java.util.List;
import java.util.Map;

import com.domain.UserCommissionCash;
import com.domain.complex.UserCommissionCashQuery;
/**
 * 用户佣金提现表业务层
 *
 * @date 2025-07-19 10:58:27
 */
public interface UserCommissionCashService {

    Integer create(UserCommissionCash userCommissionCash);

    Integer modifyById(UserCommissionCash userCommissionCash);

    UserCommissionCash findById(Long id);

    List<UserCommissionCash> find(UserCommissionCashQuery userCommissionCashQuery);

    List<UserCommissionCash> findAll(UserCommissionCashQuery userCommissionCashQuery);

    Integer count(UserCommissionCashQuery userCommissionCashQuery);

    List<UserCommissionCash> findByIds(List<Long> ids);

    Map<Long, UserCommissionCash> findMapByIds(List<Long> ids);

    Integer createBatch(List<UserCommissionCash> userCommissionCashs);

    Integer modifyBatch(List<UserCommissionCash> userCommissionCashs);

}

