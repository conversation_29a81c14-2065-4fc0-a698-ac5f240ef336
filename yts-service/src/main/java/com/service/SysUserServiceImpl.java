package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.SysUser;
import com.domain.complex.SysUserQuery;

import com.dao.SysUserDao;

@Service
public class SysUserServiceImpl extends BaseService implements SysUserService {

    @Autowired
    private SysUserDao sysUserDao;

    @Override
    public Integer create(SysUser sysUser) {
        return sysUserDao.insert(sysUser);
    }

    @Override
    public Integer modifyById(SysUser sysUser) {
        return sysUserDao.updateById(sysUser);
    }

    @Override
    public SysUser findById(Long id) {
        return sysUserDao.selectById(id);
    }

    @Override
    public List<SysUser> find(SysUserQuery sysUserQuery) {
        return sysUserDao.select(sysUserQuery);
    }

    @Override
    public List<SysUser> findAll(SysUserQuery sysUserQuery) {
        return sysUserDao.selectAll(sysUserQuery);
    }

    @Override
    public Integer count(SysUserQuery sysUserQuery) {
        return sysUserDao.count(sysUserQuery);
    }

    @Override
    public List<SysUser> findByIds(List<Long> ids) {
        return sysUserDao.selectByIds(ids);
    }

    @Override
    public Map<Long, SysUser> findMapByIds(List<Long> ids) {
        Map<Long, SysUser> sysUserMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<SysUser> sysUsers = sysUserDao.selectByIds(ids);
            for (SysUser sysUser : sysUsers) {
                sysUserMap.put(sysUser.getId(),sysUser);
            }
        }
        return sysUserMap;
    }

    @Override
    public Integer createBatch(List<SysUser> sysUsers) {
        return sysUserDao.insertBatch(sysUsers);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<SysUser> sysUsers) {
        Integer result = 0;
        if (!this.isEmpty(sysUsers) && !sysUsers.isEmpty()){
            for (SysUser sysUser : sysUsers) {
                sysUserDao.updateById(sysUser);
                result ++;
            }
        }
        return result;
    }
}