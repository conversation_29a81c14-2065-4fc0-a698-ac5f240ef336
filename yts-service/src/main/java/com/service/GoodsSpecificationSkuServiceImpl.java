package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.GoodsSpecificationSku;
import com.domain.complex.GoodsSpecificationSkuQuery;

import com.dao.GoodsSpecificationSkuDao;

@Service
public class GoodsSpecificationSkuServiceImpl extends BaseService implements GoodsSpecificationSkuService {

    @Autowired
    private GoodsSpecificationSkuDao goodsSpecificationSkuDao;

    @Override
    public Integer create(GoodsSpecificationSku goodsSpecificationSku) {
        return goodsSpecificationSkuDao.insert(goodsSpecificationSku);
    }

    @Override
    public Integer modifyById(GoodsSpecificationSku goodsSpecificationSku) {
        return goodsSpecificationSkuDao.updateById(goodsSpecificationSku);
    }

    @Override
    public GoodsSpecificationSku findById(Long id) {
        return goodsSpecificationSkuDao.selectById(id);
    }

    @Override
    public GoodsSpecificationSku findBySpecValuesAndGoodsId(String specValues , Long goodsId) {
        return goodsSpecificationSkuDao.findBySpecValuesAndGoodsId(specValues, goodsId);
    }

    @Override
    public List<GoodsSpecificationSku> find(GoodsSpecificationSkuQuery goodsSpecificationSkuQuery) {
        return goodsSpecificationSkuDao.select(goodsSpecificationSkuQuery);
    }

    @Override
    public List<GoodsSpecificationSku> findAll(GoodsSpecificationSkuQuery goodsSpecificationSkuQuery) {
        return goodsSpecificationSkuDao.selectAll(goodsSpecificationSkuQuery);
    }

    @Override
    public Integer count(GoodsSpecificationSkuQuery goodsSpecificationSkuQuery) {
        return goodsSpecificationSkuDao.count(goodsSpecificationSkuQuery);
    }

    @Override
    public List<GoodsSpecificationSku> findByIds(List<Long> ids) {
        return goodsSpecificationSkuDao.selectByIds(ids);
    }

    @Override
    public Map<Long, GoodsSpecificationSku> findMapByIds(List<Long> ids) {
        Map<Long, GoodsSpecificationSku> goodsSpecificationSkuMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<GoodsSpecificationSku> goodsSpecificationSkus = goodsSpecificationSkuDao.selectByIds(ids);
            for (GoodsSpecificationSku goodsSpecificationSku : goodsSpecificationSkus) {
                goodsSpecificationSkuMap.put(goodsSpecificationSku.getId(),goodsSpecificationSku);
            }
        }
        return goodsSpecificationSkuMap;
    }

    @Override
    public Integer createBatch(List<GoodsSpecificationSku> goodsSpecificationSkus) {
        return goodsSpecificationSkuDao.insertBatch(goodsSpecificationSkus);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<GoodsSpecificationSku> goodsSpecificationSkus) {
        Integer result = 0;
        if (!this.isEmpty(goodsSpecificationSkus) && !goodsSpecificationSkus.isEmpty()){
            for (GoodsSpecificationSku goodsSpecificationSku : goodsSpecificationSkus) {
                goodsSpecificationSkuDao.updateById(goodsSpecificationSku);
                result ++;
            }
        }
        return result;
    }
}