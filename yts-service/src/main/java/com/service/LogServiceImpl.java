package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.LogDao;
import com.domain.Log;
import com.domain.complex.LogQuery;

@Service
public class LogServiceImpl extends BaseService implements LogService {

    @Autowired
    private LogDao logDao;

    @Override
    public Integer create(Log log) {
        return logDao.insert(log);
    }

    @Override
    public Integer modifyById(Log log) {
        return logDao.updateById(log);
    }

    @Override
    public Log findById(Long id) {
        return logDao.selectById(id);
    }

    @Override
    public List<Log> find(LogQuery logQuery) {
        return logDao.select(logQuery);
    }

    @Override
    public List<Log> findAll(LogQuery logQuery) {
        return logDao.selectAll(logQuery);
    }

    @Override
    public Integer count(LogQuery logQuery) {
        return logDao.count(logQuery);
    }

    @Override
    public List<Log> findByIds(List<Long> ids) {
        return logDao.selectByIds(ids);
    }

    @Override
    public Map<Long, Log> findMapByIds(List<Long> ids) {
        Map<Long, Log> logMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<Log> logs = logDao.selectByIds(ids);
            for (Log log : logs) {
                logMap.put(log.getId(),log);
            }
        }
        return logMap;
    }

    @Override
    public Integer createBatch(List<Log> logs) {
        return logDao.insertBatch(logs);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<Log> logs) {
        Integer result = 0;
        if (!this.isEmpty(logs) && !logs.isEmpty()){
            for (Log log : logs) {
                logDao.updateById(log);
                result ++;
            }
        }
        return result;
    }

    @Override
    public Integer removeById(Long id){
        return logDao.deleteById(id);
    }
}