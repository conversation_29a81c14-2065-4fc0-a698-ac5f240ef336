package com.service;

import java.util.List;
import java.util.Map;

import com.domain.ServiceInfo;
import com.domain.complex.ServiceInfoQuery;
/**
 * 服务信息表业务层
 *
 * @date 2025-07-19 10:58:27
 */
public interface ServiceInfoService {

    Integer create(ServiceInfo serviceInfo);

    Integer modifyById(ServiceInfo serviceInfo);

    ServiceInfo findById(Long id);

    List<ServiceInfo> find(ServiceInfoQuery serviceInfoQuery);

    List<ServiceInfo> findAll(ServiceInfoQuery serviceInfoQuery);

    Integer count(ServiceInfoQuery serviceInfoQuery);

    List<ServiceInfo> findByIds(List<Long> ids);

    Map<Long, ServiceInfo> findMapByIds(List<Long> ids);

    Integer createBatch(List<ServiceInfo> serviceInfos);

    Integer modifyBatch(List<ServiceInfo> serviceInfos);

}

