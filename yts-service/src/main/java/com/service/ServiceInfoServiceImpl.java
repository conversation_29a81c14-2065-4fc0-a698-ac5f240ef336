package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.ServiceInfo;
import com.domain.complex.ServiceInfoQuery;

import com.dao.ServiceInfoDao;

@Service
public class ServiceInfoServiceImpl extends BaseService implements ServiceInfoService {

    @Autowired
    private ServiceInfoDao serviceInfoDao;

    @Override
    public Integer create(ServiceInfo serviceInfo) {
        return serviceInfoDao.insert(serviceInfo);
    }

    @Override
    public Integer modifyById(ServiceInfo serviceInfo) {
        return serviceInfoDao.updateById(serviceInfo);
    }

    @Override
    public ServiceInfo findById(Long id) {
        return serviceInfoDao.selectById(id);
    }

    @Override
    public List<ServiceInfo> find(ServiceInfoQuery serviceInfoQuery) {
        return serviceInfoDao.select(serviceInfoQuery);
    }

    @Override
    public List<ServiceInfo> findAll(ServiceInfoQuery serviceInfoQuery) {
        return serviceInfoDao.selectAll(serviceInfoQuery);
    }

    @Override
    public Integer count(ServiceInfoQuery serviceInfoQuery) {
        return serviceInfoDao.count(serviceInfoQuery);
    }

    @Override
    public List<ServiceInfo> findByIds(List<Long> ids) {
        return serviceInfoDao.selectByIds(ids);
    }

    @Override
    public Map<Long, ServiceInfo> findMapByIds(List<Long> ids) {
        Map<Long, ServiceInfo> serviceInfoMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<ServiceInfo> serviceInfos = serviceInfoDao.selectByIds(ids);
            for (ServiceInfo serviceInfo : serviceInfos) {
                serviceInfoMap.put(serviceInfo.getId(),serviceInfo);
            }
        }
        return serviceInfoMap;
    }

    @Override
    public Integer createBatch(List<ServiceInfo> serviceInfos) {
        return serviceInfoDao.insertBatch(serviceInfos);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<ServiceInfo> serviceInfos) {
        Integer result = 0;
        if (!this.isEmpty(serviceInfos) && !serviceInfos.isEmpty()){
            for (ServiceInfo serviceInfo : serviceInfos) {
                serviceInfoDao.updateById(serviceInfo);
                result ++;
            }
        }
        return result;
    }
}