package com.service;

import java.util.List;
import java.util.Map;

import com.domain.CourseAppointment;
import com.domain.complex.CourseAppointmentQuery;
/**
 * 课程预约表业务层
 *
 * @date 2025-07-19 10:58:28
 */
public interface CourseAppointmentService {

    Integer create(CourseAppointment courseAppointment);

    Integer modifyById(CourseAppointment courseAppointment);

    CourseAppointment findById(Long id);

    List<CourseAppointment> find(CourseAppointmentQuery courseAppointmentQuery);

    List<CourseAppointment> findAll(CourseAppointmentQuery courseAppointmentQuery);

    Integer count(CourseAppointmentQuery courseAppointmentQuery);

    List<CourseAppointment> findByIds(List<Long> ids);

    Map<Long, CourseAppointment> findMapByIds(List<Long> ids);

    Integer createBatch(List<CourseAppointment> courseAppointments);

    Integer modifyBatch(List<CourseAppointment> courseAppointments);

}

