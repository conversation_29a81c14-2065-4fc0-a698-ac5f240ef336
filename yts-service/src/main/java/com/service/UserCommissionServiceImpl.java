package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.UserCommissionFlowDao;
import com.domain.UserCommission;
import com.domain.UserCommissionFlow;
import com.domain.complex.UserCommissionQuery;

import com.dao.UserCommissionDao;

@Service
public class UserCommissionServiceImpl extends BaseService implements UserCommissionService {

    @Autowired
    private UserCommissionDao userCommissionDao;
    @Autowired
    private UserCommissionFlowDao userCommissionFlowDao;


    @Override
    public Integer create(UserCommission userCommission) {
        return userCommissionDao.insert(userCommission);
    }

    @Override
    public Integer modifyById(UserCommission userCommission) {
        return userCommissionDao.updateById(userCommission);
    }

    @Override
    public UserCommission findById(Long id) {
        return userCommissionDao.selectById(id);
    }

    @Override
    public UserCommission findByUserId(Long userId) {
        return userCommissionDao.selectByUserId(userId);
    }

    @Override
    public List<UserCommission> find(UserCommissionQuery userCommissionQuery) {
        return userCommissionDao.select(userCommissionQuery);
    }

    @Override
    public List<UserCommission> findAll(UserCommissionQuery userCommissionQuery) {
        return userCommissionDao.selectAll(userCommissionQuery);
    }

    @Override
    public Integer count(UserCommissionQuery userCommissionQuery) {
        return userCommissionDao.count(userCommissionQuery);
    }

    @Override
    public List<UserCommission> findByIds(List<Long> ids) {
        return userCommissionDao.selectByIds(ids);
    }

    @Override
    public Map<Long, UserCommission> findMapByIds(List<Long> ids) {
        Map<Long, UserCommission> userCommissionMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserCommission> userCommissions = userCommissionDao.selectByIds(ids);
            for (UserCommission userCommission : userCommissions) {
                userCommissionMap.put(userCommission.getId(),userCommission);
            }
        }
        return userCommissionMap;
    }

    @Override
    public Integer createBatch(List<UserCommission> userCommissions) {
        return userCommissionDao.insertBatch(userCommissions);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<UserCommission> userCommissions) {
        Integer result = 0;
        if (!this.isEmpty(userCommissions) && !userCommissions.isEmpty()){
            for (UserCommission userCommission : userCommissions) {
                userCommissionDao.updateById(userCommission);
                result ++;
            }
        }
        return result;
    }

    @Override
    @Transactional
    public Integer save(UserCommission userCommission, UserCommissionFlow userCommissionFlow) {
        if (userCommission != null){
            if (this.isEmpty(userCommission.getId())) {
                userCommissionDao.insert(userCommission);
            }else {
                userCommissionDao.updateById(userCommission);
            }
        }
        if (userCommissionFlow != null){
            userCommissionFlowDao.insert(userCommissionFlow);
        }
        return 0;
    }
}