package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.UserCommissionCash;
import com.domain.complex.UserCommissionCashQuery;

import com.dao.UserCommissionCashDao;

@Service
public class UserCommissionCashServiceImpl extends BaseService implements UserCommissionCashService {

    @Autowired
    private UserCommissionCashDao userCommissionCashDao;

    @Override
    public Integer create(UserCommissionCash userCommissionCash) {
        return userCommissionCashDao.insert(userCommissionCash);
    }

    @Override
    public Integer modifyById(UserCommissionCash userCommissionCash) {
        return userCommissionCashDao.updateById(userCommissionCash);
    }

    @Override
    public UserCommissionCash findById(Long id) {
        return userCommissionCashDao.selectById(id);
    }

    @Override
    public List<UserCommissionCash> find(UserCommissionCashQuery userCommissionCashQuery) {
        return userCommissionCashDao.select(userCommissionCashQuery);
    }

    @Override
    public List<UserCommissionCash> findAll(UserCommissionCashQuery userCommissionCashQuery) {
        return userCommissionCashDao.selectAll(userCommissionCashQuery);
    }

    @Override
    public Integer count(UserCommissionCashQuery userCommissionCashQuery) {
        return userCommissionCashDao.count(userCommissionCashQuery);
    }

    @Override
    public List<UserCommissionCash> findByIds(List<Long> ids) {
        return userCommissionCashDao.selectByIds(ids);
    }

    @Override
    public Map<Long, UserCommissionCash> findMapByIds(List<Long> ids) {
        Map<Long, UserCommissionCash> userCommissionCashMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserCommissionCash> userCommissionCashs = userCommissionCashDao.selectByIds(ids);
            for (UserCommissionCash userCommissionCash : userCommissionCashs) {
                userCommissionCashMap.put(userCommissionCash.getId(),userCommissionCash);
            }
        }
        return userCommissionCashMap;
    }

    @Override
    public Integer createBatch(List<UserCommissionCash> userCommissionCashs) {
        return userCommissionCashDao.insertBatch(userCommissionCashs);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<UserCommissionCash> userCommissionCashs) {
        Integer result = 0;
        if (!this.isEmpty(userCommissionCashs) && !userCommissionCashs.isEmpty()){
            for (UserCommissionCash userCommissionCash : userCommissionCashs) {
                userCommissionCashDao.updateById(userCommissionCash);
                result ++;
            }
        }
        return result;
    }
}