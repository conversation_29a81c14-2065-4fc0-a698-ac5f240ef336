package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.UserCommissionCashDao;
import com.dao.UserCommissionCashFlowDao;
import com.dao.UserCommissionDao;
import com.domain.UserCommission;
import com.domain.UserCommissionCash;
import com.domain.UserCommissionCashFlow;
import com.domain.UserCommissionFlow;
import com.domain.complex.UserCommissionFlowQuery;

import com.dao.UserCommissionFlowDao;

@Service
public class UserCommissionFlowServiceImpl extends BaseService implements UserCommissionFlowService {

    @Autowired
    private UserCommissionFlowDao userCommissionFlowDao;
    @Autowired
    private UserCommissionDao userCommissionDao;
    @Autowired
    private UserCommissionCashDao userCommissionCashDao;
    @Autowired
    private UserCommissionCashFlowDao userCommissionCashFlowDao;

    @Override
    public Integer create(UserCommissionFlow userCommissionFlow) {
        return userCommissionFlowDao.insert(userCommissionFlow);
    }

    @Override
    public Integer modifyById(UserCommissionFlow userCommissionFlow) {
        return userCommissionFlowDao.updateById(userCommissionFlow);
    }

    @Override
    public UserCommissionFlow findById(Long id) {
        return userCommissionFlowDao.selectById(id);
    }

    @Override
    public List<UserCommissionFlow> find(UserCommissionFlowQuery userCommissionFlowQuery) {
        return userCommissionFlowDao.select(userCommissionFlowQuery);
    }

    @Override
    public List<UserCommissionFlow> findAll(UserCommissionFlowQuery userCommissionFlowQuery) {
        return userCommissionFlowDao.selectAll(userCommissionFlowQuery);
    }

    @Override
    public Integer count(UserCommissionFlowQuery userCommissionFlowQuery) {
        return userCommissionFlowDao.count(userCommissionFlowQuery);
    }

    @Override
    public List<UserCommissionFlow> findByIds(List<Long> ids) {
        return userCommissionFlowDao.selectByIds(ids);
    }

    @Override
    public Map<Long, UserCommissionFlow> findMapByIds(List<Long> ids) {
        Map<Long, UserCommissionFlow> userCommissionFlowMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserCommissionFlow> userCommissionFlows = userCommissionFlowDao.selectByIds(ids);
            for (UserCommissionFlow userCommissionFlow : userCommissionFlows) {
                userCommissionFlowMap.put(userCommissionFlow.getId(),userCommissionFlow);
            }
        }
        return userCommissionFlowMap;
    }

    @Override
    public Integer createBatch(List<UserCommissionFlow> userCommissionFlows) {
        return userCommissionFlowDao.insertBatch(userCommissionFlows);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<UserCommissionFlow> userCommissionFlows) {
        Integer result = 0;
        if (!this.isEmpty(userCommissionFlows) && !userCommissionFlows.isEmpty()){
            for (UserCommissionFlow userCommissionFlow : userCommissionFlows) {
                userCommissionFlowDao.updateById(userCommissionFlow);
                result ++;
            }
        }
        return result;
    }

    @Override
    @Transactional
    public Integer create(UserCommissionFlow commissionFlow, UserCommission updateCommission, UserCommissionCash userCommissionCash, List<UserCommissionCashFlow> userCommissionCashFlowList) {
        userCommissionDao.updateById(updateCommission);
        userCommissionCashDao.insert(userCommissionCash);
        if (!this.isEmpty(userCommissionCashFlowList) && !userCommissionCashFlowList.isEmpty()){
            userCommissionCashFlowDao.insertBatch(userCommissionCashFlowList);
        }
        return userCommissionFlowDao.insert(commissionFlow);
    }

    @Override
    @Transactional
    public void createBatch(List<UserCommissionFlow> commissionFlows, List<UserCommission> updateCommissions, List<UserCommissionCash> userCommissionCashes, List<UserCommissionCashFlow> userCommissionCashFlows) {
        if (!this.isEmpty(updateCommissions) && !updateCommissions.isEmpty()){
            for (UserCommission updateCommission : updateCommissions) {
                userCommissionDao.updateById(updateCommission);
            }
        }
        if (!this.isEmpty(userCommissionCashes) && !userCommissionCashes.isEmpty()) {
            userCommissionCashDao.insertBatch(userCommissionCashes);
        }
        if (!this.isEmpty(userCommissionCashFlows) && !userCommissionCashFlows.isEmpty()) {
            userCommissionCashFlowDao.insertBatch(userCommissionCashFlows);
        }
        if (!this.isEmpty(commissionFlows) && !commissionFlows.isEmpty()) {
            userCommissionFlowDao.insertBatch(commissionFlows);
        }
    }
}