package com.service;

import java.util.List;
import java.util.Map;

import com.domain.ProductBrowse;
import com.domain.complex.ProductBrowseQuery;
/**
 * 产品浏览表业务层
 *
 * @date 2025-07-20 16:36:02
 */
public interface ProductBrowseService {

    Integer create(ProductBrowse productBrowse);

    Integer modifyById(ProductBrowse productBrowse);

    ProductBrowse findById(Long id);

    List<ProductBrowse> find(ProductBrowseQuery productBrowseQuery);

    List<ProductBrowse> findAll(ProductBrowseQuery productBrowseQuery);

    Integer count(ProductBrowseQuery productBrowseQuery);

    List<ProductBrowse> findByIds(List<Long> ids);

    Map<Long, ProductBrowse> findMapByIds(List<Long> ids);

    Integer createBatch(List<ProductBrowse> productBrowses);

    Integer modifyBatch(List<ProductBrowse> productBrowses);

}

