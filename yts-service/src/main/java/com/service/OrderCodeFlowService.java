package com.service;

import java.util.List;
import java.util.Map;

import com.domain.OrderCodeFlow;
import com.domain.complex.OrderCodeFlowQuery;
/**
 * 订单号流水表业务层
 *
 * @date 2025-07-22 23:08:11
 */
public interface OrderCodeFlowService {

    Integer create(OrderCodeFlow orderCodeFlow);

    Integer modifyById(OrderCodeFlow orderCodeFlow);

    OrderCodeFlow findById(Long id);

    List<OrderCodeFlow> find(OrderCodeFlowQuery orderCodeFlowQuery);

    List<OrderCodeFlow> findAll(OrderCodeFlowQuery orderCodeFlowQuery);

    Integer count(OrderCodeFlowQuery orderCodeFlowQuery);

    List<OrderCodeFlow> findByIds(List<Long> ids);

    Map<Long, OrderCodeFlow> findMapByIds(List<Long> ids);

    Integer createBatch(List<OrderCodeFlow> orderCodeFlows);

    Integer modifyBatch(List<OrderCodeFlow> orderCodeFlows);

}

