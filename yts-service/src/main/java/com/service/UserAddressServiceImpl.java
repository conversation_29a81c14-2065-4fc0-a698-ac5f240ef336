package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.UserAddress;
import com.domain.complex.UserAddressQuery;

import com.dao.UserAddressDao;

@Service
public class UserAddressServiceImpl extends BaseService implements UserAddressService {

    @Autowired
    private UserAddressDao userAddressDao;

    @Override
    public Integer create(UserAddress userAddress) {
        return userAddressDao.insert(userAddress);
    }

    @Override
    public Integer modifyById(UserAddress userAddress) {
        return userAddressDao.updateById(userAddress);
    }

    @Override
    public Integer modifyById(UserAddress userAddress, List<UserAddress> modifyUserAddressList) {
        if (!this.isEmpty(modifyUserAddressList) && !modifyUserAddressList.isEmpty()) {
            for (UserAddress modifyUserAddress : modifyUserAddressList) {
                userAddressDao.updateById(modifyUserAddress);
            }
        }
        return userAddressDao.updateById(userAddress);
    }

    @Override
    public UserAddress findById(Long id) {
        return userAddressDao.selectById(id);
    }

    @Override
    public List<UserAddress> find(UserAddressQuery userAddressQuery) {
        return userAddressDao.select(userAddressQuery);
    }

    @Override
    public List<UserAddress> findAll(UserAddressQuery userAddressQuery) {
        return userAddressDao.selectAll(userAddressQuery);
    }

    @Override
    public Integer count(UserAddressQuery userAddressQuery) {
        return userAddressDao.count(userAddressQuery);
    }

    @Override
    public List<UserAddress> findByIds(List<Long> ids) {
        return userAddressDao.selectByIds(ids);
    }

    @Override
    public Map<Long, UserAddress> findMapByIds(List<Long> ids) {
        Map<Long, UserAddress> userAddressMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserAddress> userAddresss = userAddressDao.selectByIds(ids);
            for (UserAddress userAddress : userAddresss) {
                userAddressMap.put(userAddress.getId(),userAddress);
            }
        }
        return userAddressMap;
    }

    @Override
    public Integer createBatch(List<UserAddress> userAddresss) {
        return userAddressDao.insertBatch(userAddresss);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<UserAddress> userAddresss) {
        Integer result = 0;
        if (!this.isEmpty(userAddresss) && !userAddresss.isEmpty()){
            for (UserAddress userAddress : userAddresss) {
                userAddressDao.updateById(userAddress);
                result ++;
            }
        }
        return result;
    }
}