package com.service;

import java.util.List;
import java.util.Map;

import com.domain.Sequence;
import com.domain.complex.SequenceQuery;
/**
 * 序列表业务层
 *
 * @date 2025-07-16 21:29:32
 */
public interface SequenceService {

    Integer create(Sequence sequence);

    Integer modifyById(Sequence sequence);

    Sequence findById(Long id);

    List<Sequence> find(SequenceQuery sequenceQuery);

    List<Sequence> findAll(SequenceQuery sequenceQuery);

    Integer count(SequenceQuery sequenceQuery);

    List<Sequence> findByIds(List<Long> ids);

    Map<Long, Sequence> findMapByIds(List<Long> ids);

    Integer createBatch(List<Sequence> sequences);

    Integer modifyBatch(List<Sequence> sequences);

}

