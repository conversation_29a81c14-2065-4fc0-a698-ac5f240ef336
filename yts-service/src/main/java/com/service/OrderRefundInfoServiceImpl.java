package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.common.util.DateUtil;
import com.dao.OrderDao;
import com.dao.OrderRefundInfoDao;
import com.domain.OrderRefundInfo;
import com.domain.complex.OrderRefundInfoQuery;
import com.fasterxml.jackson.core.type.TypeReference;

@Service
public class OrderRefundInfoServiceImpl extends BaseService implements OrderRefundInfoService {

    @Autowired
    private OrderRefundInfoDao orderRefundInfoDao;
    @Autowired
    private OrderDao orderDao;

    @Override
    public Integer create(OrderRefundInfo orderRefundInfo) {
        return orderRefundInfoDao.insert(orderRefundInfo);
    }

    @Override
    public Integer modifyById(OrderRefundInfo orderRefundInfo) {
        return orderRefundInfoDao.updateById(orderRefundInfo);
    }

    @Override
    public OrderRefundInfo findById(Long id) {
        return orderRefundInfoDao.selectById(id);
    }

    @Override
    public List<OrderRefundInfo> find(OrderRefundInfoQuery orderRefundInfoQuery) {
        return orderRefundInfoDao.select(orderRefundInfoQuery);
    }

    @Override
    public List<OrderRefundInfo> findAll(OrderRefundInfoQuery orderRefundInfoQuery) {
        return orderRefundInfoDao.selectAll(orderRefundInfoQuery);
    }

    @Override
    public Integer count(OrderRefundInfoQuery orderRefundInfoQuery) {
        return orderRefundInfoDao.count(orderRefundInfoQuery);
    }

    @Override
    public List<OrderRefundInfo> findByIds(List<Long> ids) {
        return orderRefundInfoDao.selectByIds(ids);
    }

    @Override
    public Map<Long, OrderRefundInfo> findMapByIds(List<Long> ids) {
        Map<Long, OrderRefundInfo> orderRefundInfoMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<OrderRefundInfo> orderRefundInfos = orderRefundInfoDao.selectByIds(ids);
            for (OrderRefundInfo orderRefundInfo : orderRefundInfos) {
                orderRefundInfoMap.put(orderRefundInfo.getId(),orderRefundInfo);
            }
        }
        return orderRefundInfoMap;
    }

    @Override
    public Integer createBatch(List<OrderRefundInfo> orderRefundInfos) {
        return orderRefundInfoDao.insertBatch(orderRefundInfos);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<OrderRefundInfo> orderRefundInfos) {
        Integer result = 0;
        if (!this.isEmpty(orderRefundInfos) && !orderRefundInfos.isEmpty()){
            for (OrderRefundInfo orderRefundInfo : orderRefundInfos) {
                orderRefundInfoDao.updateById(orderRefundInfo);
                result ++;
            }
        }
        return result;
    }

    @Override
    public void updateRefund(String bodyAsString) {
        Map<String, String> resultMap = this.getObject(bodyAsString, new TypeReference<HashMap<String, String>>(){});
        // 根据退款单编号修改退款单
        String refundNo = resultMap.get("out_refund_no");
        OrderRefundInfoQuery orderRefundInfoQuery = new OrderRefundInfoQuery();
        orderRefundInfoQuery.setRefundNo(refundNo);
        List<OrderRefundInfo> orderRefundInfos = orderRefundInfoDao.selectAll(orderRefundInfoQuery);
        if (orderRefundInfos == null || orderRefundInfos.isEmpty()){
            return;
        }

        // 设置要修改的字段
        OrderRefundInfo refundInfo = new OrderRefundInfo();
        refundInfo.setId(orderRefundInfos.get(0).getId());
        refundInfo.setRefundId(resultMap.get("refund_id"));//微信支付退款单号

        // 查询退款或申请退款中的返回参数
        if (resultMap.get("status") != null) {
            refundInfo.setRefundStatus(resultMap.get("status"));
            refundInfo.setContentReturn(bodyAsString);
        }

        // 退款回调中的回调参数
        if(resultMap.get("refund_status") != null){
            refundInfo.setRefundStatus(resultMap.get("refund_status"));//退款状态
            refundInfo.setContentNotify(bodyAsString);//将全部响应结果存入数据库的content字段
        }
        refundInfo.setModifyTime(DateUtil.getServerTime());

        //更新退款单
        orderRefundInfoDao.updateById(refundInfo);
    }
}