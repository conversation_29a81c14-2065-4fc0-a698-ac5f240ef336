package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.ReportGoodsDao;
import com.domain.ReportGoods;
import com.domain.complex.ReportGoodsQuery;

@Service
public class ReportGoodsServiceImpl extends BaseService implements ReportGoodsService {

    @Autowired
    private ReportGoodsDao reportGoodsDao;

    @Override
    public Integer create(ReportGoods reportGoods) {
        return reportGoodsDao.insert(reportGoods);
    }

    @Override
    public Integer modifyById(ReportGoods reportGoods) {
        return reportGoodsDao.updateById(reportGoods);
    }

    @Override
    public ReportGoods findById(Long id) {
        return reportGoodsDao.selectById(id);
    }

    @Override
    public List<ReportGoods> find(ReportGoodsQuery reportGoodsQuery) {
        return reportGoodsDao.select(reportGoodsQuery);
    }

    @Override
    public List<ReportGoods> findAll(ReportGoodsQuery reportGoodsQuery) {
        return reportGoodsDao.selectAll(reportGoodsQuery);
    }

    @Override
    public Integer count(ReportGoodsQuery reportGoodsQuery) {
        return reportGoodsDao.count(reportGoodsQuery);
    }

    @Override
    public List<ReportGoods> findByIds(List<Long> ids) {
        return reportGoodsDao.selectByIds(ids);
    }

    @Override
    public Map<Long, ReportGoods> findMapByIds(List<Long> ids) {
        Map<Long, ReportGoods> reportGoodsMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<ReportGoods> reportGoodss = reportGoodsDao.selectByIds(ids);
            for (ReportGoods reportGoods : reportGoodss) {
                reportGoodsMap.put(reportGoods.getId(),reportGoods);
            }
        }
        return reportGoodsMap;
    }

    @Override
    public Integer createBatch(List<ReportGoods> reportGoodss) {
        return reportGoodsDao.insertBatch(reportGoodss);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<ReportGoods> reportGoodss) {
        Integer result = 0;
        if (!this.isEmpty(reportGoodss) && !reportGoodss.isEmpty()){
            for (ReportGoods reportGoods : reportGoodss) {
                reportGoodsDao.updateById(reportGoods);
                result ++;
            }
        }
        return result;
    }
}