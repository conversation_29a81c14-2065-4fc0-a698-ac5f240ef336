package com.service;

import java.util.List;
import java.util.Map;

import com.domain.File;
import com.domain.complex.FileQuery;
/**
 * 文件表业务层
 *
 * @date 2025-07-16 21:03:32
 */
public interface FileService {

    Integer create(File file);

    Integer modifyById(File file);

    File findById(Long id);

    List<File> find(FileQuery fileQuery);

    List<File> findAll(FileQuery fileQuery);

    Integer count(FileQuery fileQuery);

    List<File> findByIds(List<Long> ids);

    Map<Long, File> findMapByIds(List<Long> ids);

    Integer createBatch(List<File> files);

    Integer modifyBatch(List<File> files);

    File findByUrl(String url);
}

