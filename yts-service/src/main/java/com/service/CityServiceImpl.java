package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.City;
import com.domain.complex.CityQuery;

import com.dao.CityDao;

@Service
public class CityServiceImpl extends BaseService implements CityService {

    @Autowired
    private CityDao cityDao;

    @Override
    public Integer create(City city) {
        return cityDao.insert(city);
    }

    @Override
    public Integer modifyById(City city) {
        return cityDao.updateById(city);
    }

    @Override
    public City findById(Long id) {
        return cityDao.selectById(id);
    }

    @Override
    public List<City> find(CityQuery cityQuery) {
        return cityDao.select(cityQuery);
    }

    @Override
    public List<City> findAll(CityQuery cityQuery) {
        return cityDao.selectAll(cityQuery);
    }

    @Override
    public Integer count(CityQuery cityQuery) {
        return cityDao.count(cityQuery);
    }

    @Override
    public List<City> findByIds(List<Long> ids) {
        return cityDao.selectByIds(ids);
    }

    @Override
    public Map<Long, City> findMapByIds(List<Long> ids) {
        Map<Long, City> cityMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<City> citys = cityDao.selectByIds(ids);
            for (City city : citys) {
                cityMap.put(city.getId(),city);
            }
        }
        return cityMap;
    }

    @Override
    public Integer createBatch(List<City> citys) {
        return cityDao.insertBatch(citys);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<City> citys) {
        Integer result = 0;
        if (!this.isEmpty(citys) && !citys.isEmpty()){
            for (City city : citys) {
                cityDao.updateById(city);
                result ++;
            }
        }
        return result;
    }
}