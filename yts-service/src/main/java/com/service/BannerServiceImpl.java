package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.BannerDao;
import com.domain.Banner;
import com.domain.complex.BannerQuery;

@Service
public class BannerServiceImpl extends BaseService implements BannerService {

    @Autowired
    private BannerDao bannerDao;

    @Override
    public Integer create(Banner banner) {
        return bannerDao.insert(banner);
    }

    @Override
    public Integer modifyById(Banner banner) {
        return bannerDao.updateById(banner);
    }

    @Override
    public Banner findById(Long id) {
        return bannerDao.selectById(id);
    }

    @Override
    public List<Banner> find(BannerQuery bannerQuery) {
        return bannerDao.select(bannerQuery);
    }

    @Override
    public List<Banner> findAll(BannerQuery bannerQuery) {
        return bannerDao.selectAll(bannerQuery);
    }

    @Override
    public Integer count(BannerQuery bannerQuery) {
        return bannerDao.count(bannerQuery);
    }

    @Override
    public List<Banner> findByIds(List<Long> ids) {
        return bannerDao.selectByIds(ids);
    }

    @Override
    public Map<Long, Banner> findMapByIds(List<Long> ids) {
        Map<Long, Banner> bannerMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<Banner> banners = bannerDao.selectByIds(ids);
            for (Banner banner : banners) {
                bannerMap.put(banner.getId(),banner);
            }
        }
        return bannerMap;
    }

    @Override
    public Integer createBatch(List<Banner> banners) {
        return bannerDao.insertBatch(banners);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<Banner> banners) {
        Integer result = 0;
        if (!this.isEmpty(banners) && !banners.isEmpty()){
            for (Banner banner : banners) {
                bannerDao.updateById(banner);
                result ++;
            }
        }
        return result;
    }
}