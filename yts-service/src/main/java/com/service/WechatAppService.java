package com.service;

import java.util.List;
import java.util.Map;

import com.domain.WechatApp;
import com.domain.complex.WechatAppQuery;
/**
 * 微信应用表业务层
 *
 * @date 2025-07-16 21:29:32
 */
public interface WechatAppService {

    Integer create(WechatApp wechatApp);

    Integer modifyById(WechatApp wechatApp);

    WechatApp findById(Long id);

    List<WechatApp> find(WechatAppQuery wechatAppQuery);

    List<WechatApp> findAll(WechatAppQuery wechatAppQuery);

    Integer count(WechatAppQuery wechatAppQuery);

    List<WechatApp> findByIds(List<Long> ids);

    Map<Long, WechatApp> findMapByIds(List<Long> ids);

    Integer createBatch(List<WechatApp> wechatApps);

    Integer modifyBatch(List<WechatApp> wechatApps);

}

