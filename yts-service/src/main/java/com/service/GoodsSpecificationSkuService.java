package com.service;

import java.util.List;
import java.util.Map;

import com.domain.GoodsSpecificationSku;
import com.domain.complex.GoodsSpecificationSkuQuery;
/**
 * 商品规格sku表业务层
 *
 * @date 2025-07-27 21:26:17
 */
public interface GoodsSpecificationSkuService {

    Integer create(GoodsSpecificationSku goodsSpecificationSku);

    Integer modifyById(GoodsSpecificationSku goodsSpecificationSku);

    GoodsSpecificationSku findById(Long id);

    GoodsSpecificationSku findBySpecValuesAndGoodsId(String specValues,Long goodsId);

    List<GoodsSpecificationSku> find(GoodsSpecificationSkuQuery goodsSpecificationSkuQuery);

    List<GoodsSpecificationSku> findAll(GoodsSpecificationSkuQuery goodsSpecificationSkuQuery);

    Integer count(GoodsSpecificationSkuQuery goodsSpecificationSkuQuery);

    List<GoodsSpecificationSku> findByIds(List<Long> ids);

    Map<Long, GoodsSpecificationSku> findMapByIds(List<Long> ids);

    Integer createBatch(List<GoodsSpecificationSku> goodsSpecificationSkus);

    Integer modifyBatch(List<GoodsSpecificationSku> goodsSpecificationSkus);

}

