package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.SalesTeacher;
import com.domain.complex.SalesTeacherQuery;

import com.dao.SalesTeacherDao;

@Service
public class SalesTeacherServiceImpl extends BaseService implements SalesTeacherService {

    @Autowired
    private SalesTeacherDao salesTeacherDao;

    @Override
    public Integer create(SalesTeacher salesTeacher) {
        return salesTeacherDao.insert(salesTeacher);
    }

    @Override
    public Integer modifyById(SalesTeacher salesTeacher) {
        return salesTeacherDao.updateById(salesTeacher);
    }

    @Override
    public SalesTeacher findById(Long id) {
        return salesTeacherDao.selectById(id);
    }

    @Override
    public List<SalesTeacher> find(SalesTeacherQuery salesTeacherQuery) {
        return salesTeacherDao.select(salesTeacherQuery);
    }

    @Override
    public List<SalesTeacher> findAll(SalesTeacherQuery salesTeacherQuery) {
        return salesTeacherDao.selectAll(salesTeacherQuery);
    }

    @Override
    public Integer count(SalesTeacherQuery salesTeacherQuery) {
        return salesTeacherDao.count(salesTeacherQuery);
    }

    @Override
    public List<SalesTeacher> findByIds(List<Long> ids) {
        return salesTeacherDao.selectByIds(ids);
    }

    @Override
    public Map<Long, SalesTeacher> findMapByIds(List<Long> ids) {
        Map<Long, SalesTeacher> salesTeacherMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<SalesTeacher> salesTeachers = salesTeacherDao.selectByIds(ids);
            for (SalesTeacher salesTeacher : salesTeachers) {
                salesTeacherMap.put(salesTeacher.getId(),salesTeacher);
            }
        }
        return salesTeacherMap;
    }

    @Override
    public Integer createBatch(List<SalesTeacher> salesTeachers) {
        return salesTeacherDao.insertBatch(salesTeachers);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<SalesTeacher> salesTeachers) {
        Integer result = 0;
        if (!this.isEmpty(salesTeachers) && !salesTeachers.isEmpty()){
            for (SalesTeacher salesTeacher : salesTeachers) {
                salesTeacherDao.updateById(salesTeacher);
                result ++;
            }
        }
        return result;
    }
}