<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.local</groupId>
        <artifactId>yts</artifactId>
        <version>dev</version>
    </parent>
    <groupId>com.local.yts.service</groupId>
    <artifactId>yts-service</artifactId>
    <packaging>jar</packaging>
    <name>yts-service</name>
    <url>http://maven.apache.org</url>
    <dependencies>
        <dependency>
            <groupId>com.local.yts.domain</groupId>
            <artifactId>yts-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.local.yts.common</groupId>
            <artifactId>yts-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.local.yts.dao</groupId>
            <artifactId>yts-dao</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
