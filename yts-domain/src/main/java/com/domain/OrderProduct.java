package com.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单产品表实体
 *
 * @date 2025-07-24 23:22:31
 */
public class OrderProduct extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 订单编号
	 */
	private Long orderId;
	/**
	 * 类型(0:课程 1:商品)
	 */
	private String productCatalog;
	/**
	 * 产品编号
	 */
	private Long productId;
	/**
	 * 数量
	 */
	private Integer number;
	/**
	 * 实付金额
	 */
	private BigDecimal amount;
	/**
	 * 产品名称
	 */
	private String productName;
	/**
	 * SKU编号
	 */
	private Long skuId;
	/**
	 * SKU属性
	 */
	private String specValues;
	/**
	 * 商品封面
	 */
	private String imgUrl;
	/**
	 * 成本价
	 */
	private BigDecimal buyingPrice;
	/**
	 * 划线价
	 */
	private BigDecimal originalPrice;
	/**
	 * 售价
	 */
	private BigDecimal price;
	/**
	 * 毛利(售价-成本价)
	 */
	private BigDecimal grossProfit;
	/**
	 * 团长佣金比例(%)
	 */
	private BigDecimal commissionRatio;
	/**
	 * 团长佣金
	 */
	private BigDecimal commission;
	/**
	 * 销售老师佣金比例(%)
	 */
	private BigDecimal salesTeacherCommissionRatio;
	/**
	 * 销售老师佣金
	 */
	private BigDecimal salesTeacherCommission;
	/**
	 * 单位
	 */
	private String unit;
	/**
	 * 商品类型
	 */
	private String goodsCatalog;
	/**
	 * 商品分类编号
	 */
	private Long goodsTypeId;
	/**
	 * 分类名称
	 */
	private String goodsTypeName;

	public Long getOrderId (){
		return orderId;
	}

	public void setOrderId (Long orderId) {
		this.orderId = orderId;
	}

	public String getProductCatalog (){
		return productCatalog;
	}

	public void setProductCatalog (String productCatalog) {
		this.productCatalog = productCatalog;
	}

	public Long getProductId (){
		return productId;
	}

	public void setProductId (Long productId) {
		this.productId = productId;
	}

	public Integer getNumber (){
		return number;
	}

	public void setNumber (Integer number) {
		this.number = number;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getCommissionRatio (){
		return commissionRatio;
	}

	public void setCommissionRatio (BigDecimal commissionRatio) {
		this.commissionRatio = commissionRatio;
	}

	public BigDecimal getCommission (){
		return commission;
	}

	public void setCommission (BigDecimal commission) {
		this.commission = commission;
	}

	public BigDecimal getSalesTeacherCommissionRatio (){
		return salesTeacherCommissionRatio;
	}

	public void setSalesTeacherCommissionRatio (BigDecimal salesTeacherCommissionRatio) {
		this.salesTeacherCommissionRatio = salesTeacherCommissionRatio;
	}

	public BigDecimal getSalesTeacherCommission (){
		return salesTeacherCommission;
	}

	public void setSalesTeacherCommission (BigDecimal salesTeacherCommission) {
		this.salesTeacherCommission = salesTeacherCommission;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSpecValues() {
		return specValues;
	}

	public void setSpecValues(String specValues) {
		this.specValues = specValues;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public BigDecimal getBuyingPrice() {
		return buyingPrice;
	}

	public void setBuyingPrice(BigDecimal buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getGrossProfit() {
		return grossProfit;
	}

	public void setGrossProfit(BigDecimal grossProfit) {
		this.grossProfit = grossProfit;
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getGoodsCatalog() {
		return goodsCatalog;
	}

	public void setGoodsCatalog(String goodsCatalog) {
		this.goodsCatalog = goodsCatalog;
	}

	public Long getGoodsTypeId() {
		return goodsTypeId;
	}

	public void setGoodsTypeId(Long goodsTypeId) {
		this.goodsTypeId = goodsTypeId;
	}

	public String getGoodsTypeName() {
		return goodsTypeName;
	}

	public void setGoodsTypeName(String goodsTypeName) {
		this.goodsTypeName = goodsTypeName;
	}
}
