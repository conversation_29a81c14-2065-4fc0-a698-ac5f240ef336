package com.domain;

import java.util.Date;

/**
 * 文件表实体
 *
 * @date 2025-07-16 21:03:32
 */
public class File extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 名称
	 */
	private String name;
	/**
	 * 类型
	 */
	private String type;
	/**
	 * 大小
	 */
	private Long size;
	/**
	 * 位置
	 */
	private String url;
	/**
	 * oss位置
	 */
	private String ossUrl;

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getType (){
		return type;
	}

	public void setType (String type) {
		this.type = type;
	}

	public Long getSize (){
		return size;
	}

	public void setSize (Long size) {
		this.size = size;
	}

	public String getUrl (){
		return url;
	}

	public void setUrl (String url) {
		this.url = url;
	}

	public String getOssUrl (){
		return ossUrl;
	}

	public void setOssUrl (String ossUrl) {
		this.ossUrl = ossUrl;
	}

}
