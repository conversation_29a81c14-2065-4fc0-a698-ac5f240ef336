package com.domain;

import java.util.Date;

/**
 * 订单号流水表实体
 *
 * @date 2025-07-23 20:14:44
 */
public class OrderCodeFlow extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 订单号
	 */
	private String orderCode;
	/**
	 * 预支付交易会话标识
	 */
	private String prepayId;
	/**
	 * 支付类型(1000:微信小程序 1001:支付宝)
	 */
	private String paymentType;
	/**
	 * 交易类型
	 */
	private String tradeType;
	/**
	 * 交易状态
	 */
	private String tradeState;
	/**
	 * 支付金额(分)
	 */
	private Integer payerTotal;
	/**
	 * 通知参数
	 */
	private String content;

	public String getOrderCode (){
		return orderCode;
	}

	public void setOrderCode (String orderCode) {
		this.orderCode = orderCode;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public String getPaymentType (){
		return paymentType;
	}

	public void setPaymentType (String paymentType) {
		this.paymentType = paymentType;
	}

	public String getTradeType (){
		return tradeType;
	}

	public void setTradeType (String tradeType) {
		this.tradeType = tradeType;
	}

	public String getTradeState (){
		return tradeState;
	}

	public void setTradeState (String tradeState) {
		this.tradeState = tradeState;
	}

	public Integer getPayerTotal (){
		return payerTotal;
	}

	public void setPayerTotal (Integer payerTotal) {
		this.payerTotal = payerTotal;
	}

	public String getContent (){
		return content;
	}

	public void setContent (String content) {
		this.content = content;
	}

}
