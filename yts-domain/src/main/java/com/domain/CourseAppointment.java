package com.domain;

import java.util.Date;

/**
 * 课程预约表实体
 *
 * @date 2025-07-19 10:58:28
 */
public class CourseAppointment extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 课程id
	 */
	private Long courseId;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 是否联系(0:已联系 1:未联系)
	 */
	private String contact;
	/**
	 * 描述
	 */
	private String comment;

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public Long getCourseId (){
		return courseId;
	}

	public void setCourseId (Long courseId) {
		this.courseId = courseId;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getRemark (){
		return remark;
	}

	public void setRemark (String remark) {
		this.remark = remark;
	}

	public String getContact (){
		return contact;
	}

	public void setContact (String contact) {
		this.contact = contact;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

}
