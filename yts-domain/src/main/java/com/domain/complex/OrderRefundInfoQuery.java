package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 订单退款记录表查询对象
 *
 * @date 2025-07-29 22:28:19
 */
public class OrderRefundInfoQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 商户订单编号
	 */
	private String orderNo;
	/**
	 * 商户退款单编号
	 */
	private String refundNo;
	/**
	 * 支付系统退款单号
	 */
	private String refundId;
	/**
	 * 支付系统退款单号集合
	 */
	private List<String> refundIds;
	/**
	 * 原订单金额(分)
	 */
	private Integer totalFee;
	/**
	 * 退款金额(分)
	 */
	private Integer refund;
	/**
	 * 退款原因
	 */
	private String reason;
	/**
	 * 退款状态
	 */
	private String refundStatus;
	/**
	 * 申请退款返回参数
	 */
	private String contentReturn;
	/**
	 * 退款结果通知参数
	 */
	private String contentNotify;
	/**
	 * 支付类型(1000:微信小程序 1001:支付宝)
	 */
	private String paymentType;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public String getOrderNo (){
		return orderNo;
	}

	public void setOrderNo (String orderNo) {
		this.orderNo = orderNo;
	}

	public String getRefundNo (){
		return refundNo;
	}

	public void setRefundNo (String refundNo) {
		this.refundNo = refundNo;
	}

	public String getRefundId (){
		return refundId;
	}

	public void setRefundId (String refundId) {
		this.refundId = refundId;
	}

	public List<String> getRefundIds (){
		return refundIds;
	}

	public void setRefundIds (List<String> refundIds) {
		this.refundIds = refundIds;
	}

	public Integer getTotalFee (){
		return totalFee;
	}

	public void setTotalFee (Integer totalFee) {
		this.totalFee = totalFee;
	}

	public Integer getRefund (){
		return refund;
	}

	public void setRefund (Integer refund) {
		this.refund = refund;
	}

	public String getReason (){
		return reason;
	}

	public void setReason (String reason) {
		this.reason = reason;
	}

	public String getRefundStatus (){
		return refundStatus;
	}

	public void setRefundStatus (String refundStatus) {
		this.refundStatus = refundStatus;
	}

	public String getContentReturn (){
		return contentReturn;
	}

	public void setContentReturn (String contentReturn) {
		this.contentReturn = contentReturn;
	}

	public String getContentNotify (){
		return contentNotify;
	}

	public void setContentNotify (String contentNotify) {
		this.contentNotify = contentNotify;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}
}
