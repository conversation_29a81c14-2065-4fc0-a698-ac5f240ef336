package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 订单号流水表查询对象
 *
 * @date 2025-07-23 20:14:44
 */
public class OrderCodeFlowQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 订单号
	 */
	private String orderCode;
	/**
	 * 预支付交易会话标识
	 */
	private String prepayId;
	/**
	 * 预支付交易会话标识集合
	 */
	private List<String> prepayIds;
	/**
	 * 支付类型(1000:微信小程序 1001:支付宝)
	 */
	private String paymentType;
	/**
	 * 交易类型
	 */
	private String tradeType;
	/**
	 * 交易状态
	 */
	private String tradeState;
	/**
	 * 支付金额(分)
	 */
	private Integer payerTotal;
	/**
	 * 通知参数
	 */
	private String content;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public String getOrderCode (){
		return orderCode;
	}

	public void setOrderCode (String orderCode) {
		this.orderCode = orderCode;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public List<String> getPrepayIds (){
		return prepayIds;
	}

	public void setPrepayIds (List<String> prepayIds) {
		this.prepayIds = prepayIds;
	}

	public String getPaymentType (){
		return paymentType;
	}

	public void setPaymentType (String paymentType) {
		this.paymentType = paymentType;
	}

	public String getTradeType (){
		return tradeType;
	}

	public void setTradeType (String tradeType) {
		this.tradeType = tradeType;
	}

	public String getTradeState (){
		return tradeState;
	}

	public void setTradeState (String tradeState) {
		this.tradeState = tradeState;
	}

	public Integer getPayerTotal (){
		return payerTotal;
	}

	public void setPayerTotal (Integer payerTotal) {
		this.payerTotal = payerTotal;
	}

	public String getContent (){
		return content;
	}

	public void setContent (String content) {
		this.content = content;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
}
