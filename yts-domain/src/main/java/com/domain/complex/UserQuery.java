package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 用户表查询对象
 *
 * @date 2025-07-24 23:22:31
 */
public class UserQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 用户统一标识(微信开放平台)
	 */
	private String unionId;
	/**
	 * 用户统一标识(微信开放平台)集合
	 */
	private List<String> unionIds;
	/**
	 * 用户统一标识(服务号)
	 */
	private String openId;
	/**
	 * 邀请码
	 */
	private Long invitationCode;
	/**
	 * 二维码
	 */
	private String qrCode;
	/**
	 * 用户类型(0:普通用户 1:常驻用户)
	 */
	private String catalog;
	/**
	 * 名字
	 */
	private String name;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 头像
	 */
	private String avatar;
	/**
	 * 性别(0:男 1:女)
	 */
	private String gender;
	/**
	 * 最后登录时间
	 */
	private Date lastLoginTime;
	/**
	 * 最小最后登录时间
	 */
	private Date minLastLoginTime;
	/**
	 * 最大最后登录时间
	 */
	private Date maxLastLoginTime;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public String getUnionId (){
		return unionId;
	}

	public void setUnionId (String unionId) {
		this.unionId = unionId;
	}

	public List<String> getUnionIds (){
		return unionIds;
	}

	public void setUnionIds (List<String> unionIds) {
		this.unionIds = unionIds;
	}

	public Long getInvitationCode (){
		return invitationCode;
	}

	public void setInvitationCode (Long invitationCode) {
		this.invitationCode = invitationCode;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getAvatar (){
		return avatar;
	}

	public void setAvatar (String avatar) {
		this.avatar = avatar;
	}

	public String getGender (){
		return gender;
	}

	public void setGender (String gender) {
		this.gender = gender;
	}

	public Date getLastLoginTime (){
		return lastLoginTime;
	}

	public void setLastLoginTime (Date lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	public Date getMinLastLoginTime (){
		return minLastLoginTime;
	}

	public void setMinLastLoginTime (Date minLastLoginTime) {
		this.minLastLoginTime = minLastLoginTime;
	}

	public Date getMaxLastLoginTime (){
		return maxLastLoginTime;
	}

	public void setMaxLastLoginTime (Date maxLastLoginTime) {
		this.maxLastLoginTime = maxLastLoginTime;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getQrCode() {
		return qrCode;
	}

	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}
}
