package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 系统用户日志表查询对象
 *
 * @date 2025-07-20 18:59:08
 */
public class LogQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 平台
	 */
	private String platform;
	/**
	 * 版本
	 */
	private String version;
	/**
	 * 用户类型
	 */
	private String type;
	/**
	 * 用户编号
	 */
	private Long userId;
	/**
	 * 用户编号集合
	 */
	private List<Long> userIds;
	/**
	 * IP
	 */
	private String ip;
	/**
	 * 地址
	 */
	private String url;
	/**
	 * 请求编号
	 */
	private String requestId;
	/**
	 * 请求编号集合
	 */
	private List<String> requestIds;
	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 最小开始时间
	 */
	private Date minStartTime;
	/**
	 * 最大开始时间
	 */
	private Date maxStartTime;
	/**
	 * 结束时间
	 */
	private Date endTime;
	/**
	 * 最小结束时间
	 */
	private Date minEndTime;
	/**
	 * 最大结束时间
	 */
	private Date maxEndTime;
	/**
	 * 执行时长
	 */
	private Integer duration;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public String getPlatform (){
		return platform;
	}

	public void setPlatform (String platform) {
		this.platform = platform;
	}

	public String getVersion (){
		return version;
	}

	public void setVersion (String version) {
		this.version = version;
	}

	public String getType (){
		return type;
	}

	public void setType (String type) {
		this.type = type;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public List<Long> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Long> userIds) {
		this.userIds = userIds;
	}

	public String getIp (){
		return ip;
	}

	public void setIp (String ip) {
		this.ip = ip;
	}

	public String getUrl (){
		return url;
	}

	public void setUrl (String url) {
		this.url = url;
	}

	public String getRequestId (){
		return requestId;
	}

	public void setRequestId (String requestId) {
		this.requestId = requestId;
	}

	public List<String> getRequestIds (){
		return requestIds;
	}

	public void setRequestIds (List<String> requestIds) {
		this.requestIds = requestIds;
	}

	public Date getStartTime (){
		return startTime;
	}

	public void setStartTime (Date startTime) {
		this.startTime = startTime;
	}

	public Date getMinStartTime (){
		return minStartTime;
	}

	public void setMinStartTime (Date minStartTime) {
		this.minStartTime = minStartTime;
	}

	public Date getMaxStartTime (){
		return maxStartTime;
	}

	public void setMaxStartTime (Date maxStartTime) {
		this.maxStartTime = maxStartTime;
	}

	public Date getEndTime (){
		return endTime;
	}

	public void setEndTime (Date endTime) {
		this.endTime = endTime;
	}

	public Date getMinEndTime (){
		return minEndTime;
	}

	public void setMinEndTime (Date minEndTime) {
		this.minEndTime = minEndTime;
	}

	public Date getMaxEndTime (){
		return maxEndTime;
	}

	public void setMaxEndTime (Date maxEndTime) {
		this.maxEndTime = maxEndTime;
	}

	public Integer getDuration (){
		return duration;
	}

	public void setDuration (Integer duration) {
		this.duration = duration;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
}
