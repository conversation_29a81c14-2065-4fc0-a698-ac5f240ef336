package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 微信应用表查询对象
 *
 * @date 2025-07-16 21:29:32
 */
public class WechatAppQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 应用ID
	 */
	private String appid;
	/**
	 * 应用密钥
	 */
	private String secret;
	/**
	 * 应用名称
	 */
	private String name;
	/**
	 * 访问凭证
	 */
	private String accessToken;
	/**
	 * 凭证刷新日期
	 */
	private Date accessTokenDatetime;
	/**
	 * 最小凭证刷新日期
	 */
	private Date minAccessTokenDatetime;
	/**
	 * 最大凭证刷新日期
	 */
	private Date maxAccessTokenDatetime;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public String getAppid (){
		return appid;
	}

	public void setAppid (String appid) {
		this.appid = appid;
	}

	public String getSecret (){
		return secret;
	}

	public void setSecret (String secret) {
		this.secret = secret;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getAccessToken (){
		return accessToken;
	}

	public void setAccessToken (String accessToken) {
		this.accessToken = accessToken;
	}

	public Date getAccessTokenDatetime (){
		return accessTokenDatetime;
	}

	public void setAccessTokenDatetime (Date accessTokenDatetime) {
		this.accessTokenDatetime = accessTokenDatetime;
	}

	public Date getMinAccessTokenDatetime (){
		return minAccessTokenDatetime;
	}

	public void setMinAccessTokenDatetime (Date minAccessTokenDatetime) {
		this.minAccessTokenDatetime = minAccessTokenDatetime;
	}

	public Date getMaxAccessTokenDatetime (){
		return maxAccessTokenDatetime;
	}

	public void setMaxAccessTokenDatetime (Date maxAccessTokenDatetime) {
		this.maxAccessTokenDatetime = maxAccessTokenDatetime;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
}
