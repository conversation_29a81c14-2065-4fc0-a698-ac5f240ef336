package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 用户团队表查询对象
 *
 * @date 2025-07-19 10:58:27
 */
public class UserTeamQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 队长编号
	 */
	private Long captainUserId;
	/**
	 * 队长编号集合
	 */
	private List<Long> captainUserIds;
	/**
	 * 队员编号
	 */
	private Long teamMemberUserId;
	/**
	 * 队员编号集合
	 */
	private List<Long> teamMemberUserIds;
	/**
	 * 绑定类型
	 */
	private String bindingCatalog;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public Long getCaptainUserId (){
		return captainUserId;
	}

	public void setCaptainUserId (Long captainUserId) {
		this.captainUserId = captainUserId;
	}

	public List<Long> getCaptainUserIds (){
		return captainUserIds;
	}

	public void setCaptainUserIds (List<Long> captainUserIds) {
		this.captainUserIds = captainUserIds;
	}

	public Long getTeamMemberUserId (){
		return teamMemberUserId;
	}

	public void setTeamMemberUserId (Long teamMemberUserId) {
		this.teamMemberUserId = teamMemberUserId;
	}

	public List<Long> getTeamMemberUserIds (){
		return teamMemberUserIds;
	}

	public void setTeamMemberUserIds (List<Long> teamMemberUserIds) {
		this.teamMemberUserIds = teamMemberUserIds;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getBindingCatalog() {
		return bindingCatalog;
	}

	public void setBindingCatalog(String bindingCatalog) {
		this.bindingCatalog = bindingCatalog;
	}
}
