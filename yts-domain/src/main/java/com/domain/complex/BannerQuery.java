package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 轮播图表查询对象
 *
 * @date 2025-07-19 10:58:28
 */
public class BannerQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 图片地址
	 */
	private String imgUrl;
	/**
	 * 是否跳转(0:跳转 1:不跳转)
	 */
	private String jump;
	/**
	 * 跳转类型
	 */
	private String jumpCatalog;
	/**
	 * 跳转编号
	 */
	private Long jumpId;
	/**
	 * 跳转编号集合
	 */
	private List<Long> jumpIds;
	/**
	 * 上架状态(0:上架 1:下架)
	 */
	private String stage;
	/**
	 * 排序字段
	 */
	private Integer sort;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getJump (){
		return jump;
	}

	public void setJump (String jump) {
		this.jump = jump;
	}

	public String getJumpCatalog (){
		return jumpCatalog;
	}

	public void setJumpCatalog (String jumpCatalog) {
		this.jumpCatalog = jumpCatalog;
	}

	public Long getJumpId (){
		return jumpId;
	}

	public void setJumpId (Long jumpId) {
		this.jumpId = jumpId;
	}

	public List<Long> getJumpIds (){
		return jumpIds;
	}

	public void setJumpIds (List<Long> jumpIds) {
		this.jumpIds = jumpIds;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public Integer getSort (){
		return sort;
	}

	public void setSort (Integer sort) {
		this.sort = sort;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
}
