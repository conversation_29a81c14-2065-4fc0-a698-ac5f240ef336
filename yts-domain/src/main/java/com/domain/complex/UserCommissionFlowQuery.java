package com.domain.complex;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户佣金明细表查询对象
 *
 * @date 2025-07-27 11:32:54
 */
public class UserCommissionFlowQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 流水号
	 */
	private String flowCode;
	/**
	 * 用户编号
	 */
	private Long userId;
	/**
	 * 用户编号集合
	 */
	private List<Long> userIds;
	/**
	 * 订单ID
	 */
	private Long orderId;
	/**
	 * 订单ID集合
	 */
	private List<Long> orderIds;
	/**
	 * 订单号
	 */
	private String orderCode;
	/**
	 * 预支付交易会话标识
	 */
	private String prepayId;
	/**
	 * 预支付交易会话标识集合
	 */
	private List<String> prepayIds;
	/**
	 * 交易时间
	 */
	private Date transactionTime;
	/**
	 * 最小交易时间
	 */
	private Date minTransactionTime;
	/**
	 * 最大交易时间
	 */
	private Date maxTransactionTime;
	/**
	 * 流转金额
	 */
	private BigDecimal flowAmount;
	/**
	 * 流转金额
	 */
	private BigDecimal amount;
	/**
	 * 分类(1000:分佣流水)
	 */
	private String catalog;
	/**
	 * 分类集合(1000:分佣流水)
	 */
	private List<String> catalogs;
	/**
	 * 支付方式(1000:微信小程序支付)
	 */
	private String payChannel;
	/**
	 * 收支出(0:收入 1:支出)
	 */
	private String income;
	/**
	 * 标签
	 */
	private String label;
	/**
	 * 备注
	 */
	private String comment;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public String getFlowCode (){
		return flowCode;
	}

	public void setFlowCode (String flowCode) {
		this.flowCode = flowCode;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public List<Long> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Long> userIds) {
		this.userIds = userIds;
	}

	public Long getOrderId (){
		return orderId;
	}

	public void setOrderId (Long orderId) {
		this.orderId = orderId;
	}

	public List<Long> getOrderIds (){
		return orderIds;
	}

	public void setOrderIds (List<Long> orderIds) {
		this.orderIds = orderIds;
	}

	public String getOrderCode (){
		return orderCode;
	}

	public void setOrderCode (String orderCode) {
		this.orderCode = orderCode;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public List<String> getPrepayIds (){
		return prepayIds;
	}

	public void setPrepayIds (List<String> prepayIds) {
		this.prepayIds = prepayIds;
	}

	public Date getTransactionTime (){
		return transactionTime;
	}

	public void setTransactionTime (Date transactionTime) {
		this.transactionTime = transactionTime;
	}

	public Date getMinTransactionTime (){
		return minTransactionTime;
	}

	public void setMinTransactionTime (Date minTransactionTime) {
		this.minTransactionTime = minTransactionTime;
	}

	public Date getMaxTransactionTime (){
		return maxTransactionTime;
	}

	public void setMaxTransactionTime (Date maxTransactionTime) {
		this.maxTransactionTime = maxTransactionTime;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public String getPayChannel (){
		return payChannel;
	}

	public void setPayChannel (String payChannel) {
		this.payChannel = payChannel;
	}

	public String getIncome (){
		return income;
	}

	public void setIncome (String income) {
		this.income = income;
	}


	public String getLabel (){
		return label;
	}

	public void setLabel (String label) {
		this.label = label;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public BigDecimal getFlowAmount() {
		return flowAmount;
	}

	public void setFlowAmount(BigDecimal flowAmount) {
		this.flowAmount = flowAmount;
	}

	public List<String> getCatalogs() {
		return catalogs;
	}

	public void setCatalogs(List<String> catalogs) {
		this.catalogs = catalogs;
	}
}
