package com.domain.complex;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品表查询对象
 *
 * @date 2025-07-27 11:42:53
 */
public class GoodsQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 商品名称
	 */
	private String name;
	/**
	 * 商品单位
	 */
	private String unit;
	/**
	 * 商品封面
	 */
	private String imgUrl;
	/**
	 * 商品类型
	 */
	private String catalog;
	/**
	 * 商品分类编号
	 */
	private Long goodsTypeId;
	/**
	 * 商品分类编号集合
	 */
	private List<Long> goodsTypeIds;
	/**
	 * 成本价
	 */
	private BigDecimal buyingPrice;
	/**
	 * 划线价
	 */
	private BigDecimal originalPrice;
	/**
	 * 售价
	 */
	private BigDecimal price;
	private BigDecimal minPrice;
	private BigDecimal maxPrice;
	/**
	 * 毛利(售价-成本价)
	 */
	private BigDecimal grossProfit;
	/**
	 * 是否分佣(0:是 1:否)
	 */
	private String openCommission;
	/**
	 * 团长佣金比例(%)
	 */
	private BigDecimal commissionRatio;
	/**
	 * 销售老师佣金比例(%)
	 */
	private BigDecimal salesTeacherCommissionRatio;
	/**
	 * 商品详情图片
	 */
	private String detailImgUrl;
	/**
	 * 描述
	 */
	private String comment;
	/**
	 * 虚拟销量
	 */
	private Integer salesVolume;
	/**
	 * 真实销量
	 */
	private Integer realSalesVolume;
	private Integer minRealSalesVolume;
	private Integer maxRealSalesVolume;
	/**
	 * 是否首页展示(0:展示 1:不展示)
	 */
	private String home;
	/**
	 * 上架状态(0:上架 1:下架)
	 */
	private String stage;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	private String sortType;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getCatalog() {
		return catalog;
	}

	public void setCatalog(String catalog) {
		this.catalog = catalog;
	}

	public Long getGoodsTypeId() {
		return goodsTypeId;
	}

	public void setGoodsTypeId(Long goodsTypeId) {
		this.goodsTypeId = goodsTypeId;
	}

	public List<Long> getGoodsTypeIds() {
		return goodsTypeIds;
	}

	public void setGoodsTypeIds(List<Long> goodsTypeIds) {
		this.goodsTypeIds = goodsTypeIds;
	}

	public BigDecimal getBuyingPrice() {
		return buyingPrice;
	}

	public void setBuyingPrice(BigDecimal buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getMinPrice() {
		return minPrice;
	}

	public void setMinPrice(BigDecimal minPrice) {
		this.minPrice = minPrice;
	}

	public BigDecimal getMaxPrice() {
		return maxPrice;
	}

	public void setMaxPrice(BigDecimal maxPrice) {
		this.maxPrice = maxPrice;
	}

	public BigDecimal getGrossProfit() {
		return grossProfit;
	}

	public void setGrossProfit(BigDecimal grossProfit) {
		this.grossProfit = grossProfit;
	}

	public String getOpenCommission() {
		return openCommission;
	}

	public void setOpenCommission(String openCommission) {
		this.openCommission = openCommission;
	}

	public BigDecimal getCommissionRatio() {
		return commissionRatio;
	}

	public void setCommissionRatio(BigDecimal commissionRatio) {
		this.commissionRatio = commissionRatio;
	}

	public BigDecimal getSalesTeacherCommissionRatio() {
		return salesTeacherCommissionRatio;
	}

	public void setSalesTeacherCommissionRatio(BigDecimal salesTeacherCommissionRatio) {
		this.salesTeacherCommissionRatio = salesTeacherCommissionRatio;
	}

	public String getDetailImgUrl() {
		return detailImgUrl;
	}

	public void setDetailImgUrl(String detailImgUrl) {
		this.detailImgUrl = detailImgUrl;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public Integer getSalesVolume() {
		return salesVolume;
	}

	public void setSalesVolume(Integer salesVolume) {
		this.salesVolume = salesVolume;
	}

	public Integer getRealSalesVolume() {
		return realSalesVolume;
	}

	public void setRealSalesVolume(Integer realSalesVolume) {
		this.realSalesVolume = realSalesVolume;
	}

	public Integer getMinRealSalesVolume() {
		return minRealSalesVolume;
	}

	public void setMinRealSalesVolume(Integer minRealSalesVolume) {
		this.minRealSalesVolume = minRealSalesVolume;
	}

	public Integer getMaxRealSalesVolume() {
		return maxRealSalesVolume;
	}

	public void setMaxRealSalesVolume(Integer maxRealSalesVolume) {
		this.maxRealSalesVolume = maxRealSalesVolume;
	}

	public String getStage() {
		return stage;
	}

	public void setStage(String stage) {
		this.stage = stage;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getSortType() {
		return sortType;
	}

	public void setSortType(String sortType) {
		this.sortType = sortType;
	}

	public String getHome() {
		return home;
	}

	public void setHome(String home) {
		this.home = home;
	}
}
