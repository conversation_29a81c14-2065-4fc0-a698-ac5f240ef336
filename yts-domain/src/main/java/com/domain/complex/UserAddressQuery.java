package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 用户地址表查询对象
 *
 * @date 2025-07-23 22:58:35
 */
public class UserAddressQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 用户编号
	 */
	private Long userId;
	/**
	 * 用户编号集合
	 */
	private List<Long> userIds;
	/**
	 * 省份编号
	 */
	private Long provinceId;
	/**
	 * 省份编号集合
	 */
	private List<Long> provinceIds;
	/**
	 * 城市编号
	 */
	private Long cityId;
	/**
	 * 城市编号集合
	 */
	private List<Long> cityIds;
	/**
	 * 区县编号
	 */
	private Long districtId;
	/**
	 * 区县编号集合
	 */
	private List<Long> districtIds;
	/**
	 * 详细地址
	 */
	private String detailAddress;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 是否默认地址(0:是 1:否)
	 */
	private String defaultAddress;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public List<Long> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Long> userIds) {
		this.userIds = userIds;
	}

	public Long getProvinceId (){
		return provinceId;
	}

	public void setProvinceId (Long provinceId) {
		this.provinceId = provinceId;
	}

	public List<Long> getProvinceIds (){
		return provinceIds;
	}

	public void setProvinceIds (List<Long> provinceIds) {
		this.provinceIds = provinceIds;
	}

	public Long getCityId (){
		return cityId;
	}

	public void setCityId (Long cityId) {
		this.cityId = cityId;
	}

	public List<Long> getCityIds (){
		return cityIds;
	}

	public void setCityIds (List<Long> cityIds) {
		this.cityIds = cityIds;
	}

	public Long getDistrictId (){
		return districtId;
	}

	public void setDistrictId (Long districtId) {
		this.districtId = districtId;
	}

	public List<Long> getDistrictIds (){
		return districtIds;
	}

	public void setDistrictIds (List<Long> districtIds) {
		this.districtIds = districtIds;
	}

	public String getDetailAddress (){
		return detailAddress;
	}

	public void setDetailAddress (String detailAddress) {
		this.detailAddress = detailAddress;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getDefaultAddress() {
		return defaultAddress;
	}

	public void setDefaultAddress(String defaultAddress) {
		this.defaultAddress = defaultAddress;
	}
}
