package com.domain.complex;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 课程表查询对象
 *
 * @date 2025-07-19 10:58:28
 */
public class CourseQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 课程标题
	 */
	private String title;
	/**
	 * 课程封面
	 */
	private String imgUrl;
	/**
	 * 课程时长
	 */
	private Integer length;
	/**
	 * 课程详情图片
	 */
	private String detailImgUrl;
	/**
	 * 原价
	 */
	private BigDecimal originalPrice;
	/**
	 * 价格
	 */
	private BigDecimal price;
	/**
	 * 描述
	 */
	private String comment;
	/**
	 * 虚拟销量
	 */
	private String salesVolume;
	/**
	 * 是否首页展示(0:展示 1:不展示)
	 */
	private String home;
	/**
	 * 排序字段
	 */
	private Integer sort;
	/**
	 * 上架状态(0:上架 1:下架)
	 */
	private String stage;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public String getTitle (){
		return title;
	}

	public void setTitle (String title) {
		this.title = title;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public Integer getLength (){
		return length;
	}

	public void setLength (Integer length) {
		this.length = length;
	}

	public String getDetailImgUrl (){
		return detailImgUrl;
	}

	public void setDetailImgUrl (String detailImgUrl) {
		this.detailImgUrl = detailImgUrl;
	}

	public BigDecimal getOriginalPrice (){
		return originalPrice;
	}

	public void setOriginalPrice (BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice (){
		return price;
	}

	public void setPrice (BigDecimal price) {
		this.price = price;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public String getSalesVolume (){
		return salesVolume;
	}

	public void setSalesVolume (String salesVolume) {
		this.salesVolume = salesVolume;
	}

	public String getHome (){
		return home;
	}

	public void setHome (String home) {
		this.home = home;
	}

	public Integer getSort (){
		return sort;
	}

	public void setSort (Integer sort) {
		this.sort = sort;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
}
