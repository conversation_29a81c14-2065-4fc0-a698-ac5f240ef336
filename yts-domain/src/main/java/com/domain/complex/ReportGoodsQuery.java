package com.domain.complex;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 统计商品表查询对象
 *
 * @date 2025-07-20 16:36:02
 */
public class ReportGoodsQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 日期
	 */
	private Date date;
	/**
	 * 最小日期
	 */
	private Date minDate;
	/**
	 * 最大日期
	 */
	private Date maxDate;
	/**
	 * 商品编号
	 */
	private Long goodsId;
	/**
	 * 商品编号集合
	 */
	private List<Long> goodsIds;
	/**
	 * 浏览数量
	 */
	private Long browseNum;
	/**
	 * 支付数量
	 */
	private Long payNum;
	/**
	 * 支付金额
	 */
	private BigDecimal payAmount;
	/**
	 * 退款数量
	 */
	private Long refundNum;
	/**
	 * 退款金额
	 */
	private BigDecimal refundAmount;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;
	/**
	 * 排序字段
	 */
	private String sortType;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public Date getDate (){
		return date;
	}

	public void setDate (Date date) {
		this.date = date;
	}

	public Date getMinDate (){
		return minDate;
	}

	public void setMinDate (Date minDate) {
		this.minDate = minDate;
	}

	public Date getMaxDate (){
		return maxDate;
	}

	public void setMaxDate (Date maxDate) {
		this.maxDate = maxDate;
	}

	public Long getGoodsId (){
		return goodsId;
	}

	public void setGoodsId (Long goodsId) {
		this.goodsId = goodsId;
	}

	public List<Long> getGoodsIds (){
		return goodsIds;
	}

	public void setGoodsIds (List<Long> goodsIds) {
		this.goodsIds = goodsIds;
	}

	public Long getBrowseNum (){
		return browseNum;
	}

	public void setBrowseNum (Long browseNum) {
		this.browseNum = browseNum;
	}

	public Long getPayNum (){
		return payNum;
	}

	public void setPayNum (Long payNum) {
		this.payNum = payNum;
	}

	public BigDecimal getPayAmount (){
		return payAmount;
	}

	public void setPayAmount (BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

	public Long getRefundNum (){
		return refundNum;
	}

	public void setRefundNum (Long refundNum) {
		this.refundNum = refundNum;
	}

	public BigDecimal getRefundAmount (){
		return refundAmount;
	}

	public void setRefundAmount (BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getSortType() {
		return sortType;
	}

	public void setSortType(String sortType) {
		this.sortType = sortType;
	}
}
