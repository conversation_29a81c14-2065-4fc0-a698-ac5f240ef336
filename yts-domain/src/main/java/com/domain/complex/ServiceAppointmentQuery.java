package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 服务预约表查询对象
 *
 * @date 2025-07-19 10:58:27
 */
public class ServiceAppointmentQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 用户id集合
	 */
	private List<Long> userIds;
	/**
	 * 服务id
	 */
	private Long serviceId;
	/**
	 * 服务id集合
	 */
	private List<Long> serviceIds;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 是否联系(0:已联系 1:未联系)
	 */
	private String contact;
	/**
	 * 描述
	 */
	private String comment;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public List<Long> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Long> userIds) {
		this.userIds = userIds;
	}

	public Long getServiceId (){
		return serviceId;
	}

	public void setServiceId (Long serviceId) {
		this.serviceId = serviceId;
	}

	public List<Long> getServiceIds (){
		return serviceIds;
	}

	public void setServiceIds (List<Long> serviceIds) {
		this.serviceIds = serviceIds;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getRemark (){
		return remark;
	}

	public void setRemark (String remark) {
		this.remark = remark;
	}

	public String getContact (){
		return contact;
	}

	public void setContact (String contact) {
		this.contact = contact;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
}
