package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 区(县)表查询对象
 *
 * @date 2025-07-23 22:58:35
 */
public class DistrictQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 城市编号
	 */
	private Long cityId;
	/**
	 * 城市编号集合
	 */
	private List<Long> cityIds;
	/**
	 * 区(县)名称
	 */
	private String name;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public Long getCityId (){
		return cityId;
	}

	public void setCityId (Long cityId) {
		this.cityId = cityId;
	}

	public List<Long> getCityIds (){
		return cityIds;
	}

	public void setCityIds (List<Long> cityIds) {
		this.cityIds = cityIds;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
}
