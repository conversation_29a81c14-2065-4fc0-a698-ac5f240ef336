package com.domain.complex;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单表查询对象
 *
 * @date 2025-07-24 23:22:31
 */
public class OrderQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 订单号
	 */
	private String code;
	/**
	 * 预支付交易会话标识
	 */
	private String prepayId;
	/**
	 * 预支付交易会话标识集合
	 */
	private List<String> prepayIds;
	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 用户id集合
	 */
	private List<Long> userIds;
	/**
	 * 用户类型
	 */
	private Long userCatalog;
	/**
	 * 订单类型(0:课程 1:商品)
	 */
	private String orderCatalog;
	/**
	 * 选择类型(0:自选 1:帮选)
	 */
	private String selectCatalog;
	/**
	 * 实付金额
	 */
	private BigDecimal amount;
	/**
	 * 退款金额
	 */
	private BigDecimal refundAmount;
	/**
	 * 团长总佣金
	 */
	private BigDecimal sumCommission;
	/**
	 * 团长佣金人
	 */
	private Long commissionUserId;
	/**
	 * 团长佣金人集合
	 */
	private List<Long> commissionUserIds;
	/**
	 * 销售老师佣金金额
	 */
	private BigDecimal sumSalesTeacherCommission;
	/**
	 * 销售老师ID
	 */
	private Long salesTeacherId;
	/**
	 * 销售老师ID集合
	 */
	private List<Long> salesTeacherIds;
	/**
	 * 支付状态
	 */
	private String stage;
	/**
	 * 支付状态集合
	 */
	private List<String> stages;
	/**
	 * 提货状态(0:已提货 1:未提货)
	 */
	private String operateStage;
	/**
	 * 配送方式(0:自提 1:派送)
	 */
	private String deliveryCatalog;
	/**
	 * 预约时间
	 */
	private Date reservationTime;
	/**
	 * 最小预约时间
	 */
	private Date minReservationTime;
	/**
	 * 最大预约时间
	 */
	private Date maxReservationTime;
	/**
	 * 用户地址编号
	 */
	private Long userAddressId;
	/**
	 * 用户地址编号集合
	 */
	private List<Long> userAddressIds;
	/**
	 * 地址-省份编号
	 */
	private Long addressProvinceId;
	/**
	 * 地址-城市编号
	 */
	private Long addressCityId;
	/**
	 * 地址-区县编号
	 */
	private Long addressDistrictId;
	/**
	 * 地址-详细地址
	 */
	private String addressDetailAddress;
	/**
	 * 地址-姓名
	 */
	private String addressName;
	/**
	 * 地址-手机号
	 */
	private String addressMobile;
	/**
	 * 支付时间
	 */
	private Date payTime;
	/**
	 * 最小支付时间
	 */
	private Date minPayTime;
	/**
	 * 最大支付时间
	 */
	private Date maxPayTime;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	/**
	 * 用户姓名
	 */
	private String userName;

	/**
	 * 用户手机号
	 */
	private String mobile;

	/**
	 * 商品类型
	 */
	private String goodsCatalog;

	/*
	 * 备注
	 */
	private String comment;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public String getCode (){
		return code;
	}

	public void setCode (String code) {
		this.code = code;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public List<String> getPrepayIds (){
		return prepayIds;
	}

	public void setPrepayIds (List<String> prepayIds) {
		this.prepayIds = prepayIds;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public List<Long> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Long> userIds) {
		this.userIds = userIds;
	}

	public Long getUserCatalog (){
		return userCatalog;
	}

	public void setUserCatalog (Long userCatalog) {
		this.userCatalog = userCatalog;
	}

	public String getOrderCatalog (){
		return orderCatalog;
	}

	public void setOrderCatalog (String orderCatalog) {
		this.orderCatalog = orderCatalog;
	}

	public String getSelectCatalog (){
		return selectCatalog;
	}

	public void setSelectCatalog (String selectCatalog) {
		this.selectCatalog = selectCatalog;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getRefundAmount (){
		return refundAmount;
	}

	public void setRefundAmount (BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public BigDecimal getSumCommission (){
		return sumCommission;
	}

	public void setSumCommission (BigDecimal sumCommission) {
		this.sumCommission = sumCommission;
	}

	public Long getCommissionUserId (){
		return commissionUserId;
	}

	public void setCommissionUserId (Long commissionUserId) {
		this.commissionUserId = commissionUserId;
	}

	public List<Long> getCommissionUserIds (){
		return commissionUserIds;
	}

	public void setCommissionUserIds (List<Long> commissionUserIds) {
		this.commissionUserIds = commissionUserIds;
	}

	public BigDecimal getSumSalesTeacherCommission (){
		return sumSalesTeacherCommission;
	}

	public void setSumSalesTeacherCommission (BigDecimal sumSalesTeacherCommission) {
		this.sumSalesTeacherCommission = sumSalesTeacherCommission;
	}

	public Long getSalesTeacherId (){
		return salesTeacherId;
	}

	public void setSalesTeacherId (Long salesTeacherId) {
		this.salesTeacherId = salesTeacherId;
	}

	public List<Long> getSalesTeacherIds (){
		return salesTeacherIds;
	}

	public void setSalesTeacherIds (List<Long> salesTeacherIds) {
		this.salesTeacherIds = salesTeacherIds;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public String getOperateStage (){
		return operateStage;
	}

	public void setOperateStage (String operateStage) {
		this.operateStage = operateStage;
	}

	public String getDeliveryCatalog (){
		return deliveryCatalog;
	}

	public void setDeliveryCatalog (String deliveryCatalog) {
		this.deliveryCatalog = deliveryCatalog;
	}

	public Date getReservationTime (){
		return reservationTime;
	}

	public void setReservationTime (Date reservationTime) {
		this.reservationTime = reservationTime;
	}

	public Date getMinReservationTime (){
		return minReservationTime;
	}

	public void setMinReservationTime (Date minReservationTime) {
		this.minReservationTime = minReservationTime;
	}

	public Date getMaxReservationTime (){
		return maxReservationTime;
	}

	public void setMaxReservationTime (Date maxReservationTime) {
		this.maxReservationTime = maxReservationTime;
	}

	public Long getUserAddressId (){
		return userAddressId;
	}

	public void setUserAddressId (Long userAddressId) {
		this.userAddressId = userAddressId;
	}

	public List<Long> getUserAddressIds (){
		return userAddressIds;
	}

	public void setUserAddressIds (List<Long> userAddressIds) {
		this.userAddressIds = userAddressIds;
	}

	public Date getPayTime (){
		return payTime;
	}

	public void setPayTime (Date payTime) {
		this.payTime = payTime;
	}

	public Date getMinPayTime (){
		return minPayTime;
	}

	public void setMinPayTime (Date minPayTime) {
		this.minPayTime = minPayTime;
	}

	public Date getMaxPayTime (){
		return maxPayTime;
	}

	public void setMaxPayTime (Date maxPayTime) {
		this.maxPayTime = maxPayTime;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public List<String> getStages() {
		return stages;
	}

	public void setStages(List<String> stages) {
		this.stages = stages;
	}

	public Long getAddressProvinceId() {
		return addressProvinceId;
	}

	public void setAddressProvinceId(Long addressProvinceId) {
		this.addressProvinceId = addressProvinceId;
	}

	public Long getAddressCityId() {
		return addressCityId;
	}

	public void setAddressCityId(Long addressCityId) {
		this.addressCityId = addressCityId;
	}

	public Long getAddressDistrictId() {
		return addressDistrictId;
	}

	public void setAddressDistrictId(Long addressDistrictId) {
		this.addressDistrictId = addressDistrictId;
	}

	public String getAddressDetailAddress() {
		return addressDetailAddress;
	}

	public void setAddressDetailAddress(String addressDetailAddress) {
		this.addressDetailAddress = addressDetailAddress;
	}

	public String getAddressName() {
		return addressName;
	}

	public void setAddressName(String addressName) {
		this.addressName = addressName;
	}

	public String getAddressMobile() {
		return addressMobile;
	}

	public void setAddressMobile(String addressMobile) {
		this.addressMobile = addressMobile;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getGoodsCatalog() {
		return goodsCatalog;
	}

	public void setGoodsCatalog(String goodsCatalog) {
		this.goodsCatalog = goodsCatalog;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}
}
