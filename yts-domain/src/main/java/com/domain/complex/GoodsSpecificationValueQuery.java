package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 商品规格值表查询对象
 *
 * @date 2025-07-27 21:26:17
 */
public class GoodsSpecificationValueQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Long> ids;
	/**
	 * 商品id
	 */
	private Long goodsId;
	/**
	 * 商品id集合
	 */
	private List<Long> goodsIds;
	/**
	 * 规格类型ID
	 */
	private Long goodsSpecificationTypeId;
	/**
	 * 规格类型ID集合
	 */
	private List<Long> goodsSpecificationTypeIds;
	/**
	 * 规格值
	 */
	private String name;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public Long getGoodsId (){
		return goodsId;
	}

	public void setGoodsId (Long goodsId) {
		this.goodsId = goodsId;
	}

	public List<Long> getGoodsIds (){
		return goodsIds;
	}

	public void setGoodsIds (List<Long> goodsIds) {
		this.goodsIds = goodsIds;
	}

	public Long getGoodsSpecificationTypeId (){
		return goodsSpecificationTypeId;
	}

	public void setGoodsSpecificationTypeId (Long goodsSpecificationTypeId) {
		this.goodsSpecificationTypeId = goodsSpecificationTypeId;
	}

	public List<Long> getGoodsSpecificationTypeIds (){
		return goodsSpecificationTypeIds;
	}

	public void setGoodsSpecificationTypeIds (List<Long> goodsSpecificationTypeIds) {
		this.goodsSpecificationTypeIds = goodsSpecificationTypeIds;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
}
