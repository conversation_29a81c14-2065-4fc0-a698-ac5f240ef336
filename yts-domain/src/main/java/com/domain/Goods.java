package com.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品表实体
 *
 * @date 2025-07-27 11:42:53
 */
public class Goods extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 商品名称
	 */
	private String name;
	/**
	 * 商品单位
	 */
	private String unit;
	/**
	 * 商品封面
	 */
	private String imgUrl;
	/**
	 * 商品类型
	 */
	private String catalog;
	/**
	 * 商品分类编号
	 */
	private Long goodsTypeId;
	/**
	 * 成本价
	 */
	private BigDecimal buyingPrice;
	/**
	 * 划线价
	 */
	private BigDecimal originalPrice;
	/**
	 * 售价
	 */
	private BigDecimal price;
	/**
	 * 毛利(售价-成本价)
	 */
	private BigDecimal grossProfit;
	/**
	 * 是否分佣(0:是 1:否)
	 */
	private String openCommission;
	/**
	 * 团长佣金比例(%)
	 */
	private BigDecimal commissionRatio;
	/**
	 * 销售老师佣金比例(%)
	 */
	private BigDecimal salesTeacherCommissionRatio;
	/**
	 * 商品详情图片
	 */
	private String detailImgUrl;
	/**
	 * 描述
	 */
	private String comment;
	/**
	 * 虚拟销量
	 */
	private Integer salesVolume;
	/**
	 * 真实销量
	 */
	private Integer realSalesVolume;
	/**
	 * 是否首页展示(0:展示 1:不展示)
	 */
	private String home;
	/**
	 * 上架状态(0:上架 1:下架)
	 */
	private String stage;

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getUnit (){
		return unit;
	}

	public void setUnit (String unit) {
		this.unit = unit;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public Long getGoodsTypeId (){
		return goodsTypeId;
	}

	public void setGoodsTypeId (Long goodsTypeId) {
		this.goodsTypeId = goodsTypeId;
	}

	public BigDecimal getBuyingPrice (){
		return buyingPrice;
	}

	public void setBuyingPrice (BigDecimal buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public BigDecimal getOriginalPrice (){
		return originalPrice;
	}

	public void setOriginalPrice (BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice (){
		return price;
	}

	public void setPrice (BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getGrossProfit (){
		return grossProfit;
	}

	public void setGrossProfit (BigDecimal grossProfit) {
		this.grossProfit = grossProfit;
	}

	public String getOpenCommission (){
		return openCommission;
	}

	public void setOpenCommission (String openCommission) {
		this.openCommission = openCommission;
	}

	public BigDecimal getCommissionRatio (){
		return commissionRatio;
	}

	public void setCommissionRatio (BigDecimal commissionRatio) {
		this.commissionRatio = commissionRatio;
	}

	public BigDecimal getSalesTeacherCommissionRatio (){
		return salesTeacherCommissionRatio;
	}

	public void setSalesTeacherCommissionRatio (BigDecimal salesTeacherCommissionRatio) {
		this.salesTeacherCommissionRatio = salesTeacherCommissionRatio;
	}

	public String getDetailImgUrl (){
		return detailImgUrl;
	}

	public void setDetailImgUrl (String detailImgUrl) {
		this.detailImgUrl = detailImgUrl;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public Integer getSalesVolume (){
		return salesVolume;
	}

	public void setSalesVolume (Integer salesVolume) {
		this.salesVolume = salesVolume;
	}

	public Integer getRealSalesVolume (){
		return realSalesVolume;
	}

	public void setRealSalesVolume (Integer realSalesVolume) {
		this.realSalesVolume = realSalesVolume;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public String getHome() {
		return home;
	}

	public void setHome(String home) {
		this.home = home;
	}
}
