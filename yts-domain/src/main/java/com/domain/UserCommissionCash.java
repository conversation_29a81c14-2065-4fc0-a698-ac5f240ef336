package com.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户佣金提现表实体
 *
 * @date 2025-07-19 10:58:27
 */
public class UserCommissionCash extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 用户编号
	 */
	private Long userId;
	/**
	 * 总佣金
	 */
	private BigDecimal sumCommission;
	/**
	 * 订单数量
	 */
	private Integer orderSize;
	/**
	 * 操作人编号
	 */
	private Long optSysUserId;

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public BigDecimal getSumCommission (){
		return sumCommission;
	}

	public void setSumCommission (BigDecimal sumCommission) {
		this.sumCommission = sumCommission;
	}

	public Integer getOrderSize (){
		return orderSize;
	}

	public void setOrderSize (Integer orderSize) {
		this.orderSize = orderSize;
	}

	public Long getOptSysUserId (){
		return optSysUserId;
	}

	public void setOptSysUserId (Long optSysUserId) {
		this.optSysUserId = optSysUserId;
	}

}
