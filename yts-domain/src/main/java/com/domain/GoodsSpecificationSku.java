package com.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品规格sku表实体
 *
 * @date 2025-07-27 21:26:17
 */
public class GoodsSpecificationSku extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 商品id
	 */
	private Long goodsId;
	/**
	 * SKU属性
	 */
	private String specValues;
	/**
	 * 商品封面
	 */
	private String imgUrl;
	/**
	 * 成本价
	 */
	private BigDecimal buyingPrice;
	/**
	 * 划线价
	 */
	private BigDecimal originalPrice;
	/**
	 * 售价
	 */
	private BigDecimal price;
	/**
	 * 毛利(售价-成本价)
	 */
	private BigDecimal grossProfit;
	/**
	 * 库存数量
	 */
	private Integer stock;

	/**
	 * 产品名称
	 */
	private String productName;

	public Long getGoodsId (){
		return goodsId;
	}

	public void setGoodsId (Long goodsId) {
		this.goodsId = goodsId;
	}

	public String getSpecValues (){
		return specValues;
	}

	public void setSpecValues (String specValues) {
		this.specValues = specValues;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public BigDecimal getBuyingPrice (){
		return buyingPrice;
	}

	public void setBuyingPrice (BigDecimal buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public BigDecimal getOriginalPrice (){
		return originalPrice;
	}

	public void setOriginalPrice (BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice (){
		return price;
	}

	public void setPrice (BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getGrossProfit() {
		return grossProfit;
	}

	public void setGrossProfit(BigDecimal grossProfit) {
		this.grossProfit = grossProfit;
	}

	public Integer getStock (){
		return stock;
	}

	public void setStock (Integer stock) {
		this.stock = stock;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}
}
