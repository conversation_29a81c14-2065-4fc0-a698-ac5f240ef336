package com.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户佣金表实体
 *
 * @date 2025-07-27 11:32:54
 */
public class UserCommission extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 用户编号
	 */
	private Long userId;
	/**
	 * 可用余额
	 */
	private BigDecimal amount;
	/**
	 * 总余额
	 */
	private BigDecimal totalAmount;

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getTotalAmount (){
		return totalAmount;
	}

	public void setTotalAmount (BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

}
