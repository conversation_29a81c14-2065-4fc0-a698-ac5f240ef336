package com.domain;

import java.util.Date;

/**
 * 轮播图表实体
 *
 * @date 2025-07-19 10:58:28
 */
public class Banner extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 图片地址
	 */
	private String imgUrl;
	/**
	 * 是否跳转(0:跳转 1:不跳转)
	 */
	private String jump;
	/**
	 * 跳转类型
	 */
	private String jumpCatalog;
	/**
	 * 跳转编号
	 */
	private Long jumpId;
	/**
	 * 上架状态(0:上架 1:下架)
	 */
	private String stage;
	/**
	 * 排序字段
	 */
	private Integer sort;

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getJump (){
		return jump;
	}

	public void setJump (String jump) {
		this.jump = jump;
	}

	public String getJumpCatalog (){
		return jumpCatalog;
	}

	public void setJumpCatalog (String jumpCatalog) {
		this.jumpCatalog = jumpCatalog;
	}

	public Long getJumpId (){
		return jumpId;
	}

	public void setJumpId (Long jumpId) {
		this.jumpId = jumpId;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public Integer getSort (){
		return sort;
	}

	public void setSort (Integer sort) {
		this.sort = sort;
	}

}
