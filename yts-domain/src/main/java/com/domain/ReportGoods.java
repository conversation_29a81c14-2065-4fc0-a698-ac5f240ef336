package com.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 统计商品表实体
 *
 * @date 2025-07-20 16:36:02
 */
public class ReportGoods extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 日期
	 */
	private Date date;
	/**
	 * 商品编号
	 */
	private Long goodsId;
	/**
	 * 浏览数量
	 */
	private Long browseNum;
	/**
	 * 支付数量
	 */
	private Long payNum;
	/**
	 * 支付金额
	 */
	private BigDecimal payAmount;
	/**
	 * 退款数量
	 */
	private Long refundNum;
	/**
	 * 退款金额
	 */
	private BigDecimal refundAmount;

	public Date getDate (){
		return date;
	}

	public void setDate (Date date) {
		this.date = date;
	}

	public Long getGoodsId (){
		return goodsId;
	}

	public void setGoodsId (Long goodsId) {
		this.goodsId = goodsId;
	}

	public Long getBrowseNum (){
		return browseNum;
	}

	public void setBrowseNum (Long browseNum) {
		this.browseNum = browseNum;
	}

	public Long getPayNum (){
		return payNum;
	}

	public void setPayNum (Long payNum) {
		this.payNum = payNum;
	}

	public BigDecimal getPayAmount (){
		return payAmount;
	}

	public void setPayAmount (BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

	public Long getRefundNum (){
		return refundNum;
	}

	public void setRefundNum (Long refundNum) {
		this.refundNum = refundNum;
	}

	public BigDecimal getRefundAmount (){
		return refundAmount;
	}

	public void setRefundAmount (BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

}
