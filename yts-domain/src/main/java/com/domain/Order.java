package com.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单表实体
 *
 * @date 2025-07-24 23:22:31
 */
public class Order extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 订单号
	 */
	private String code;
	/**
	 * 预支付交易会话标识
	 */
	private String prepayId;
	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 用户类型
	 */
	private String userCatalog;
	/**
	 * 订单类型(0:课程 1:商品)
	 */
	private String orderCatalog;
	/**
	 * 选择类型(0:自选 1:帮选)
	 */
	private String selectCatalog;
	/**
	 * 实付金额
	 */
	private BigDecimal amount;
	/**
	 * 退款金额
	 */
	private BigDecimal refundAmount;
	/**
	 * 团长总佣金
	 */
	private BigDecimal sumCommission;
	/**
	 * 团长佣金人
	 */
	private Long commissionUserId;
	/**
	 * 销售老师佣金金额
	 */
	private BigDecimal sumSalesTeacherCommission;
	/**
	 * 销售老师ID
	 */
	private Long salesTeacherId;
	/**
	 * 支付状态
	 */
	private String stage;
	/**
	 * 提货状态(0:已提货 1:未提货)
	 */
	private String operateStage;
	/**
	 * 配送方式(0:自提 1:派送)
	 */
	private String deliveryCatalog;
	/**
	 * 预约时间
	 */
	private Date reservationTime;
	/**
	 * 用户地址编号
	 */
	private Long userAddressId;
	/**
	 * 地址-省份编号
	 */
	private Long addressProvinceId;
	/**
	 * 地址-城市编号
	 */
	private Long addressCityId;
	/**
	 * 地址-区县编号
	 */
	private Long addressDistrictId;
	/**
	 * 地址-详细地址
	 */
	private String addressDetailAddress;
	/**
	 * 地址-姓名
	 */
	private String addressName;
	/**
	 * 地址-手机号
	 */
	private String addressMobile;
	/**
	 * 支付时间
	 */
	private Date payTime;
	/*
	 * 备注
	 */
	private String comment;

	public String getCode (){
		return code;
	}

	public void setCode (String code) {
		this.code = code;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public String getUserCatalog (){
		return userCatalog;
	}

	public void setUserCatalog (String userCatalog) {
		this.userCatalog = userCatalog;
	}

	public String getOrderCatalog (){
		return orderCatalog;
	}

	public void setOrderCatalog (String orderCatalog) {
		this.orderCatalog = orderCatalog;
	}

	public String getSelectCatalog (){
		return selectCatalog;
	}

	public void setSelectCatalog (String selectCatalog) {
		this.selectCatalog = selectCatalog;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getRefundAmount (){
		return refundAmount;
	}

	public void setRefundAmount (BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public BigDecimal getSumCommission (){
		return sumCommission;
	}

	public void setSumCommission (BigDecimal sumCommission) {
		this.sumCommission = sumCommission;
	}

	public Long getCommissionUserId (){
		return commissionUserId;
	}

	public void setCommissionUserId (Long commissionUserId) {
		this.commissionUserId = commissionUserId;
	}

	public BigDecimal getSumSalesTeacherCommission (){
		return sumSalesTeacherCommission;
	}

	public void setSumSalesTeacherCommission (BigDecimal sumSalesTeacherCommission) {
		this.sumSalesTeacherCommission = sumSalesTeacherCommission;
	}

	public Long getSalesTeacherId (){
		return salesTeacherId;
	}

	public void setSalesTeacherId (Long salesTeacherId) {
		this.salesTeacherId = salesTeacherId;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

	public String getOperateStage (){
		return operateStage;
	}

	public void setOperateStage (String operateStage) {
		this.operateStage = operateStage;
	}

	public String getDeliveryCatalog (){
		return deliveryCatalog;
	}

	public void setDeliveryCatalog (String deliveryCatalog) {
		this.deliveryCatalog = deliveryCatalog;
	}

	public Date getReservationTime (){
		return reservationTime;
	}

	public void setReservationTime (Date reservationTime) {
		this.reservationTime = reservationTime;
	}

	public Long getUserAddressId (){
		return userAddressId;
	}

	public void setUserAddressId (Long userAddressId) {
		this.userAddressId = userAddressId;
	}

	public Date getPayTime (){
		return payTime;
	}

	public void setPayTime (Date payTime) {
		this.payTime = payTime;
	}

	public Long getAddressProvinceId() {
		return addressProvinceId;
	}

	public void setAddressProvinceId(Long addressProvinceId) {
		this.addressProvinceId = addressProvinceId;
	}

	public Long getAddressCityId() {
		return addressCityId;
	}

	public void setAddressCityId(Long addressCityId) {
		this.addressCityId = addressCityId;
	}

	public Long getAddressDistrictId() {
		return addressDistrictId;
	}

	public void setAddressDistrictId(Long addressDistrictId) {
		this.addressDistrictId = addressDistrictId;
	}

	public String getAddressDetailAddress() {
		return addressDetailAddress;
	}

	public void setAddressDetailAddress(String addressDetailAddress) {
		this.addressDetailAddress = addressDetailAddress;
	}

	public String getAddressName() {
		return addressName;
	}

	public void setAddressName(String addressName) {
		this.addressName = addressName;
	}

	public String getAddressMobile() {
		return addressMobile;
	}

	public void setAddressMobile(String addressMobile) {
		this.addressMobile = addressMobile;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}
}
