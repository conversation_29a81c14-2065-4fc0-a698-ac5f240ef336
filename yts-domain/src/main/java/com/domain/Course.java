package com.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 课程表实体
 *
 * @date 2025-07-19 10:58:28
 */
public class Course extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 课程标题
	 */
	private String title;
	/**
	 * 课程封面
	 */
	private String imgUrl;
	/**
	 * 课程时长
	 */
	private Integer length;
	/**
	 * 课程详情图片
	 */
	private String detailImgUrl;
	/**
	 * 原价
	 */
	private BigDecimal originalPrice;
	/**
	 * 价格
	 */
	private BigDecimal price;
	/**
	 * 描述
	 */
	private String comment;
	/**
	 * 虚拟销量
	 */
	private Integer salesVolume;
	/**
	 * 是否首页展示(0:展示 1:不展示)
	 */
	private String home;
	/**
	 * 排序字段
	 */
	private Integer sort;
	/**
	 * 上架状态(0:上架 1:下架)
	 */
	private String stage;

	public String getTitle (){
		return title;
	}

	public void setTitle (String title) {
		this.title = title;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public Integer getLength (){
		return length;
	}

	public void setLength (Integer length) {
		this.length = length;
	}

	public String getDetailImgUrl (){
		return detailImgUrl;
	}

	public void setDetailImgUrl (String detailImgUrl) {
		this.detailImgUrl = detailImgUrl;
	}

	public BigDecimal getOriginalPrice (){
		return originalPrice;
	}

	public void setOriginalPrice (BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPrice (){
		return price;
	}

	public void setPrice (BigDecimal price) {
		this.price = price;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public Integer getSalesVolume (){
		return salesVolume;
	}

	public void setSalesVolume (Integer salesVolume) {
		this.salesVolume = salesVolume;
	}

	public String getHome (){
		return home;
	}

	public void setHome (String home) {
		this.home = home;
	}

	public Integer getSort (){
		return sort;
	}

	public void setSort (Integer sort) {
		this.sort = sort;
	}

	public String getStage (){
		return stage;
	}

	public void setStage (String stage) {
		this.stage = stage;
	}

}
