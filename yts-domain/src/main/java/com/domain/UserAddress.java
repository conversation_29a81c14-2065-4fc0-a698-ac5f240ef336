package com.domain;

import java.util.Date;

/**
 * 用户地址表实体
 *
 * @date 2025-07-23 22:58:35
 */
public class UserAddress extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 用户编号
	 */
	private Long userId;
	/**
	 * 省份编号
	 */
	private Long provinceId;
	/**
	 * 城市编号
	 */
	private Long cityId;
	/**
	 * 区县编号
	 */
	private Long districtId;
	/**
	 * 详细地址
	 */
	private String detailAddress;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 是否默认地址(0:是 1:否)
	 */
	private String defaultAddress;

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public Long getProvinceId (){
		return provinceId;
	}

	public void setProvinceId (Long provinceId) {
		this.provinceId = provinceId;
	}

	public Long getCityId (){
		return cityId;
	}

	public void setCityId (Long cityId) {
		this.cityId = cityId;
	}

	public Long getDistrictId (){
		return districtId;
	}

	public void setDistrictId (Long districtId) {
		this.districtId = districtId;
	}

	public String getDetailAddress (){
		return detailAddress;
	}

	public void setDetailAddress (String detailAddress) {
		this.detailAddress = detailAddress;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getDefaultAddress() {
		return defaultAddress;
	}

	public void setDefaultAddress(String defaultAddress) {
		this.defaultAddress = defaultAddress;
	}
}
