package com.domain;

import java.util.Date;

/**
 * 用户表实体
 *
 * @date 2025-07-24 23:22:31
 */
public class User extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 用户统一标识(微信开放平台)
	 */
	private String unionId;
	/**
	 * 用户统一标识(服务号)
	 */
	private String openId;
	/**
	 * 邀请码
	 */
	private Long invitationCode;
	/**
	 * 二维码
	 */
	private String qrCode;
	/**
	 * 用户类型(0:普通用户 1:常驻用户)
	 */
	private String catalog;
	/**
	 * 名字
	 */
	private String name;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 头像
	 */
	private String avatar;
	/**
	 * 性别(0:男 1:女)
	 */
	private String gender;
	/**
	 * 最后登录时间
	 */
	private Date lastLoginTime;

	public String getUnionId (){
		return unionId;
	}

	public void setUnionId (String unionId) {
		this.unionId = unionId;
	}

	public Long getInvitationCode (){
		return invitationCode;
	}

	public void setInvitationCode (Long invitationCode) {
		this.invitationCode = invitationCode;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getAvatar (){
		return avatar;
	}

	public void setAvatar (String avatar) {
		this.avatar = avatar;
	}

	public String getGender (){
		return gender;
	}

	public void setGender (String gender) {
		this.gender = gender;
	}

	public Date getLastLoginTime (){
		return lastLoginTime;
	}

	public void setLastLoginTime (Date lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	public String getQrCode() {
		return qrCode;
	}

	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}
}
