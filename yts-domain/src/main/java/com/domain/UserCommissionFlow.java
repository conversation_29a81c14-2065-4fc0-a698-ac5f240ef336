package com.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户佣金明细表实体
 *
 * @date 2025-07-27 11:32:54
 */
public class UserCommissionFlow extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	private String flowCode;
	/**
	 * 用户编号
	 */
	private Long userId;
	/**
	 * 订单ID
	 */
	private Long orderId;
	/**
	 * 订单号
	 */
	private String orderCode;
	/**
	 * 预支付交易会话标识
	 */
	private String prepayId;
	/**
	 * 交易时间
	 */
	private Date transactionTime;
	/**
	 * 流转金额
	 */
	private BigDecimal flowAmount;
	/**
	 * 总金额
	 */
	private BigDecimal amount;
	/**
	 * 分类(1000:分佣流水 1001:提现流水)
	 */
	private String catalog;
	/**
	 * 支付方式(1000:微信小程序支付)
	 */
	private String payChannel;
	/**
	 * 收支出(0:收入 1:支出)
	 */
	private String income;
	/**
	 * 标签
	 */
	private String label;
	/**
	 * 备注
	 */
	private String comment;

	public String getFlowCode (){
		return flowCode;
	}

	public void setFlowCode (String flowCode) {
		this.flowCode = flowCode;
	}

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public Long getOrderId (){
		return orderId;
	}

	public void setOrderId (Long orderId) {
		this.orderId = orderId;
	}

	public String getOrderCode (){
		return orderCode;
	}

	public void setOrderCode (String orderCode) {
		this.orderCode = orderCode;
	}

	public String getPrepayId (){
		return prepayId;
	}

	public void setPrepayId (String prepayId) {
		this.prepayId = prepayId;
	}

	public Date getTransactionTime (){
		return transactionTime;
	}

	public void setTransactionTime (Date transactionTime) {
		this.transactionTime = transactionTime;
	}

	public BigDecimal getAmount (){
		return amount;
	}

	public void setAmount (BigDecimal amount) {
		this.amount = amount;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public String getPayChannel (){
		return payChannel;
	}

	public void setPayChannel (String payChannel) {
		this.payChannel = payChannel;
	}

	public String getIncome (){
		return income;
	}

	public void setIncome (String income) {
		this.income = income;
	}


	public String getLabel (){
		return label;
	}

	public void setLabel (String label) {
		this.label = label;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public BigDecimal getFlowAmount() {
		return flowAmount;
	}

	public void setFlowAmount(BigDecimal flowAmount) {
		this.flowAmount = flowAmount;
	}
}
