package com.domain;

import java.util.Date;

/**
 * 微信应用表实体
 *
 * @date 2025-07-16 21:29:32
 */
public class WechatApp extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 应用ID
	 */
	private String appid;
	/**
	 * 应用密钥
	 */
	private String secret;
	/**
	 * 应用名称
	 */
	private String name;
	/**
	 * 访问凭证
	 */
	private String accessToken;
	/**
	 * 凭证刷新日期
	 */
	private Date accessTokenDatetime;

	public String getAppid (){
		return appid;
	}

	public void setAppid (String appid) {
		this.appid = appid;
	}

	public String getSecret (){
		return secret;
	}

	public void setSecret (String secret) {
		this.secret = secret;
	}

	public String getName (){
		return name;
	}

	public void setName (String name) {
		this.name = name;
	}

	public String getAccessToken (){
		return accessToken;
	}

	public void setAccessToken (String accessToken) {
		this.accessToken = accessToken;
	}

	public Date getAccessTokenDatetime (){
		return accessTokenDatetime;
	}

	public void setAccessTokenDatetime (Date accessTokenDatetime) {
		this.accessTokenDatetime = accessTokenDatetime;
	}

}
