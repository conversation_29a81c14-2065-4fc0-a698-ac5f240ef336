package com.domain;

import java.util.Date;

/**
 * 用户团队表实体
 *
 * @date 2025-07-19 10:58:27
 */
public class UserTeam extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 队长编号
	 */
	private Long captainUserId;
	/**
	 * 队员编号
	 */
	private Long teamMemberUserId;
	/**
	 * 绑定类型
	 */
	private String bindingCatalog;

	public Long getCaptainUserId (){
		return captainUserId;
	}

	public void setCaptainUserId (Long captainUserId) {
		this.captainUserId = captainUserId;
	}

	public Long getTeamMemberUserId (){
		return teamMemberUserId;
	}

	public void setTeamMemberUserId (Long teamMemberUserId) {
		this.teamMemberUserId = teamMemberUserId;
	}

	public String getBindingCatalog() {
		return bindingCatalog;
	}

	public void setBindingCatalog(String bindingCatalog) {
		this.bindingCatalog = bindingCatalog;
	}
}
