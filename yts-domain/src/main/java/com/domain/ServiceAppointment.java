package com.domain;

import java.util.Date;

/**
 * 服务预约表实体
 *
 * @date 2025-07-19 10:58:27
 */
public class ServiceAppointment extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 服务id
	 */
	private Long serviceId;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 是否联系(0:已联系 1:未联系)
	 */
	private String contact;
	/**
	 * 描述
	 */
	private String comment;

	public Long getUserId (){
		return userId;
	}

	public void setUserId (Long userId) {
		this.userId = userId;
	}

	public Long getServiceId (){
		return serviceId;
	}

	public void setServiceId (Long serviceId) {
		this.serviceId = serviceId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMobile (){
		return mobile;
	}

	public void setMobile (String mobile) {
		this.mobile = mobile;
	}

	public String getRemark (){
		return remark;
	}

	public void setRemark (String remark) {
		this.remark = remark;
	}

	public String getContact (){
		return contact;
	}

	public void setContact (String contact) {
		this.contact = contact;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

}
